/**
 * IYM Backend OpenAPI definition
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { EvrakGelenKurumlarResponse } from './evrakGelenKurumlarResponse';
import { ResponseIDYeniKararResponseException } from './responseIDYeniKararResponseException';


export interface ResponseEvrakGelenKurumlarResponse { 
    resultCode?: ResponseEvrakGelenKurumlarResponse.ResultCodeEnum;
    resultDetails?: string;
    exception?: ResponseIDYeniKararResponseException;
    result?: EvrakGelenKurumlarResponse;
    success?: boolean;
}
export namespace ResponseEvrakGelenKurumlarResponse {
    export const ResultCodeEnum = {
        Success: 'SUCCESS',
        Failed: 'FAILED',
        Rejected: 'REJECTED'
    } as const;
    export type ResultCodeEnum = typeof ResultCodeEnum[keyof typeof ResultCodeEnum];
}


