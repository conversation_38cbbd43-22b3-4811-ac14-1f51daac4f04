package iym.common.util.db;

import iym.common.enums.KullaniciKurum;
import jakarta.persistence.AttributeConverter;

public class KullaniciKurumConverter implements AttributeConverter<KullaniciKurum, String> {

    @Override
    public String convertToDatabaseColumn(KullaniciKurum kurum) {
        if (kurum == null) {
            return null;
        } else {
            return kurum.getValue();
        }
    }

    @Override
    public KullaniciKurum convertToEntityAttribute(String kurum) {
        if (kurum == null) {
            return null;
        } else {
            return KullaniciKurum.convert(kurum).orElse(null);
        }
    }
}
