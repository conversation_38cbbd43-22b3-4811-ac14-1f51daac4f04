package spring.common;

import iym.common.config.JacksonConfig;
import iym.common.util.AppContextProvider;
import iym.common.util.EnvironmentUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Configuration
@Import(JacksonConfig.class)
@Slf4j
public class CommonLoader {

    public CommonLoader() {
        log.info("BaseLoader initializing...");
    }

    @Autowired
    private ApplicationContext context;

    @Bean
    public AppContextProvider getAppContextProvider(){
        return new AppContextProvider(context);
    }

    @Bean
    public EnvironmentUtil environmentUtil() {
        return new EnvironmentUtil();
    }
}
