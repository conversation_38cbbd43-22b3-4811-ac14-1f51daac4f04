/**
 * IYM Backend OpenAPI definition
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { ResponseIDYeniKararResponseException } from './responseIDYeniKararResponseException';
import { SucTipleriResponse } from './sucTipleriResponse';


export interface ResponseSucTipleriResponse { 
    resultCode?: ResponseSucTipleriResponse.ResultCodeEnum;
    resultDetails?: string;
    exception?: ResponseIDYeniKararResponseException;
    result?: SucTipleriResponse;
    success?: boolean;
}
export namespace ResponseSucTipleriResponse {
    export const ResultCodeEnum = {
        Success: 'SUCCESS',
        Failed: 'FAILED',
        Rejected: 'REJECTED'
    } as const;
    export type ResultCodeEnum = typeof ResultCodeEnum[keyof typeof ResultCodeEnum];
}


