# Mapper Sınıfları için Birim Test Yazım Kılavuzu

## G<PERSON>ş

Bu dokümantasyon, projede bulunan mapper sınıfları için birim testlerin nasıl yazılacağını açıklamaktadır. Map<PERSON> sın<PERSON>, veri dönüşüm işlemlerini gerçekleştiren ve farklı veri modelleri arasında dönüşüm sağlayan sınıflardır. Bu sınıfların doğru çalıştığından emin olmak için kapsamlı birim testler yazılmalıdır.

## Mapper Sınıfları Nedir?

Mapper sınıfları, genellikle aşağıdaki dönüşümleri gerçekleştirir:

1. Entity -> DTO dönüşümü
2. DTO -> Entity dönüşümü
3. Entity -> View dönüşümü
4. Entity listelerinin dönüşümü
5. <PERSON>ti<PERSON> güncelleme işlemleri

## Test Edilmesi Gereken Durumlar

Mapper sınıfları için birim testler yazarken aşağıdaki durumlar test edilmelidir:

1. **Null Kontrolü**: Mapper metoduna null değer gönderildiğinde beklenen sonucun dönmesi
2. **Temel Dönüşüm**: Entity'den DTO'ya veya DTO'dan Entity'ye dönüşümün doğru yapılması
3. **Liste Dönüşümü**: Entity listesinden DTO listesine dönüşümün doğru yapılması
4. **Güncelleme İşlemi**: Entity güncelleme işleminin doğru yapılması

## Test Sınıfı Yapısı

Bir mapper test sınıfı genellikle aşağıdaki yapıda olmalıdır:

```java
class MapperTest {

    private Mapper mapper;
    private Entity entity;
    private DTO dto;

    @BeforeEach
    void setUp() {
        // Mapper ve test verilerini hazırla
    }

    @Test
    void toDto_shouldMapEntityToDto() {
        // Entity -> DTO dönüşümünü test et
    }

    @Test
    void toEntity_shouldMapDtoToEntity() {
        // DTO -> Entity dönüşümünü test et
    }

    @Test
    void toDto_shouldReturnNull_whenEntityIsNull() {
        // Null entity durumunu test et
    }

    // Diğer test metodları
}
```

## Örnek Test Sınıfları

Projede bulunan mapper sınıfları için örnek test sınıfları:

### MahkemeKararTalepMapper Test Sınıfı

`MahkemeKararTalepMapper` sınıfı için test sınıfı örneği:

```java
class MahkemeKararTalepMapperTest {

    private MahkemeKararTalepMapper mahkemeKararTalepMapper;
    private MahkemeKararTalep mahkemeKararTalep;
    private MahkemeKararTalepDTO mahkemeKararTalepDTO;
    private LocalDateTime testDate;

    @BeforeEach
    void setUp() {
        mahkemeKararTalepMapper = new MahkemeKararTalepMapper();
        testDate = LocalDateTime.now();

        // Test verilerini hazırla
        mahkemeKararTalep = MahkemeKararTalep.builder()
                .id(1L)
                .evrakId(100L)
                // Diğer alanlar
                .build();

        mahkemeKararTalepDTO = MahkemeKararTalepDTO.builder()
                .id(1L)
                .evrakId(100L)
                // Diğer alanlar
                .build();
    }

    @Test
    void toDto_shouldMapEntityToDto() {
        // When
        MahkemeKararTalepDTO result = mahkemeKararTalepMapper.toDto(mahkemeKararTalep);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(mahkemeKararTalep.getId());
        assertThat(result.getEvrakId()).isEqualTo(mahkemeKararTalep.getEvrakId());
        // Diğer alanların kontrolü
    }

    // Diğer test metodları
}
```

### MahkemeKararTalepSorguMapper Test Sınıfı

`MahkemeKararTalepSorguMapper` sınıfı için test sınıfı örneği:

```java
class MahkemeKararTalepSorguMapperTest {

    private MahkemeKararTalepSorguMapper mahkemeKararTalepSorguMapper;
    private MahkemeKararTalepSorguInfo mahkemeKararTalepSorguInfo;
    private LocalDateTime testDate;

    @BeforeEach
    void setUp() {
        mahkemeKararTalepSorguMapper = new MahkemeKararTalepSorguMapper();
        testDate = LocalDateTime.now();

        // Test verilerini hazırla
        mahkemeKararTalepSorguInfo = MahkemeKararTalepSorguInfo.builder()
                .id(1L)
                .evrakId(100L)
                // Diğer alanlar
                .build();
    }

    @Test
    void toKararTalepViewInfo_shouldMapEntityToDto() {
        // When
        MahkemeKararTalepIslenecekView result = mahkemeKararTalepSorguMapper.toKararTalepViewInfo(mahkemeKararTalepSorguInfo);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getMahkemeKararTalepId()).isEqualTo(mahkemeKararTalepSorguInfo.getId());
        assertThat(result.getEvrakId()).isEqualTo(mahkemeKararTalepSorguInfo.getEvrakId());
        // Diğer alanların kontrolü
    }

    // Diğer test metodları
}
```

## Test Yazarken Dikkat Edilmesi Gerekenler

1. **Tüm Alanların Kontrolü**: Dönüşüm sonucunda tüm alanların doğru şekilde dönüştürüldüğünden emin olun.
2. **Null Kontrolü**: Mapper metodlarının null değerlerle çağrıldığında nasıl davrandığını test edin.
3. **Özel Dönüşümler**: Bazı mapper metodları özel dönüşümler içerebilir (örneğin, string birleştirme, tarih formatı değiştirme). Bu özel dönüşümlerin doğru çalıştığından emin olun.
4. **Liste Dönüşümleri**: Liste dönüşümlerinde boş liste ve null liste durumlarını test edin.

## Yeni Mapper Sınıfları için Test Yazma Adımları

1. Mapper sınıfını inceleyin ve hangi metodların test edilmesi gerektiğini belirleyin.
2. Test sınıfını oluşturun ve gerekli test verilerini hazırlayın.
3. Her bir mapper metodu için en az bir test metodu yazın.
4. Null değer kontrolü için ayrı test metodları ekleyin.
5. Özel dönüşümler varsa, bunlar için özel test metodları ekleyin.

## Sonuç

Mapper sınıfları, veri dönüşüm işlemlerini gerçekleştiren önemli bileşenlerdir. Bu sınıfların doğru çalıştığından emin olmak için kapsamlı birim testler yazılmalıdır. Bu dokümantasyon, mapper sınıfları için birim test yazarken izlenmesi gereken adımları ve dikkat edilmesi gereken noktaları açıklamaktadır.