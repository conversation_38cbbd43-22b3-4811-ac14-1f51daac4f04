package iym.db.jpa.service.impl;

import iym.common.model.entity.iym.talep.MahkemeKararTalep;
import iym.db.jpa.dao.mktalep.MahkemeKararTalepRepo;
import iym.db.jpa.service.impl.mktalep.DbMahkemeKararTalepServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class DbMahkemeKararTalepServiceImplTest {

    @Mock
    private MahkemeKararTalepRepo mahkemeKararTalepRepo;

    @InjectMocks
    private DbMahkemeKararTalepServiceImpl dbMahkemeKararTalepService;

    private MahkemeKararTalep mahkemeKararTalep;
    private LocalDateTime testDate;

    @BeforeEach
    void setUp() {
        testDate = LocalDateTime.now();
        mahkemeKararTalep = MahkemeKararTalep.builder()
                .id(1L)
                .evrakId(100L)
                .kullaniciId(200L)
                .kayitTarihi(testDate)
                .durum("AKTIF")
                .hukukBirim("SULH CEZA")
                .kararTip("300")
                .mahKararBasTar(testDate)
                .mahKararBitisTar(testDate)
                .mahkemeAdi("ANKARA 1. SULH CEZA HAKİMLİĞİ")
                .mahkemeKararNo("2023/123")
                .mahkemeIlIlceKodu("0600")
                .aciklama("Test açıklama")
                .hakimSicilNo("12345")
                .sorusturmaNo("2023/456")
                .gercekMahId(300L)
                .mahkemeKodu("SC01")
                .build();
    }

    @Test
    void findByEvrakId_shouldReturnListOfMahkemeKararTalep() {
        // Given
        Long evrakId = 100L;
        List<MahkemeKararTalep> expectedList = Arrays.asList(mahkemeKararTalep);
        when(mahkemeKararTalepRepo.findByEvrakId(evrakId)).thenReturn(expectedList);

        // When
        List<MahkemeKararTalep> result = dbMahkemeKararTalepService.findByEvrakId(evrakId);

        // Then
        assertThat(result).isEqualTo(expectedList);
        verify(mahkemeKararTalepRepo).findByEvrakId(evrakId);
    }



}
