package iym.db.jpa.service.impl.mkislem;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.mkislem.MahkemeHedeflerAidiyatIslem;
import iym.common.model.entity.iym.talep.MahkemeHedeflerAidiyatTalep;
import iym.common.service.db.mkislem.DbMahkemeHedeflerAidiyatIslemService;
import iym.common.service.db.mktalep.DbMahkemeHedeflerAidiyatTalepService;
import iym.db.jpa.dao.mkislem.MahkemeHedeflerAidiyatIslemRepo;
import iym.db.jpa.dao.mktalep.MahkemeHedeflerAidiyatTalepRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Service implementation for MahkemeHedeflerAidiyatIslem entity
 */
@Service
public class DbMahkemeHedeflerAidiyatIslemServiceImpl extends GenericDbServiceImpl<MahkemeHedeflerAidiyatIslem, Long> implements DbMahkemeHedeflerAidiyatIslemService {

    private final MahkemeHedeflerAidiyatIslemRepo mahkemeHedeflerAidiyatIslemRepo;

    @Autowired
    public DbMahkemeHedeflerAidiyatIslemServiceImpl(MahkemeHedeflerAidiyatIslemRepo repository) {
        super(repository);
        this.mahkemeHedeflerAidiyatIslemRepo = repository;
    }


}
