<div class="p-4">
  <!-- Başlık -->
  <div class="flex justify-between items-center mb-4">
    <h2 class="text-2xl font-bold text-gray-800">
      <i class="pi pi-search mr-2"></i>
      <PERSON><PERSON><PERSON>
    </h2>
    <div class="flex gap-2">
      <p-button
        icon="pi pi-file-excel"
        label="Excel"
        severity="success"
        size="small"
        (onClick)="excelAktar()"
        [disabled]="evraklar.length === 0">
      </p-button>
      <p-button
        icon="pi pi-file-pdf"
        label="PDF"
        severity="danger"
        size="small"
        (onClick)="pdfAktar()"
        [disabled]="evraklar.length === 0">
      </p-button>
    </div>
  </div>

  <!-- <PERSON><PERSON> Filtreleri -->
  <p-card header="Arama Filtreleri" class="mb-4">
    <!-- <PERSON><PERSON><PERSON> -->
    <div class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <i class="pi pi-info-circle text-blue-400"></i>
        </div>
        <div class="ml-3">
          <p class="text-sm text-blue-700">
            <strong>Yeni Özellik:</strong> Mahkeme Adı, Kurum Kodu, Kurum Adı ve Evrak Konusu alanları eklendi. 
            Bu alanlar henüz arama filtrelemesi yapmıyor ancak sonuçlarda görüntüleniyor.
          </p>
        </div>
      </div>
    </div>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">

      <!-- Soruşturma No -->
      <div class="flex flex-col">
        <label for="sorusturmaNo" class="text-sm font-medium text-gray-700 mb-1">
          Soruşturma No
        </label>
        <input
          pInputText
          id="sorusturmaNo"
          [(ngModel)]="aramaFiltresi.sorusturmaNo"
          placeholder="Soruşturma numarası giriniz"
          class="w-full">
      </div>

      <!-- Mahkeme Karar No -->
      <div class="flex flex-col">
        <label for="mahkemeKararNo" class="text-sm font-medium text-gray-700 mb-1">
          Mahkeme Karar No
        </label>
        <input
          pInputText
          id="mahkemeKararNo"
          [(ngModel)]="aramaFiltresi.mahkemeKararNo"
          placeholder="Mahkeme karar numarası giriniz"
          class="w-full">
      </div>

      <!-- Mahkeme Kodu -->
      <div class="flex flex-col">
        <label for="mahkemeKodu" class="text-sm font-medium text-gray-700 mb-1">
          Mahkeme Kodu
        </label>
        <input
          pInputText
          id="mahkemeKodu"
          [(ngModel)]="aramaFiltresi.mahkemeKodu"
          placeholder="Mahkeme kodu giriniz"
          class="w-full">
      </div>

      <!-- Durum -->
      <div class="flex flex-col">
        <label for="durum" class="text-sm font-medium text-gray-700 mb-1">
          Durum
        </label>
        <p-select
          [(ngModel)]="aramaFiltresi.durum"
          [options]="durumSecenekleri"
          optionLabel="label"
          optionValue="value"
          placeholder="Durum seçiniz"
          inputId="durum"
          class="w-full">
        </p-select>
      </div>

      <!-- Açıklama -->
      <div class="flex flex-col">
        <label for="aciklama" class="text-sm font-medium text-gray-700 mb-1">
          Açıklama
        </label>
        <input
          pInputText
          id="aciklama"
          [(ngModel)]="aramaFiltresi.aciklama"
          placeholder="Açıklama giriniz"
          class="w-full">
      </div>

      <!-- Kayıt Tarihi -->
      <div class="flex flex-col">
        <label for="kayitTarihi" class="text-sm font-medium text-gray-700 mb-1">
          Kayıt Tarihi
        </label>
        <p-datepicker
          [(ngModel)]="aramaFiltresi.kayitTarihi"
          dateFormat="dd/mm/yy"
          placeholder="Kayıt tarihi seçiniz"
          icon="pi pi-calendar" iconDisplay="input"
          inputId="kayitTarihi"
          class="w-full">
        </p-datepicker>
      </div>

      <!-- Kaydeden Kullanıcı ID -->
      <div class="flex flex-col">
        <label for="kaydedenKullaniciId" class="text-sm font-medium text-gray-700 mb-1">
          Kaydeden Kullanıcı ID
        </label>
        <input
          pInputText
          id="kaydedenKullaniciId"
          [(ngModel)]="aramaFiltresi.kaydedenKullaniciId"
          placeholder="Kullanıcı ID giriniz"
          type="number"
          class="w-full">
      </div>

      <!-- Evrak Sıra No -->
      <div class="flex flex-col">
        <label for="evrakSiraNo" class="text-sm font-medium text-gray-700 mb-1">
          Evrak Sıra No
        </label>
        <input
          pInputText
          id="evrakSiraNo"
          [(ngModel)]="aramaFiltresi.evrakSiraNo"
          placeholder="Evrak sıra numarası giriniz"
          class="w-full">
      </div>

      <!-- Mahkeme Adı -->
      <div class="flex flex-col">
        <label for="mahkemeAdi" class="text-sm font-medium text-gray-700 mb-1">
          Mahkeme Adı
        </label>
        <input
          pInputText
          id="mahkemeAdi"
          [(ngModel)]="aramaFiltresi.mahkemeAdi"
          placeholder="Mahkeme adı giriniz (henüz aktif değil)"
          class="w-full opacity-75">
      </div>

      <!-- Kurum Kodu -->
      <div class="flex flex-col">
        <label for="kurumKodu" class="text-sm font-medium text-gray-700 mb-1">
          Kurum Kodu
        </label>
        <input
          pInputText
          id="kurumKodu"
          [(ngModel)]="aramaFiltresi.kurumKodu"
          placeholder="Kurum kodu giriniz (henüz aktif değil)"
          class="w-full opacity-75">
      </div>

      <!-- Kurum Adı -->
      <div class="flex flex-col">
        <label for="kurumAdi" class="text-sm font-medium text-gray-700 mb-1">
          Kurum Adı
        </label>
        <input
          pInputText
          id="kurumAdi"
          [(ngModel)]="aramaFiltresi.kurumAdi"
          placeholder="Kurum adı giriniz (henüz aktif değil)"
          class="w-full opacity-75">
      </div>

      <!-- Evrak Konusu -->
      <div class="flex flex-col">
        <label for="evrakKonusu" class="text-sm font-medium text-gray-700 mb-1">
          Evrak Konusu
        </label>
        <input
          pInputText
          id="evrakKonusu"
          [(ngModel)]="aramaFiltresi.evrakKonusu"
          placeholder="Evrak konusu giriniz (henüz aktif değil)"
          class="w-full opacity-75">
      </div>

      <!-- Boş alan -->
      <div class="flex flex-col justify-end">
        <div class="flex gap-2">
          <p-button
            icon="pi pi-search"
            label="Ara"
            (onClick)="evrakAra()"
            [loading]="yukleniyor"
            class="flex-1">
          </p-button>
          <p-button
            icon="pi pi-times"
            label="Temizle"
            severity="secondary"
            (onClick)="filtreleriTemizle()"
            class="flex-1">
          </p-button>
        </div>
      </div>
    </div>
  </p-card>

  <!-- Sonuçlar Tablosu -->
  <p-card header="Arama Sonuçları" class="mb-4">
    <p-table
      [value]="evraklar"
      [loading]="yukleniyor"
      [paginator]="true"
      [rows]="10"
      [rowsPerPageOptions]="[10, 25, 50]"
      [showCurrentPageReport]="true"
      currentPageReportTemplate="{first} - {last} / {totalRecords} kayıt"
      [globalFilterFields]="['sorusturmaNo', 'mahkemeKararNo', 'mahkemeKodu', 'mahkemeAdi', 'durumu', 'kaydedenKullaniciAdi']"
      responsiveLayout="scroll"
      styleClass="p-datatable-sm">

      <ng-template pTemplate="header">
        <tr>
          <th pSortableColumn="mahkemeKararTalepId">
            ID
            <p-sortIcon field="mahkemeKararTalepId"></p-sortIcon>
          </th>
          <th pSortableColumn="evrakSiraNo">
            Evrak Sıra No
            <p-sortIcon field="evrakSiraNo"></p-sortIcon>
          </th>
          <th pSortableColumn="sorusturmaNo">
            Soruşturma No
            <p-sortIcon field="sorusturmaNo"></p-sortIcon>
          </th>
          <th pSortableColumn="mahkemeKararNo">
            Mahkeme Karar No
            <p-sortIcon field="mahkemeKararNo"></p-sortIcon>
          </th>
          <th pSortableColumn="mahkemeKodu">
            Mahkeme Kodu
            <p-sortIcon field="mahkemeKodu"></p-sortIcon>
          </th>
          <th pSortableColumn="mahkemeAdi">
            Mahkeme Adı
            <p-sortIcon field="mahkemeAdi"></p-sortIcon>
          </th>
          <th pSortableColumn="durumu">
            Durum
            <p-sortIcon field="durumu"></p-sortIcon>
          </th>
          <th pSortableColumn="kararKayitTarihi">
            Kayıt Tarihi
            <p-sortIcon field="kararKayitTarihi"></p-sortIcon>
          </th>
          <th pSortableColumn="kaydedenKullaniciId">
            Kaydeden Kullanıcı
            <p-sortIcon field="kaydedenKullaniciId"></p-sortIcon>
          </th>
          <th>İşlemler</th>
        </tr>
      </ng-template>

      <ng-template pTemplate="body" let-evrak>
        <tr>
          <td>
            <span class="font-mono text-sm">{{ evrak.mahkemeKararTalepId }}</span>
          </td>
          <td>
            <span class="font-mono text-sm">{{ evrak.evrakSiraNo }}</span>
          </td>
          <td>
            <span class="font-mono text-sm">{{ evrak.sorusturmaNo }}</span>
          </td>
          <td>
            <span class="font-mono text-sm">{{ evrak.mahkemeKararNo }}</span>
          </td>
          <td>
            <span class="font-mono text-sm">{{ evrak.mahkemeKodu }}</span>
          </td>
          <td>
            <span class="text-sm">{{ evrak.mahkemeAdi }}</span>
          </td>
          <td>
            <p-tag
              [value]="durumMetniGetir(evrak.durumu)"
              [severity]="durumSeviyesiGetir(evrak.durumu)">
            </p-tag>
          </td>
          <td>{{ tarihFormatiDuzelt(evrak.kararKayitTarihi) }}</td>
          <td>
            <div class="text-sm">
              <div class="font-medium">{{ evrak.kaydedenKullaniciAdi }}</div>
              <div class="text-gray-500">{{ evrak.kaydedenAdiSoyadi }}</div>
            </div>
          </td>
          <td>
            <p-button
              icon="pi pi-eye"
              size="small"
              severity="info"
              (onClick)="evrakDetayGoster(evrak)"
              pTooltip="Detay Görüntüle"
              tooltipPosition="top">
            </p-button>
          </td>
        </tr>
      </ng-template>

      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="10" class="text-center py-8">
            <i class="pi pi-info-circle text-4xl text-gray-400 mb-2"></i>
            <p class="text-gray-500">Arama kriterlerinize uygun evrak bulunamadı.</p>
          </td>
        </tr>
      </ng-template>
    </p-table>
  </p-card>

  <!-- Toast Mesajları -->
  <p-toast></p-toast>

  <!-- Loading Spinner -->
  <div *ngIf="yukleniyor" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <p-progressSpinner></p-progressSpinner>
  </div>
</div>

<!-- Evrak Detay Dialog -->
<p-dialog
  header="Evrak Detayı"
  [(visible)]="detayDialogGoruntule"
  [modal]="true"
  [style]="{width: '80vw', maxWidth: '800px'}"
  [closable]="true"
  (onHide)="detayDialogKapat()">

  <div *ngIf="seciliEvrak" class="space-y-4">
    <!-- Genel Bilgiler -->
    <div class="grid grid-cols-2 gap-4">
      <div>
        <label class="text-sm font-medium text-gray-700">Mahkeme Karar Talep ID:</label>
        <p class="font-mono">{{ seciliEvrak.mahkemeKararTalepId }}</p>
      </div>
      <div>
        <label class="text-sm font-medium text-gray-700">Evrak ID:</label>
        <p class="font-mono">{{ seciliEvrak.evrakId }}</p>
      </div>
      <div>
        <label class="text-sm font-medium text-gray-700">Evrak Sıra No:</label>
        <p class="font-mono">{{ seciliEvrak.evrakSiraNo }}</p>
      </div>
      <div>
        <label class="text-sm font-medium text-gray-700">Kurum Evrak No:</label>
        <p class="font-mono">{{ seciliEvrak.kurumEvrakNo }}</p>
      </div>
    </div>

    <p-divider></p-divider>

    <!-- Mahkeme Bilgileri -->
    <div>
      <h4 class="text-lg font-semibold mb-3">Mahkeme Bilgileri</h4>
      <div class="grid grid-cols-2 gap-4">
        <div>
          <label class="text-sm font-medium text-gray-700">Mahkeme Karar No:</label>
          <p class="font-mono">{{ seciliEvrak.mahkemeKararNo }}</p>
        </div>
        <div>
          <label class="text-sm font-medium text-gray-700">Soruşturma No:</label>
          <p class="font-mono">{{ seciliEvrak.sorusturmaNo }}</p>
        </div>
        <div>
          <label class="text-sm font-medium text-gray-700">Mahkeme Kodu:</label>
          <p class="font-mono">{{ seciliEvrak.mahkemeKodu }}</p>
        </div>
        <div>
          <label class="text-sm font-medium text-gray-700">Mahkeme Adı:</label>
          <p>{{ seciliEvrak.mahkemeAdi }}</p>
        </div>
        <div>
          <label class="text-sm font-medium text-gray-700">Mahkeme İl/İlçe Kodu:</label>
          <p class="font-mono">{{ seciliEvrak.mahkemeIlIlceKodu }}</p>
        </div>
        <div>
          <label class="text-sm font-medium text-gray-700">Mahkeme İl/İlçe Adı:</label>
          <p>{{ seciliEvrak.mahkemeIlIlceAdi }}</p>
        </div>
      </div>
    </div>

    <p-divider></p-divider>

    <!-- Kurum Bilgileri -->
    <div>
      <h4 class="text-lg font-semibold mb-3">Kurum Bilgileri</h4>
      <div class="grid grid-cols-2 gap-4">
        <div>
          <label class="text-sm font-medium text-gray-700">Kurum Kodu:</label>
          <p class="font-mono">{{ seciliEvrak.kurumKodu }}</p>
        </div>
        <div>
          <label class="text-sm font-medium text-gray-700">Kurum Adı:</label>
          <p>{{ seciliEvrak.kurumAdi }}</p>
        </div>
        <div>
          <label class="text-sm font-medium text-gray-700">Evrak No:</label>
          <p class="font-mono">{{ seciliEvrak.evrakNo }}</p>
        </div>
        <div>
          <label class="text-sm font-medium text-gray-700">Evrak Konusu:</label>
          <p>{{ seciliEvrak.evrakKonusu }}</p>
        </div>
      </div>
    </div>

    <p-divider></p-divider>

    <!-- Kullanıcı Bilgileri -->
    <div>
      <h4 class="text-lg font-semibold mb-3">Kullanıcı Bilgileri</h4>
      <div class="grid grid-cols-2 gap-4">
        <div>
          <label class="text-sm font-medium text-gray-700">Kaydeden Kullanıcı ID:</label>
          <p class="font-mono">{{ seciliEvrak.kaydedenKullaniciId }}</p>
        </div>
        <div>
          <label class="text-sm font-medium text-gray-700">Kullanıcı Adı:</label>
          <p>{{ seciliEvrak.kaydedenKullaniciAdi }}</p>
        </div>
        <div>
          <label class="text-sm font-medium text-gray-700">Adı Soyadı:</label>
          <p>{{ seciliEvrak.kaydedenAdiSoyadi }}</p>
        </div>
        <div>
          <label class="text-sm font-medium text-gray-700">Durum:</label>
          <p-tag
            [value]="durumMetniGetir(seciliEvrak.durumu)"
            [severity]="durumSeviyesiGetir(seciliEvrak.durumu)">
          </p-tag>
        </div>
      </div>
    </div>

    <p-divider></p-divider>

    <!-- Tarih Bilgileri -->
    <div>
      <h4 class="text-lg font-semibold mb-3">Tarih Bilgileri</h4>
      <div class="grid grid-cols-2 gap-4">
        <div>
          <label class="text-sm font-medium text-gray-700">Kurum Evrak Tarihi:</label>
          <p>{{ tarihFormatiDuzelt(seciliEvrak.kurumEvrakTarihi) }}</p>
        </div>
        <div>
          <label class="text-sm font-medium text-gray-700">Karar Kayıt Tarihi:</label>
          <p>{{ tarihFormatiDuzelt(seciliEvrak.kararKayitTarihi) }}</p>
        </div>
      </div>
    </div>

    <!-- Açıklama -->
    <div *ngIf="seciliEvrak.aciklama">
      <p-divider></p-divider>
      <h4 class="text-lg font-semibold mb-3">Açıklama</h4>
      <p class="text-gray-700">{{ seciliEvrak.aciklama }}</p>
    </div>
  </div>

  <ng-template pTemplate="footer">
    <p-button
      label="Kapat"
      icon="pi pi-times"
      (onClick)="detayDialogKapat()"
      severity="secondary">
    </p-button>
  </ng-template>
</p-dialog>
