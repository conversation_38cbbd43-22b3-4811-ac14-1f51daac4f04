package iym.db.jpa.dao.mk;

import iym.common.model.entity.iym.mk.MahkemeSuclar;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for MahkemeSuclarRepo entity
 */
@Repository
public interface MahkemeSuclarRepo extends JpaRepository<MahkemeSuclar, Long> {

    List<MahkemeSuclar> findByMahkemeKararId(Long mahkemeKararId);

    Optional<MahkemeSuclar> findByMahkemeKararIdAndSucTipKodu(Long mahkemeKararId, String sucTipKodu);
}
