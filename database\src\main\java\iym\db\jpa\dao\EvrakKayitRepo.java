package iym.db.jpa.dao;

import iym.common.model.entity.iym.EvrakKayit;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for EvrakKayit entity
 */
@Repository
public interface EvrakKayitRepo extends JpaRepository<EvrakKayit, Long> {

    boolean existsByEvrakSiraNo(String evrakSiraNo);

    Optional<EvrakKayit> findByEvrakNoAndEvrakGeldigiKurumKodu(String evrakNo,
                                                            String evrakGeldigiKurumKodu);

    List<EvrakKayit> findAllByEvrakNoAndEvrakGeldigiKurumKodu(String evrakNo,
                                                           String evrakGeldigiKurumKodu);

    //TODO : query li yap
    //@Query("SELECT ek from EvrakKayit ek WHERE ek.evrakGeldigiKurumKodu = :iIlIlceKodu and ek.geldigiIlIlceKodu= :gelIlIlceKodu and ek.evrakGeldigiKurumKodu = :evrakGeldigiKurumKodu")
    Optional<EvrakKayit> findByEvrakNoAndGeldigiIlIlceKoduAndEvrakGeldigiKurumKodu(String evrakNo,
                                                                                   String gelIlIlceKodu,
                                                                                   String evrakGeldigiKurumKodu);


    List<EvrakKayit> findByEvrakTipi(String evrakTipi);

    List<EvrakKayit> findByGirisTarihBetween(LocalDateTime startDate, LocalDateTime endDate);

    List<EvrakKayit> findByDurumu(String durumu);

    List<EvrakKayit> findByHavaleBirim(String havaleBirim);

    List<EvrakKayit> findByAcilmi(String acilmi);
}
