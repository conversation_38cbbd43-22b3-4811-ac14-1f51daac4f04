package iym.common.service.db.mk;

import iym.common.model.entity.iym.mk.Hedefler;
import iym.common.service.db.GenericDbService;

import java.util.List;
import java.util.Optional;

/**
 * Service interface for Hedefler entity
 */
public interface DbHedeflerService extends GenericDbService<Hedefler, Long> {

    Optional<Hedefler> findById(Long id);

    Optional<Hedefler> findByMahkemeKararIdAndHedefNoAndHedefTipi(Long mahkemeKararId, String hedefNo, Integer hedefTipi);

    List<Hedefler> findByMahkemeKararId(Long mahkemeKararId);

}
