/**
 * IYM Backend OpenAPI definition
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { IDHedefAidiyatInfo } from './iDHedefAidiyatInfo';
import { DetayMahkemeKararInfo } from './detayMahkemeKararInfo';
import { IDHedefInfo } from './iDHedefInfo';
import { IDKarariSucTipiInfo } from './iDKarariSucTipiInfo';


export interface UzatmaKarariHedefInfo { 
    hedefBilgisi?: IDHedefInfo;
    baslamaTarihi?: string;
    sureTip?: UzatmaKarariHedefInfo.SureTipEnum;
    sure?: number;
    uzatmaSayisi?: number;
    iliskiliMahkemeKararInfo?: DetayMahkemeKararInfo;
    hedefAidiyatListesi?: Array<IDHedefAidiyatInfo>;
    sucTipleri?: Array<IDKarariSucTipiInfo>;
    canakNo?: string;
}
export namespace UzatmaKarariHedefInfo {
    export const SureTipEnum = {
        Gun: 'GUN',
        Ay: 'AY',
        Hicbiri: 'HICBIRI'
    } as const;
    export type SureTipEnum = typeof SureTipEnum[keyof typeof SureTipEnum];
}


