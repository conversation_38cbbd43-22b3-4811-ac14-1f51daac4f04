# Self-Hosted GitLab CE as a Full GitHub Replacement

This document explains how to set up **GitLab CE (Community Edition)** on your local PC (Windows 11 with Docker Desktop) as a **full open-source replacement for GitHub + GitHub Actions**. The setup supports:

- Source code hosting (GitHub alternative)
- Java multi-module projects (Maven build)
- Angular frontend builds
- Docker image generation & built-in Container Registry
- CI/CD pipelines via `.gitlab-ci.yml`
- Local package/artifact registry (<PERSON><PERSON>, npm, Docker)

---

## 1. Why GitLab CE?

Unlike Jenkins or Nexus where you need multiple services, **GitLab CE** provides:

- ✅ Git hosting (like GitHub)
- ✅ CI/CD pipelines (like GitHub Actions)
- ✅ Built-in Docker Container Registry
- ✅ Built-in Package Registry (Maven, npm, etc.)
- ✅ Unlimited usage if self-hosted (no billing limits)

---

## 2. Prerequisites

- Windows 11 with [Docker Desktop](https://www.docker.com/products/docker-desktop/)
- At least **8 GB RAM** (GitLab is heavy)
- Open ports: `80` (HTTP), `443` (HTTPS), `2222` (SSH), `5050` (Container Registry)

---

## 3. Docker Compose Setup

Create a folder (e.g., `C:\gitlab`) and inside it a `docker-compose.yml`:

```yaml
version: '3.6'
services:
  web:
    image: gitlab/gitlab-ce:latest
    restart: always
    hostname: gitlab.local
    environment:
      GITLAB_OMNIBUS_CONFIG: |
        external_url 'http://localhost'
        gitlab_rails['gitlab_shell_ssh_port'] = 2222
        registry_external_url 'http://localhost:5050'
    ports:
      - '80:80'
      - '443:443'
      - '2222:22'
      - '5050:5050'
    volumes:
      - ./config:/etc/gitlab
      - ./logs:/var/log/gitlab
      - ./data:/var/opt/gitlab
```

Start GitLab:
```powershell
docker-compose up -d
```

---

## 4. Access GitLab

- Open `http://localhost` in your browser.
- Get root password:
  ```powershell
  docker exec -it gitlab-web-1 grep 'Password:' /etc/gitlab/initial_root_password
  ```
- Login with `root` and the password.

---

## 5. GitLab Container Registry

GitLab provides a built-in **Docker Registry** at `http://localhost:5050`.

- Push images: `docker push localhost:5050/myproject/myapp:tag`
- Authenticate with your GitLab credentials or CI job token.

---

## 6. GitLab Runner Setup

GitLab CI/CD pipelines need a **runner**. Run it with Docker:

```powershell
docker run -d --name gitlab-runner --restart always `
  -v //var/run/docker.sock:/var/run/docker.sock `
  -v %cd%/config:/etc/gitlab-runner `
  gitlab/gitlab-runner:latest
```

Register runner:
```powershell
docker exec -it gitlab-runner gitlab-runner register
```
- GitLab URL: `http://host.docker.internal/`
- Registration token: from **GitLab → Admin → Runners**
- Executor: `docker`
- Default image: `maven:3.9.6-eclipse-temurin-17`

---

## 7. Example `.gitlab-ci.yml`

Place in your repo root:

```yaml
stages:
  - build
  - test
  - docker

variables:
  MAVEN_OPTS: "-Dmaven.repo.local=.m2/repository"

cache:
  paths:
    - .m2/repository
    - frontend/node_modules

build-backend:
  stage: build
  image: maven:3.9.6-eclipse-temurin-17
  script:
    - mvn clean install -DskipTests
  artifacts:
    paths:
      - target/*.jar

build-frontend:
  stage: build
  image: node:18
  script:
    - cd frontend
    - npm install
    - npm run build --prod
  artifacts:
    paths:
      - frontend/dist

docker-build:
  stage: docker
  image: docker:20
  services:
    - docker:dind
  script:
    - export VERSION=$(mvn help:evaluate -Dexpression=project.version -q -DforceStdout)
    - docker build -t localhost:5050/myproject/myapp:$VERSION .
    - docker login -u gitlab-ci-token -p $CI_JOB_TOKEN localhost:5050
    - docker push localhost:5050/myproject/myapp:$VERSION
```

---

## 8. Artifact Management

- **Maven Artifacts** → GitLab Package Registry (`maven` endpoint)
- **npm Packages** → GitLab Package Registry (`npm` endpoint)
- **Docker Images** → GitLab Container Registry (`localhost:5050`)
- **Pipeline Artifacts** → stored in GitLab CI jobs

---

## 9. Benefits

✅ Full GitHub replacement (repo, CI/CD, registry, issues, wiki)  
✅ Single tool (GitLab CE) → easier maintenance  
✅ Unlimited private repos and pipelines  
✅ Built-in Docker & Package registry  
✅ YAML-based pipelines (similar to GitHub Actions)

---

## 10. Next Steps

- Secure with HTTPS (reverse proxy like Nginx + Let's Encrypt)
- Configure daily backups of GitLab data
- Scale with multiple GitLab Runners if needed
- Explore GitLab Pages for frontend hosting

---

**Now you have a full GitHub replacement running locally on Windows 11 with Docker!** 🚀

