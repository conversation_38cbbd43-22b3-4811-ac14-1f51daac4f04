package iym.db.jpa.dao.mktalep;

import iym.common.model.entity.iym.talep.MahkemeKararGuncellemeTalep;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Repository interface for MahkemeKoduDetayTalepRepo entity
 */
@Repository
public interface MahkemeKararGuncelleTalepRepo extends JpaRepository<MahkemeKararGuncellemeTalep, Long> {

    Optional<MahkemeKararGuncellemeTalep> findByDetayMahkemeKararTalepId(Long mahkemeKararTalepId);

}
