import { Component, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { CheckboxModule } from 'primeng/checkbox';
import { InputTextModule } from 'primeng/inputtext';
import { PasswordModule } from 'primeng/password';
import { RippleModule } from 'primeng/ripple';
import { AppFloatingConfigurator } from '../layout/component/app.floatingconfigurator';
import { AuthService } from './auth.service';
import { enumKullaniciStatus } from '../kullanici-yonetimi/kullanici-status.enum';
import { environment } from '../../enviroments/environment';
import { NgIf } from '@angular/common';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [ButtonModule, CheckboxModule, InputTextModule, PasswordModule, FormsModule, RouterModule, RippleModule, AppFloatingConfigurator, NgIf],
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss'
})
export class LoginComponent {
  email: string = '';

  password: string = '';

  checked: boolean = false;
  
  // Geliştirme ortamı için otomatik giriş bilgileri
  private readonly DEV_USERNAME = 'admin';
  private readonly DEV_PASSWORD = '1';
  
  // HTML'de kullanabilmek için environment'ı public yapıyoruz
  environment = environment;

  constructor(private router: Router, private authService:AuthService)
  {
    
  }
  
  // Geliştirme ortamında otomatik giriş için
  autoDevelopmentLogin(): void {
    this.email = this.DEV_USERNAME;
    this.password = this.DEV_PASSWORD;
    this.login();
  }

  login() {
    this.authService.login(this.email,this.password).subscribe(
      {
        next:response => {
          if (response) {
            //  this.layoutService.config.menuMode='static';
            
            if(response.kullaniciStatus==enumKullaniciStatus.SifreDegistirmeli)
            {
              localStorage.setItem("extoken",response.token);
              this.router.navigate(['/reset-password']);
              
            } 
            else
            {
              localStorage.setItem("token",response.token);
              this.router.navigate(['/']);
            }  
            
          }      
        },
        error:err=>{
          alert('Hatalı kullanıcı adı veya şifre');
        }
          
      }
  );
} 
   
}
