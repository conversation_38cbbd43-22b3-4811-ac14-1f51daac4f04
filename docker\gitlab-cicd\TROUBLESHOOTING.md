# GitLab CI/CD Troubleshooting Guide

This document provides solutions for common issues you might encounter when setting up and using the self-hosted GitLab CI/CD infrastructure.

## Table of Contents

1. [GitLab Startup Issues](#gitlab-startup-issues)
2. [GitLab Runner Issues](#gitlab-runner-issues)
3. [Pipeline Execution Issues](#pipeline-execution-issues)
4. [Docker Registry Issues](#docker-registry-issues)
5. [Performance Issues](#performance-issues)

## GitLab Startup Issues

### GitLab Container Fails to Start

**Symptoms:**
- Docker container exits shortly after starting
- Error messages in Docker logs
- Container shows as "unhealthy" in `docker ps` output

**Solutions:**

1. Check if ports are already in use:
   ```powershell
   # Windows
   netstat -ano | findstr "************** 5050"
   
   # Linux
   netstat -tulpn | grep -E "8929|8443|2222|5050"
   ```

2. Ensure sufficient system resources:
   - GitLab requires at least 4GB of RAM
   - Increase Docker Desktop resource allocation (Settings > Resources)

3. Check Docker logs for specific errors:
   ```bash
   docker logs gitlab-cicd-web-1
   ```

4. Reset GitLab configuration:
   ```bash
   # Stop containers
   docker-compose down
   
   # Remove volumes (Warning: This will delete all GitLab data)
   docker volume rm gitlab-cicd_gitlab_config gitlab-cicd_gitlab_logs gitlab-cicd_gitlab_data
   
   # Start containers again
   docker-compose up -d
   ```

### Database Initialization Issues

**Symptoms:**
- GitLab container shows as "unhealthy" in `docker ps` output
- Error messages in logs about missing database tables
- Errors like "relation 'container_repositories' does not exist" in logs

**Solutions:**

1. Enable automatic database migrations in docker-compose.yml:
   ```yaml
   # In docker-compose.yml, under GITLAB_OMNIBUS_CONFIG
   gitlab_rails['auto_migrate'] = true
   ```

2. Restart GitLab containers:
   ```bash
   docker-compose down
   docker-compose up -d
   ```

3. Wait for migrations to complete (this may take several minutes)

4. Verify database initialization:
   ```bash
   docker exec gitlab-cicd-web-1 gitlab-rake gitlab:check
   ```

## GitLab Runner Issues

### Runner Registration Fails

**Symptoms:**
- Error during runner registration
- Runner not appearing in GitLab UI

**Solutions:**

1. Ensure GitLab is fully initialized before registering runners:
   - Wait at least 5 minutes after starting GitLab
   - Check if you can log in to the GitLab web interface

2. Verify the registration token:
   - Get a new token from Admin Area > Runners

3. Check network connectivity between runner and GitLab:
   - For Docker Compose setup, ensure they're on the same network
   - Try using `http://web/` as the GitLab URL (internal Docker network name)

4. Manually register the runner:
   ```bash
   docker exec -it docker-gitlab-cicd-runner-1 gitlab-runner register
   ```
   - URL: `http://web/`
   - Token: [Get from GitLab UI]
   - Description: `docker-runner`
   - Tags: `docker,windows` or `docker,linux`
   - Executor: `docker`
   - Default image: `maven:3.9.6-eclipse-temurin-17`

### Runner Not Picking Up Jobs

**Symptoms:**
- Pipeline jobs stay in "pending" state
- No runner is assigned to jobs

**Solutions:**

1. Check if runner is connected:
   - Go to Admin Area > Runners
   - Verify runner status is "online"

2. Check job tags and runner tags:
   - Ensure job tags match runner tags
   - Try adding `tags: ["docker"]` to your job in `.gitlab-ci.yml`

3. Restart the runner:
   ```bash
   docker restart docker-gitlab-cicd-runner-1
   ```

4. Check runner logs:
   ```bash
   docker logs docker-gitlab-cicd-runner-1
   ```

## Pipeline Execution Issues

### Jobs Fail with Docker-in-Docker Issues

**Symptoms:**
- Docker build/push commands fail
- Error about Docker daemon not being available

**Solutions:**

1. Ensure Docker socket is properly mounted:
   - Check volume mapping in `docker-compose.yml`
   - For Windows: `//var/run/docker.sock:/var/run/docker.sock`
   - For Linux: `/var/run/docker.sock:/var/run/docker.sock`

2. Use Docker-in-Docker service:
   ```yaml
   # In .gitlab-ci.yml
   docker-build-job:
     image: docker:20
     services:
       - docker:dind
     variables:
       DOCKER_TLS_CERTDIR: ""
       DOCKER_HOST: "tcp://docker:2375"
   ```

3. Ensure runner has privileged mode enabled:
   - Edit `/etc/gitlab-runner/config.toml`
   - Set `privileged = true` under the `[runners.docker]` section

### Maven Build Fails

**Symptoms:**
- Maven build errors
- Dependency resolution issues

**Solutions:**

1. Use Maven cache:
   ```yaml
   # In .gitlab-ci.yml
   cache:
     paths:
       - .m2/repository
   ```

2. Set Maven options:
   ```yaml
   variables:
     MAVEN_OPTS: "-Dmaven.repo.local=.m2/repository -Xmx1024m"
   ```

3. Check Maven version compatibility:
   - Use a specific Maven image version: `maven:3.9.6-eclipse-temurin-17`

## Docker Registry Issues

### Cannot Push to GitLab Container Registry

**Symptoms:**
- Docker push fails
- Authentication errors

**Solutions:**

1. Ensure registry is properly configured:
   - Check `registry_external_url` in GitLab configuration

2. Use the correct authentication method:
   ```yaml
   # In .gitlab-ci.yml
   script:
     - docker login -u gitlab-ci-token -p $CI_JOB_TOKEN localhost:5050
     - docker push localhost:5050/iym/backend:$CI_COMMIT_REF_SLUG
   ```

3. Check registry logs:
   ```bash
   docker exec -it docker-gitlab-cicd-web-1 gitlab-ctl tail registry
   ```

4. Restart the registry service:
   ```bash
   docker exec -it docker-gitlab-cicd-web-1 gitlab-ctl restart registry
   ```

## Performance Issues

### GitLab UI is Slow

**Symptoms:**
- Web interface is sluggish
- High CPU/memory usage

**Solutions:**

1. Increase Docker resource allocation:
   - Docker Desktop > Settings > Resources
   - Allocate at least 4 CPU cores and 8GB RAM

2. Optimize GitLab configuration:
   ```yaml
   # In docker-compose.yml
   environment:
     GITLAB_OMNIBUS_CONFIG: |
       # Reduce number of background jobs
       sidekiq['concurrency'] = 1
       # Disable unused features
       gitlab_rails['monitoring_whitelist'] = ['*********/8', '::1/128']
       prometheus_monitoring['enable'] = false
   ```

3. Clean up old data:
   - Remove old Docker images: `docker image prune -a`
   - Clean up GitLab artifacts and logs through the UI

### Pipeline Jobs Are Slow

**Symptoms:**
- CI/CD jobs take a long time to complete
- Resource constraints during job execution

**Solutions:**

1. Optimize Docker images:
   - Use specific image tags instead of `latest`
   - Use lightweight images when possible

2. Improve caching strategy:
   ```yaml
   # In .gitlab-ci.yml
   cache:
     key: $CI_COMMIT_REF_SLUG
     paths:
       - .m2/repository
       - frontend/node_modules
   ```

3. Parallelize jobs:
   - Split large jobs into smaller, parallel jobs
   - Use dependencies to manage job relationships

4. Add more runners:
   - Register additional runners for better job distribution

## Additional Resources

- [GitLab Documentation](https://docs.gitlab.com/)
- [GitLab Runner Documentation](https://docs.gitlab.com/runner/)
- [GitLab CI/CD Documentation](https://docs.gitlab.com/ee/ci/)
- [Docker Documentation](https://docs.docker.com/)