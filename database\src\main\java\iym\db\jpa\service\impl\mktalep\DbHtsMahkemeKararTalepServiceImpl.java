package iym.db.jpa.service.impl.mktalep;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.talep.HtsMahkemeKararTalep;
import iym.common.service.db.mktalep.DbHtsMahkemeKararTalepService;
import iym.db.jpa.dao.mktalep.HtsMahkemeKararTalepRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Service implementation for HtsMahkemeKararTalep entity
 */
@Service
public class DbHtsMahkemeKararTalepServiceImpl extends GenericDbServiceImpl<HtsMahkemeKararTalep, Long> implements DbHtsMahkemeKararTalepService {

    private final HtsMahkemeKararTalepRepo htsMahkemeKararTalepRepo;

    @Autowired
    public DbHtsMahkemeKararTalepServiceImpl(HtsMahkemeKararTalepRepo repository) {
        super(repository);
        this.htsMahkemeKararTalepRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public List<HtsMahkemeKararTalep> findByEvrakId(Long evrakId) {
        return htsMahkemeKararTalepRepo.findByEvrakId(evrakId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HtsMahkemeKararTalep> findByKullaniciId(Long kullaniciId) {
        return htsMahkemeKararTalepRepo.findByKullaniciId(kullaniciId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HtsMahkemeKararTalep> findByDurum(String durum) {
        return htsMahkemeKararTalepRepo.findByDurum(durum);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HtsMahkemeKararTalep> findByKararTip(String kararTip) {
        return htsMahkemeKararTalepRepo.findByKararTip(kararTip);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HtsMahkemeKararTalep> findByHukukBirim(String hukukBirim) {
        return htsMahkemeKararTalepRepo.findByHukukBirim(hukukBirim);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HtsMahkemeKararTalep> findByMahkemeIli(String mahkemeIli) {
        return htsMahkemeKararTalepRepo.findByMahkemeIli(mahkemeIli);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HtsMahkemeKararTalep> findByMahkemeKodu(String mahkemeKodu) {
        return htsMahkemeKararTalepRepo.findByMahkemeKodu(mahkemeKodu);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HtsMahkemeKararTalep> findByMahkemeAdiContainingIgnoreCase(String mahkemeAdi) {
        return htsMahkemeKararTalepRepo.findByMahkemeAdiContainingIgnoreCase(mahkemeAdi);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<HtsMahkemeKararTalep> findByMahkemeKararNo(String mahkemeKararNo) {
        return htsMahkemeKararTalepRepo.findByMahkemeKararNo(mahkemeKararNo);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HtsMahkemeKararTalep> findBySorusturmaNo(String sorusturmaNo) {
        return htsMahkemeKararTalepRepo.findBySorusturmaNo(sorusturmaNo);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HtsMahkemeKararTalep> findByKayitTarihiBetween(LocalDateTime startDate, LocalDateTime endDate) {
        return htsMahkemeKararTalepRepo.findByKayitTarihiBetween(startDate, endDate);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HtsMahkemeKararTalep> findByMahkemeIliAndMahkemeKodu(String mahkemeIli, String mahkemeKodu) {
        return htsMahkemeKararTalepRepo.findByMahkemeIliAndMahkemeKodu(mahkemeIli, mahkemeKodu);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HtsMahkemeKararTalep> findByKararTipAndDurum(String kararTip, String durum) {
        return htsMahkemeKararTalepRepo.findByKararTipAndDurum(kararTip, durum);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByMahkemeKararNo(String mahkemeKararNo) {
        return htsMahkemeKararTalepRepo.existsByMahkemeKararNo(mahkemeKararNo);
    }
}
