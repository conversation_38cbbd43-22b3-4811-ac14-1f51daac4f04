/**
 * IYM Backend OpenAPI definition
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface MahkemeKararTalepStateUpdateRequest { 
    id: string;
    talepGuncellemeTuru: MahkemeKararTalepStateUpdateRequest.TalepGuncellemeTuruEnum;
    mahkemeKararTalepId: number;
}
export namespace MahkemeKararTalepStateUpdateRequest {
    export const TalepGuncellemeTuruEnum = {
        Onayla: 'ONAYLA',
        Arsiv: 'ARSIV',
        Sil: 'SIL'
    } as const;
    export type TalepGuncellemeTuruEnum = typeof TalepGuncellemeTuruEnum[keyof typeof TalepGuncellemeTuruEnum];
}


