<app-floating-configurator />
        <div class="bg-surface-50 dark:bg-surface-950 flex items-center justify-center min-h-screen min-w-[100vw] overflow-hidden">
            <div class="flex flex-col items-center justify-center">
                <div style="border-radius: 56px; padding: 0.3rem; background: linear-gradient(180deg, var(--primary-color) 10%, rgba(33, 150, 243, 0) 30%)">
                    <div class="w-full bg-surface-0 dark:bg-surface-900 py-20 px-8 sm:px-20" style="border-radius: 53px">
                        <div class="flex flex-col items-center mb-2">
                            <img src="assets/images/logo.png" alt="Logo" class="mb-1" style="width: 200px; height: auto; object-fit: contain;">
                            <span class="text-muted-color font-medium mt-1">Sign in to continue</span>
                          </div>
                          

                        <div  (keydown.enter)="login()">
                            <label for="email1" class="block text-surface-900 dark:text-surface-0 text-xl font-medium mb-2"><PERSON><PERSON><PERSON><PERSON><PERSON> Adı</label>
                            <input pInputText id="email1" type="text" placeholder="Kullanıcı Adı" class="w-full md:w-[30rem] mb-8" [(ngModel)]="email" />

                            <label for="password1" class="block text-surface-900 dark:text-surface-0 font-medium text-xl mb-2">Parola</label>
                            <p-password  id="password1" [(ngModel)]="password" placeholder="Parola" [toggleMask]="true" styleClass="mb-4" [fluid]="true" [feedback]="false"></p-password>

                            <!--
                          
                            <div class="flex items-center justify-between mt-2 mb-8 gap-8">
                                <div class="flex items-center">
                                    <p-checkbox [(ngModel)]="checked" id="rememberme1" binary class="mr-2"></p-checkbox>
                                    <label for="rememberme1">Remember me</label>
                                </div>
                                <span class="font-medium no-underline ml-2 text-right cursor-pointer text-primary">Forgot password?</span>
                            </div>  -->
                            <div class="flex flex-col gap-2">
                                <p-button label="Login" styleClass="w-full" (onClick)="login()"></p-button>
                                <p-button *ngIf="!environment.production" label="Otomatik Giriş (Geliştirme:admin:1)" styleClass="w-full p-button-secondary" (onClick)="autoDevelopmentLogin()"></p-button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>