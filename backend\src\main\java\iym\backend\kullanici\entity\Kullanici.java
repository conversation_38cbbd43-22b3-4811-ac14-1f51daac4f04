package iym.backend.kullanici.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import iym.backend.kullanici.enums.enumKullaniciStatus;
import iym.backend.kullanicikullanicigrup.entity.KullaniciKullaniciGrup;
import iym.backend.shared.entity.BaseEntity;
import iym.common.enums.KullaniciKurum;
import iym.common.enums.UserStatusType;
import iym.common.util.db.KullaniciKurumConverter;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "kullanicilar")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class <PERSON><PERSON><PERSON> extends BaseEntity {

    @Column(nullable = false)
    @NotNull
    @NotBlank
    @Size(min = 4, max = 100)
    private String kullaniciAdi;

    @Column(length = 11)
    private String tcno;

    private String ad;

    private String soyad;

    private String email;

    @JsonIgnore
    @Column(nullable = false)
    @NotNull
    @NotBlank
    @Size(min = 5, max = 100)
    private String parola;

    private String avatarPath;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private enumKullaniciStatus status = enumKullaniciStatus.SIFRE_DEGISTIRMELI;

    @Column(name = "kurum")
    @Convert(converter = KullaniciKurumConverter.class)
    private KullaniciKurum kurum;

    @OneToMany(mappedBy = "kullanici", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<KullaniciKullaniciGrup> kullaniciKullaniciGruplar = new ArrayList<>();

    @Transient
    private String newPassword;

    @Transient
    @JsonIgnore
    public boolean isActive() {
        return getStatus() == enumKullaniciStatus.AKTIF;
    }

    // Method to get roles from related tables
    @Transient
    @JsonIgnore
    public List<String> getRoles() {
        return kullaniciKullaniciGruplar.stream()
                .flatMap(kkg -> kkg.getKullaniciGrup().getKullaniciGrupYetkiler().stream())
                .map(kgy -> kgy.getYetki().getAd())
                .distinct()
                .toList();
    }

    // Method to check if user has a specific authority/role
    @Transient
    @JsonIgnore
    public boolean hasAuthority(String authority) {
        return getRoles().contains(authority);
    }

    // Method to check if user has any of the given authorities
    @Transient
    @JsonIgnore
    public boolean hasAnyAuthority(String... authorities) {
        List<String> userRoles = getRoles();
        for (String authority : authorities) {
            if (userRoles.contains(authority)) {
                return true;
            }
        }
        return false;
    }

    // Method to get UserStatusType for compatibility with IymUser
    @Transient
    @JsonIgnore
    public UserStatusType getUserStatus() {
        return switch (status) {
            case AKTIF -> UserStatusType.ACTIVE;
            case PASIF -> UserStatusType.PASSIVE;
            case SIFRE_DEGISTIRMELI -> UserStatusType.LOCKED;
        };
    }

    public String getUsername() {
        return kullaniciAdi;
    }

    public void setUsername(String username) {
        this.kullaniciAdi = username;
    }

    public String getPassword() {
        return parola;
    }

    public void setPassword(String password) {
        this.parola = password;
    }

}

