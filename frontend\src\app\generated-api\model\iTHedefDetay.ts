/**
 * IYM Backend OpenAPI definition
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { Hedef } from './hedef';


export interface ITHedefDetay { 
    sorguTipi: ITHedefDetay.SorguTipiEnum;
    hedef: Hedef;
    karsiHedef?: Hedef;
    baslamaTarihi: string;
    bitisTarihi: string;
    tespitTuru: string;
    tespitTuruDetay?: string;
    aciklama?: string;
}
export namespace ITHedefDetay {
    export const SorguTipiEnum = {
        TelefonGorusme: 'TELEFON_GORUSME',
        ImeiGorusme: 'IMEI_GORUSME',
        ImeiKullananNumara: 'IMEI_KULLANAN_NUMARA'
    } as const;
    export type SorguTipiEnum = typeof SorguTipiEnum[keyof typeof SorguTipiEnum];
}


