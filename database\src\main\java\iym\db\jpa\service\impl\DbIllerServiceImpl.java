package iym.db.jpa.service.impl;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.Iller;
import iym.common.service.db.DbIllerService;
import iym.db.jpa.dao.IllerRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Service implementation for Iller entity
 */
@Service
public class DbIllerServiceImpl extends GenericDbServiceImpl<Iller, String> implements DbIllerService {

    private final IllerRepo illerRepo;

    @Autowired
    public DbIllerServiceImpl(IllerRepo repository) {
        super(repository);
        this.illerRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Iller> findByIlIlceKodu(String ilIlceKodu){
        return illerRepo.findByIlKod(ilIlceKodu);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Iller> findByIlAdi(String ilAdi) {
        return illerRepo.findByIlAdi(ilAdi);
    }


    @Override
    @Transactional(readOnly = true)
    public List<Iller> findByIlKodStartingWith(String ilKodPrefix) {
        return illerRepo.findByIlKodStartingWith(ilKodPrefix);
    }


}
