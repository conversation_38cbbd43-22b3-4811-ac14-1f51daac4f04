import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

// PrimeNG Imports
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { SelectModule } from 'primeng/select';
import { ToastModule } from 'primeng/toast';
import { DialogModule } from 'primeng/dialog';
import { CardModule } from 'primeng/card';
import { TagModule } from 'primeng/tag';
import { ToggleSwitchModule } from 'primeng/toggleswitch';
import { InputTextarea } from 'primeng/inputtextarea';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ToolbarModule } from 'primeng/toolbar';
import { DividerModule } from 'primeng/divider';
import { MessagesModule } from 'primeng/messages';
import { MessageModule } from 'primeng/message';

import { MessageService, ConfirmationService } from 'primeng/api';

// IYM Imports
import { IymService } from '../shared/services/iym.service';
import { ErrorHandlingService } from '../shared/services/error-handling.service';
import { ParametreAramaFiltresi, ParametreSonucu, SistemParametresi } from '../shared/models/iym.models';

@Component({
  selector: 'app-parametre-sorgulama',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TableModule,
    ButtonModule,
    InputTextModule,
    SelectModule,
    ToastModule,
    DialogModule,
    CardModule,
    TagModule,
    ToggleSwitchModule,
    InputTextarea,
    ConfirmDialogModule,
    ToolbarModule,
    DividerModule,
    MessagesModule,
    MessageModule
  ],
  providers: [MessageService, ConfirmationService],
  templateUrl: './parametre-sorgulama.component.html',
  styleUrls: ['./parametre-sorgulama.component.scss']
})
export class ParametreSorgulamaComponent implements OnInit {
  
  // Arama ve Filtreleme
  aramaFiltresi: ParametreAramaFiltresi = {};
  parametreler: ParametreSonucu[] = [];
  filtrelenmisParametreler: ParametreSonucu[] = [];
  
  // UI Durumları
  yukleniyor = false;
  parametreDialogGoruntule = false;
  yeniParametreMi = false;
  
  // Seçili Parametre
  seciliParametre: ParametreSonucu = this.bosParametreOlustur();
  
  // Dropdown Seçenekleri
  parametreGruplari = [
    { label: 'Tümü', value: null },
    { label: 'Dosya Yükleme', value: 'DOSYA_YUKLEME' },
    { label: 'XML İşlemleri', value: 'XML_ISLEMLERI' },
    { label: 'Sistem', value: 'SISTEM' },
    { label: 'Güvenlik', value: 'GUVENLIK' },
    { label: 'Performans', value: 'PERFORMANS' },
    { label: 'Loglama', value: 'LOGLAMA' }
  ];
  
  aktifDurumSecenekleri = [
    { label: 'Tümü', value: null },
    { label: 'Aktif', value: true },
    { label: 'Pasif', value: false }
  ];
  
  // Tablo seçenekleri
  seciliParametreler: ParametreSonucu[] = [];
  
  // Arama metni
  globalAramaMetni = '';

  constructor(
    private iymService: IymService,
    private messageService: MessageService,
    private confirmationService: ConfirmationService,
    private errorHandlingService: ErrorHandlingService
  ) {}

  ngOnInit() {
    this.parametreleriYukle();
  }

  parametreleriYukle() {
    this.yukleniyor = true;
    
    this.iymService.parametreleriGetir(this.aramaFiltresi).subscribe({
      next: (parametreler) => {
        this.parametreler = parametreler;
        this.filtrelenmisParametreler = [...parametreler];
        this.yukleniyor = false;
        
        this.messageService.add({
          severity: 'success',
          summary: 'Başarılı',
          detail: `${parametreler.length} parametre yüklendi`
        });
      },
      error: (hata) => {
        this.errorHandlingService.handleError(hata, () => {
          this.yukleniyor = false;
        });
      }
    });
  }

  parametreAra() {
    this.parametreleriYukle();
  }

  filtreleriTemizle() {
    this.aramaFiltresi = {};
    this.globalAramaMetni = '';
    this.parametreleriYukle();
  }

  globalArama() {
    if (!this.globalAramaMetni.trim()) {
      this.filtrelenmisParametreler = [...this.parametreler];
      return;
    }
    
    const aramaMetni = this.globalAramaMetni.toLowerCase();
    this.filtrelenmisParametreler = this.parametreler.filter(p => 
      p.parametreAdi.toLowerCase().includes(aramaMetni) ||
      p.parametreDegeri.toLowerCase().includes(aramaMetni) ||
      p.parametreGrubu.toLowerCase().includes(aramaMetni) ||
      (p.aciklama && p.aciklama.toLowerCase().includes(aramaMetni))
    );
  }

  yeniParametreEkle() {
    this.seciliParametre = this.bosParametreOlustur();
    this.yeniParametreMi = true;
    this.parametreDialogGoruntule = true;
  }

  parametreDuzenle(parametre: ParametreSonucu) {
    this.seciliParametre = { ...parametre };
    this.yeniParametreMi = false;
    this.parametreDialogGoruntule = true;
  }

  parametreKaydet() {
    if (!this.seciliParametre.parametreAdi.trim()) {
      this.messageService.add({
        severity: 'warn',
        summary: 'Uyarı',
        detail: 'Parametre adı boş olamaz'
      });
      return;
    }

    if (!this.seciliParametre.parametreDegeri.trim()) {
      this.messageService.add({
        severity: 'warn',
        summary: 'Uyarı',
        detail: 'Parametre değeri boş olamaz'
      });
      return;
    }

    this.yukleniyor = true;
    
    this.iymService.parametreGuncelle(this.seciliParametre).subscribe({
      next: (basarili) => {
        if (basarili) {
          if (this.yeniParametreMi) {
            // Yeni parametre ekleme simülasyonu
            this.seciliParametre.id = Date.now();
            this.seciliParametre.olusturmaTarihi = new Date();
            this.parametreler.push({ ...this.seciliParametre });
          } else {
            // Mevcut parametre güncelleme
            const index = this.parametreler.findIndex(p => p.id === this.seciliParametre.id);
            if (index !== -1) {
              this.seciliParametre.guncellemeTarihi = new Date();
              this.parametreler[index] = { ...this.seciliParametre };
            }
          }
          
          this.filtrelenmisParametreler = [...this.parametreler];
          this.parametreDialogKapat();
          this.yukleniyor = false;
          
          this.messageService.add({
            severity: 'success',
            summary: 'Başarılı',
            detail: this.yeniParametreMi ? 'Parametre eklendi' : 'Parametre güncellendi'
          });
        }
      },
      error: (hata) => {
        this.errorHandlingService.handleError(hata, () => {
          this.yukleniyor = false;
        });
      }
    });
  }

  parametreSil(parametre: ParametreSonucu) {
    this.confirmationService.confirm({
      message: `"${parametre.parametreAdi}" parametresini silmek istediğinizden emin misiniz?`,
      header: 'Parametre Silme Onayı',
      icon: 'pi pi-exclamation-triangle',
      acceptLabel: 'Evet',
      rejectLabel: 'Hayır',
      accept: () => {
        // Silme simülasyonu
        const index = this.parametreler.findIndex(p => p.id === parametre.id);
        if (index !== -1) {
          this.parametreler.splice(index, 1);
          this.filtrelenmisParametreler = [...this.parametreler];
          
          this.messageService.add({
            severity: 'success',
            summary: 'Başarılı',
            detail: 'Parametre silindi'
          });
        }
      }
    });
  }

  topluSil() {
    if (this.seciliParametreler.length === 0) {
      this.messageService.add({
        severity: 'warn',
        summary: 'Uyarı',
        detail: 'Silinecek parametre seçiniz'
      });
      return;
    }

    this.confirmationService.confirm({
      message: `${this.seciliParametreler.length} parametreyi silmek istediğinizden emin misiniz?`,
      header: 'Toplu Silme Onayı',
      icon: 'pi pi-exclamation-triangle',
      acceptLabel: 'Evet',
      rejectLabel: 'Hayır',
      accept: () => {
        // Toplu silme simülasyonu
        this.seciliParametreler.forEach(seciliParametre => {
          const index = this.parametreler.findIndex(p => p.id === seciliParametre.id);
          if (index !== -1) {
            this.parametreler.splice(index, 1);
          }
        });
        
        this.filtrelenmisParametreler = [...this.parametreler];
        this.seciliParametreler = [];
        
        this.messageService.add({
          severity: 'success',
          summary: 'Başarılı',
          detail: 'Seçili parametreler silindi'
        });
      }
    });
  }

  parametreDialogKapat() {
    this.parametreDialogGoruntule = false;
    this.seciliParametre = this.bosParametreOlustur();
    this.yeniParametreMi = false;
  }

  bosParametreOlustur(): ParametreSonucu {
    return {
      id: 0,
      parametreAdi: '',
      parametreDegeri: '',
      parametreGrubu: 'SISTEM',
      aciklama: '',
      aktifMi: true,
      olusturmaTarihi: new Date()
    };
  }

  aktifDurumDegistir(parametre: ParametreSonucu) {
    parametre.aktifMi = !parametre.aktifMi;
    parametre.guncellemeTarihi = new Date();
    
    this.iymService.parametreGuncelle(parametre).subscribe({
      next: (basarili) => {
        if (basarili) {
          this.messageService.add({
            severity: 'success',
            summary: 'Başarılı',
            detail: `Parametre ${parametre.aktifMi ? 'aktif' : 'pasif'} edildi`
          });
        }
      },
      error: (hata) => {
        // Hata durumunda eski haline döndür
        parametre.aktifMi = !parametre.aktifMi;
        this.messageService.add({
          severity: 'error',
          summary: 'Hata',
          detail: 'Parametre durumu değiştirilemedi'
        });
      }
    });
  }

  excelAktar() {
    // Excel export işlemi - şimdilik mockup
    this.messageService.add({
      severity: 'info',
      summary: 'Bilgi',
      detail: 'Excel dosyası hazırlanıyor...'
    });
  }

  parametreleriYenile() {
    this.parametreleriYukle();
  }

  durumSeviyesiGetir(aktifMi: boolean): 'success' | 'info' | 'warn' | 'danger' | 'secondary' | 'contrast' {
    return aktifMi ? 'success' : 'danger';
  }

  durumMetniGetir(aktifMi: boolean): string {
    return aktifMi ? 'Aktif' : 'Pasif';
  }

  tarihFormatiDuzelt(tarih: Date | undefined): string {
    if (!tarih) return '-';
    return new Date(tarih).toLocaleDateString('tr-TR');
  }

  grupRengiGetir(grup: string): 'success' | 'info' | 'warn' | 'danger' | 'secondary' | 'contrast' {
    const renkler: { [key: string]: 'success' | 'info' | 'warn' | 'danger' | 'secondary' | 'contrast' } = {
      'DOSYA_YUKLEME': 'info',
      'XML_ISLEMLERI': 'warn',
      'SISTEM': 'success',
      'GUVENLIK': 'danger',
      'PERFORMANS': 'secondary',
      'LOGLAMA': 'info'
    };
    return renkler[grup] || 'secondary';
  }

  // Getter method for template filter expression
  get filtrelenmisParametreGruplari() {
    return this.parametreGruplari.filter(g => g.value !== null);
  }
}
