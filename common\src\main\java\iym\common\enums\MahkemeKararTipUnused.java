package iym.common.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "<PERSON><PERSON><PERSON><PERSON> ka<PERSON> tipi (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)", type = "string", allowableValues = {
        "ONLEYICI_HAKIM_KARARI",
        "SINYAL_BILGI_DEGERLENDIRME_KARARI",
        "ABONE_KUTUK_BILGILERI_KARARI",
        "ONLEYICI_YAZILI_EMIR",
        "ADLI_HAKIM_KARARI",
        "ADLI_HAKIM_HTS_KARARI",
        "ADLI_YAZILI_EMIR",
        "ADLI_KHK_YAZILI_EMIR",
        "ADLI_SAVCILIK_HTS_KARARI",
        "HEDEF_AD_SOYAD_DEGISTIRME",
        "MAHKEME_KODU_DEGISTIRME",
        "MAHKE<PERSON>_AIDIYAT_DEGISTIRME",
        "HEDEF_CANAK_DEGISTIRME",
        "ONLEYICI_SONLANDIRMA",
        "ADLI_SONLANDIRMA",
        "ADLI_SAVCILIK_SONLANDIRMA",
        "ADLI_KHK_SONLANDIRMA",
        "ADLI_ASKERI_HAKIM_KARARI",
        "ADLI_ASKERI_SONLANDIRMA",
        "ADLI_ASKERI_SAVCILIK_SONLANDIRMA"
})
public enum MahkemeKararTipUnused {
    ONLEYICI_HAKIM_KARARI(100),
    SINYAL_BILGI_DEGERLENDIRME_KARARI(150),
    ABONE_KUTUK_BILGILERI_KARARI(151),
    ONLEYICI_YAZILI_EMIR(200),
    ADLI_HAKIM_KARARI(300),
    ADLI_HAKIM_HTS_KARARI(350),
    ADLI_YAZILI_EMIR(400),
    ADLI_KHK_YAZILI_EMIR(410),
    ADLI_SAVCILIK_HTS_KARARI(450),
    HEDEF_AD_SOYAD_DEGISTIRME(510),
    MAHKEME_KODU_DEGISTIRME(520),
    MAHKEME_AIDIYAT_DEGISTIRME(530),
    HEDEF_CANAK_DEGISTIRME(599),
    ONLEYICI_SONLANDIRMA(600),
    ADLI_SONLANDIRMA(700),
    ADLI_SAVCILIK_SONLANDIRMA(710),
    ADLI_KHK_SONLANDIRMA(730),
    ADLI_ASKERI_HAKIM_KARARI(800),
    ADLI_ASKERI_SONLANDIRMA(900);

    private final int kararKodu;

    MahkemeKararTipUnused(int kararKodu) {
        this.kararKodu = kararKodu;
    }

    @JsonValue
    public int getKararKodu() {
        return this.kararKodu;
    }

    @JsonCreator
    public static MahkemeKararTipUnused fromName(String name) {
        for (MahkemeKararTipUnused b : MahkemeKararTipUnused.values()) {
            if (b.name().equals(name)) {
                return b;
            }
        }
        throw new IllegalArgumentException("Gecersiz kararTip: " + name + "'");
    }

    //@JsonCreator
    public static MahkemeKararTipUnused fromValue(int kararKodu) {
        for (MahkemeKararTipUnused b : MahkemeKararTipUnused.values()) {
            if (b.kararKodu == kararKodu) {
                return b;
            }
        }
        throw new IllegalArgumentException("Gecersiz kararKodu: '" + kararKodu + "'");
    }
}
