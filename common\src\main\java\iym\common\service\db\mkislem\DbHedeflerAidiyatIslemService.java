package iym.common.service.db.mkislem;

import iym.common.model.entity.iym.mkislem.HedeflerAidiyatIslem;
import iym.common.service.db.GenericDbService;

import java.util.List;

/**
 * Service interface for HedeflerAidiyatIslem entity
 */
public interface DbHedeflerAidiyatIslemService extends GenericDbService<HedeflerAidiyatIslem, Long> {
    List<HedeflerAidiyatIslem> findByHedeflerIslemId(Long hedefId);

}
