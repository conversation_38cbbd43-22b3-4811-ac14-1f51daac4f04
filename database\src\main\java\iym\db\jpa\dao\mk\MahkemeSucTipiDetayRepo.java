package iym.db.jpa.dao.mk;

import iym.common.model.entity.iym.mk.MahkemeSucTipiDetay;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface MahkemeSucTipiDetayRepo extends JpaRepository<MahkemeSucTipiDetay, Long> {

    List<MahkemeSucTipiDetay> findByMahkemeKararDetayId(Long mahkemeKararDetayId);
    

}
