package iym.common.model.entity.iym;

import jakarta.persistence.*;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "KullaniciKurum")
@Table(name = "KULLANICI_KURUM")
public class KullaniciKurum implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "KULLANICI_KURUM_SEQ")
    @SequenceGenerator(name = "KULLANICI_KURUM_SEQ", sequenceName = "KULLANICI_KURUM_SEQ", allocationSize = 1)
    @Column(name = "ID")
    private Long id;

    @Column(name = "KU<PERSON><PERSON><PERSON>I_ID", nullable = false)
    private Long kullaniciId;

    @Column(name = "KURUM_KOD", nullable = false, length = 10)
    private String kurumKod;
}
