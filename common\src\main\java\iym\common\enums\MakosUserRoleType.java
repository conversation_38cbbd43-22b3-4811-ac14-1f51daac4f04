package iym.common.enums;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "<PERSON>KOS kullanıcı rol türleri", type = "string", allowableValues = {
        "ROLE_ADMIN",
        "ROLE_QUERY_ADMIN",
        "ROLE_KURUM_TEMSILCISI",
        "ROLE_KURUM_KULLANICI"
})
public enum MakosUserRoleType {

    ROLE_ADMIN {
    },
    ROLE_QUERY_ADMIN {
    },
    ROLE_KURUM_TEMSILCISI {
    },
    ROLE_KURUM_KULLANICI {
    };

    /**
     * Check if this role can login to the system
     * @return true if the role can login, false otherwise
     */
    public boolean canLogin(){
        return true;
    }

    /**
     * Check if this role can use impersonate login
     * @return true if the role can use impersonate login, false otherwise
     */
    public boolean canUseImpersonateLogin(){
        return false;
    }

    /**
     * Check if this role can change other users' passwords
     * @return true if the role can change others' passwords, false otherwise
     */
    public boolean canChangeOthersPassword(){
        return false;
    }
}
