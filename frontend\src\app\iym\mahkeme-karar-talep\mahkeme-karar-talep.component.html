<div class="p-4">
    <p-toast></p-toast>

    <div class="flex justify-between items-center mb-4">
        <h1 class="text-2xl font-bold text-gray-800">Mahke<PERSON></h1>
        <div class="flex gap-2">
            <p-button label="Test Verisi Doldur" icon="pi pi-play" severity="info" (onClick)="fillTestDataForSelectedType()" [disabled]="!kararTuru"> </p-button>
            <p-button label="Temizle" icon="pi pi-refresh" severity="secondary" (onClick)="clearForm()"> </p-button>
        </div>
    </div>

    <form [formGroup]="commonForm">
        <!--<p>KararTuru: {{ kararTuru }}</p>-->
        <!--<p>Enum.YENI: {{ KararTuruEnum.IletisiminDenetlenmesiYeniKarar }}</p>-->
        <!--<p>Ka<PERSON><PERSON><PERSON>laştırma: {{ kararTuru === KararTuruEnum.IletisiminDenetlenmesi<PERSON> }}</p>-->
        <!-- <PERSON><PERSON> -->
        <p-card class="mb-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="flex flex-col">
                    <label for="kararTuru" class="text-sm font-medium text-gray-700 mb-1"> Karar Türü <span class="text-red-500">*</span> </label>
                    <p-select
                        [showClear]="true"
                        (onChange)="onKararTuruChanged($event)"
                        formControlName="kararTuru"
                        [options]="kararTuruOptions"
                        optionLabel="label"
                        optionValue="value"
                        placeholder="Karar türü seçiniz"
                        inputId="kararTuru"
                        [class]="isFieldInvalid('kararTuru') ? 'ng-invalid ng-dirty' : ''"
                        class="w-full"
                    >
                    </p-select>
                    <small *ngIf="isFieldInvalid('kararTuru')" class="text-red-500">
                        {{ getFieldError('kararTuru') }}
                    </small>
                </div>
            </div>
        </p-card>
        <p-tabView [(activeIndex)]="activeTabIndex">
            <!-- Ortak Alanlar Tab -->
            <p-tabPanel header="Ortak Alanlar" [selected]="true">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="flex flex-col lg:col-span-1">
                        <!-- Dosya Yükleme -->
                        <p-card header="Dosya Yükleme" class="mb-6">
                            <div class="flex flex-col">
                                <label class="text-sm font-medium text-gray-700 mb-1"> Mahkeme Karar Dosyası </label>
                                <p-fileUpload mode="basic" name="mahkemeKararDosyasi" accept=".pdf,.doc,.docx" maxFileSize="10000000" (onSelect)="onFileSelect($event)" chooseLabel="Dosya Seç" chooseIcon="pi pi-upload" class="w-full"> </p-fileUpload>
                            </div>
                        </p-card>

                        <!-- Mahkeme Karar Detayları -->
                        <p-card header="Mahkeme Karar Detayları">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="flex flex-col">
                                    <label for="mahkemeKararTipi" class="text-sm font-medium text-gray-700 mb-1"> Mahkeme Karar Tipi <span class="text-red-500">*</span> </label>
                                    <p-select
                                        formControlName="mahkemeKararTipi"
                                        [options]="mahkemeKararTipOptions"
                                        optionLabel="label"
                                        optionValue="value"
                                        placeholder="Mahkeme karar tipi seçiniz"
                                        inputId="mahkemeKararTipi"
                                        [loading]="dropdownLoading.mahkemeKararTipleri"
                                        [class]="isFieldInvalid('mahkemeKararTipi') ? 'ng-invalid ng-dirty' : ''"
                                        class="w-full"
                                    >
                                    </p-select>
                                    <small *ngIf="isFieldInvalid('mahkemeKararTipi')" class="text-red-500">
                                        {{ getFieldError('mahkemeKararTipi') }}
                                    </small>
                                </div>

                                <div class="md:col-span-2">
                                    <app-mahkeme-karar-detaylari
                                        formControlName="mahkemeKararDetaylari"
                                        [required]="true"
                                        [mahkemeKodlari]="mahkemeKodlari"
                                        [iller]="iller"
                                        [dropdownLoading]="dropdownLoading">
                                    </app-mahkeme-karar-detaylari>
                                </div>
                            </div>
                        </p-card>
                    </div>

                    <!-- Evrak Detayları -->
                    <p-card header="Evrak Detayları" class="lg:col-span-1">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="flex flex-col">
                                <label for="evrakNo" class="text-sm font-medium text-gray-700 mb-1"> Evrak No <span class="text-red-500">*</span> </label>
                                <input pInputText formControlName="evrakNo" placeholder="Evrak numarası giriniz" [class]="isFieldInvalid('evrakNo') ? 'ng-invalid ng-dirty' : ''" class="w-full" />
                                <small *ngIf="isFieldInvalid('evrakNo')" class="text-red-500">
                                    {{ getFieldError('evrakNo') }}
                                </small>
                            </div>

                            <div class="flex flex-col">
                                <label for="evrakTarihi" class="text-sm font-medium text-gray-700 mb-1"> Evrak Tarihi <span class="text-red-500">*</span> </label>
                                <p-datepicker formControlName="evrakTarihi" dateFormat="dd/mm/yy" [showIcon]="true" [class]="isFieldInvalid('evrakTarihi') ? 'ng-invalid ng-dirty' : ''" class="w-full"> </p-datepicker>
                                <small *ngIf="isFieldInvalid('evrakTarihi')" class="text-red-500">
                                    {{ getFieldError('evrakTarihi') }}
                                </small>
                            </div>

                            <div class="flex flex-col">
                                <label for="evrakKurumKodu" class="text-sm font-medium text-gray-700 mb-1"> Evrak Kurum Kodu <span class="text-red-500">*</span> </label>
                                <input pInputText formControlName="evrakKurumKodu" placeholder="Kurum kodu giriniz" [class]="isFieldInvalid('evrakKurumKodu') ? 'ng-invalid ng-dirty' : ''" class="w-full" />
                                <small *ngIf="isFieldInvalid('evrakKurumKodu')" class="text-red-500">
                                    {{ getFieldError('evrakKurumKodu') }}
                                </small>
                            </div>

                            <div class="flex flex-col">
                                <label for="evrakTuru" class="text-sm font-medium text-gray-700 mb-1"> Evrak Türü <span class="text-red-500">*</span> </label>
                                <p-select
                                    formControlName="evrakTuru"
                                    [options]="evrakTuruOptions"
                                    optionLabel="label"
                                    optionValue="value"
                                    placeholder="Evrak türü seçiniz"
                                    inputId="evrakTuru"
                                    [class]="isFieldInvalid('evrakTuru') ? 'ng-invalid ng-dirty' : ''"
                                    class="w-full"
                                >
                                </p-select>
                                <small *ngIf="isFieldInvalid('evrakTuru')" class="text-red-500">
                                    {{ getFieldError('evrakTuru') }}
                                </small>
                            </div>

                            <div class="flex flex-col">
                                <label for="geldigiIlIlceKodu" class="text-sm font-medium text-gray-700 mb-1"> Geldigi İl/İlçe Kodu <span class="text-red-500">*</span> </label>
                                <p-select
                                    formControlName="geldigiIlIlceKodu"
                                    [options]="iller"
                                    optionLabel="label"
                                    optionValue="value"
                                    placeholder="İl/İlçe seçiniz"
                                    inputId="geldigiIlIlceKodu"
                                    [loading]="dropdownLoading.iller"
                                    [filter]="true"
                                    [class]="isFieldInvalid('geldigiIlIlceKodu') ? 'ng-invalid ng-dirty' : ''"
                                    class="w-full"
                                >
                                </p-select>
                                <small *ngIf="isFieldInvalid('geldigiIlIlceKodu')" class="text-red-500">
                                    {{ getFieldError('geldigiIlIlceKodu') }}
                                </small>
                            </div>

                            <div class="flex flex-col">
                                <label for="havaleBirimi" class="text-sm font-medium text-gray-700 mb-1"> Havale Birimi </label>
                                <input pInputText formControlName="havaleBirimi" placeholder="Havale birimi giriniz" maxlength="10" [class]="isFieldInvalid('havaleBirimi') ? 'ng-invalid ng-dirty' : ''" class="w-full" />
                                <small *ngIf="isFieldInvalid('havaleBirimi')" class="text-red-500">
                                    {{ getFieldError('havaleBirimi') }}
                                </small>
                            </div>
                        </div>

                        <div class="mt-2">
                            <label for="evrakKonusu" class="text-sm font-medium text-gray-700 mb-1 block"> Evrak Konusu </label>
                            <input pInputText formControlName="evrakKonusu" placeholder="Evrak konusu giriniz" class="w-full" />
                        </div>

                        <div class="mt-2">
                            <label for="evrakAciklama" class="text-sm font-medium text-gray-700 mb-1 block"> Evrak Açıklama </label>
                            <textarea pTextarea formControlName="evrakAciklama" placeholder="Evrak hakkında açıklama giriniz" rows="2" class="w-full"> </textarea>
                        </div>

                        <div class="mt-2">
                            <div class="flex items-center">
                                <p-checkbox formControlName="acilmi" binary="true" inputId="acilmi"> </p-checkbox>
                                <label for="acilmi" class="ml-2 text-sm text-gray-700">Acil</label>
                            </div>
                        </div>
                    </p-card>
                </div>
            </p-tabPanel>

            <!-- Method-specific tabs -->
            <p-tabPanel header="ID Yeni Karar" *ngIf="kararTuru === KararTuruEnum.IletisiminDenetlenmesiYeniKarar">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6" [formGroup]="idYeniKararForm">
                    <!-- Suç Tipleri -->
                    <p-card header="Suç Tipleri">
                        <div class="flex flex-col">
                            <label class="text-sm font-medium text-gray-700 mb-2"> Suç Tipleri <span class="text-red-500">*</span> </label>
                            <p-multiSelect
                                formControlName="mahkemeSucTipiKodlari"
                                [options]="sucTipleri"
                                optionLabel="label"
                                optionValue="value"
                                placeholder="Suç tipi seçiniz"
                                [filter]="true"
                                [loading]="dropdownLoading.sucTipleri"
                                display="chip"
                                [showClear]="true"
                                class="w-full"
                                appendTo="body"
                                [maxSelectedLabels]="10"
                            >
                            </p-multiSelect>
                            <small *ngIf="isFieldInvalid('mahkemeSucTipiKodlari', idYeniKararForm)" class="text-red-500">
                                {{ getFieldError('mahkemeSucTipiKodlari', idYeniKararForm) }}
                            </small>
                        </div>
                    </p-card>

                    <!-- Aidiyat Kodları -->
                    <p-card header="Aidiyat Kodları">
                        <div class="flex flex-col">
                            <label class="text-sm font-medium text-gray-700 mb-2"> Aidiyat Kodları </label>
                            <p-multiSelect
                                formControlName="mahkemeAidiyatKodlari"
                                [options]="aidiyatKodlariOptions"
                                [showClear]="true"
                                [filter]="false"
                                [showToggleAll]="false"
                                display="chip"
                                placeholder="Aidiyat kodları seçin veya yeni ekleyin..."
                                class="w-full"
                                [dropdownIcon]="'pi pi-chevron-down'"
                                [style]="{ maxHeight: '200px' }"
                                panelStyle="{maxHeight: '200px', overflow: 'auto'}"
                                appendTo="body"
                                [maxSelectedLabels]="10"
                            >
                                <ng-template pTemplate="header">
                                    <div class="p-2 border-b">
                                        <div class="flex gap-2">
                                            <input
                                                #newAidiyatInput
                                                type="text"
                                                pInputText
                                                placeholder="Yeni aidiyat kodu ekle..."
                                                [(ngModel)]="newAidiyatKodu"
                                                [ngModelOptions]="{ standalone: true }"
                                                (keyup.enter)="addNewAidiyatKodu(); $event.preventDefault(); $event.stopPropagation()"
                                                (click)="$event.stopPropagation()"
                                                class="flex-1"
                                            />
                                            <p-button icon="pi pi-plus" size="small" [disabled]="!newAidiyatKodu.trim()" (onClick)="addNewAidiyatKodu()"> </p-button>
                                        </div>
                                    </div>
                                </ng-template>
                            </p-multiSelect>
                        </div>
                    </p-card>

                    <!-- Hedef Detayları -->
                    <p-card header="Hedef Detayları" class="lg:col-span-2">
                        <div class="flex justify-between items-center mb-4">
                            <span class="text-sm text-gray-600"> Toplam {{ hedefler.length }} hedef </span>
                            <p-button icon="pi pi-plus" label="Hedef Ekle" severity="success" size="small" (onClick)="hedefEkleDialog()"> </p-button>
                        </div>

                        <p-table [value]="hedefler" responsiveLayout="scroll" styleClass="p-datatable-sm" *ngIf="hedefler.length > 0">
                            <ng-template pTemplate="header">
                                <tr>
                                    <th>Hedef No</th>
                                    <th>Hedef Tipi</th>
                                    <th>Ad Soyad</th>
                                    <th>Başlama Tarihi</th>
                                    <th>Süre</th>
                                    <th>Hedef Aidiyat Kodları</th>
                                    <th>İşlemler</th>
                                </tr>
                            </ng-template>

                            <ng-template pTemplate="body" let-hedef let-i="rowIndex">
                                <tr>
                                    <td>{{ hedef.hedefNo }}</td>
                                    <td>{{ getHedefTipLabel(hedef.hedefTip) }}</td>
                                    <td>{{ hedef.hedefAd }} {{ hedef.hedefSoyad }}</td>
                                    <td>{{ formatTarih(hedef.baslamaTarihi) }}</td>
                                    <td>{{ hedef.sure }} {{ getSureTipiLabel(hedef.sureTip) }}</td>
                                    <td>
                                        <p-chip *ngFor="let kod of hedef.hedefAidiyatKodlari" [label]="kod" class="mr-1"></p-chip>
                                    </td>
                                    <td>
                                        <div class="flex gap-1">
                                            <p-button icon="pi pi-pencil" size="small" severity="info" (onClick)="hedefDuzenleDialog(i)" pTooltip="Düzenle" tooltipPosition="top"> </p-button>
                                            <p-button icon="pi pi-trash" size="small" severity="danger" (onClick)="hedefSil(i)" pTooltip="Sil" tooltipPosition="top"> </p-button>
                                        </div>
                                    </td>
                                </tr>
                            </ng-template>

                            <ng-template pTemplate="emptymessage">
                                <tr>
                                    <td colspan="7" class="text-center py-8">
                                        <i class="pi pi-info-circle text-4xl text-gray-400 mb-2"></i>
                                        <p class="text-gray-500">Henüz hedef eklenmedi.</p>
                                    </td>
                                </tr>
                            </ng-template>
                        </p-table>

                        <div *ngIf="hedefler.length === 0" class="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
                            <i class="pi pi-info-circle text-4xl text-gray-400 mb-2"></i>
                            <p class="text-gray-500 mb-4">Henüz hedef eklenmedi.</p>
                            <p-button icon="pi pi-plus" label="İlk Hedefi Ekle" severity="success" (onClick)="hedefEkleDialog()"> </p-button>
                        </div>
                    </p-card>
                </div>
            </p-tabPanel>

            <p-tabPanel header="ID Uzatma Kararı" *ngIf="kararTuru === KararTuruEnum.IletisiminDenetlenmesiUzatmaKarari">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6" [formGroup]="idUzatmaKarariForm">
                    <!-- Suç Tipi Kodları -->
                    <p-card header="Suç Tipi Kodları">
                        <div class="flex flex-col">
                            <label class="text-sm font-medium text-gray-700 mb-2"> Suç Tipi Kodları </label>
                            <p-multiSelect
                                formControlName="mahkemeSucTipiKodlari"
                                [options]="sucTipleri"
                                optionLabel="label"
                                optionValue="value"
                                [showClear]="true"
                                [filter]="false"
                                [showToggleAll]="false"
                                display="chip"
                                placeholder="Suç tipi kodları seçin veya yeni ekleyin..."
                                class="w-full"
                                [dropdownIcon]="'pi pi-chevron-down'"
                                [style]="{ maxHeight: '200px' }"
                                panelStyle="{maxHeight: '200px', overflow: 'auto'}"
                                appendTo="body"
                                [maxSelectedLabels]="10"
                            >
                                <ng-template pTemplate="header">
                                    <div class="p-2 border-b">
                                        <div class="flex gap-2">
                                            <input
                                                #newSucTipiInputUzatma
                                                type="text"
                                                pInputText
                                                placeholder="Yeni suç tipi kodu ekle..."
                                                [(ngModel)]="newSucTipiKodu"
                                                [ngModelOptions]="{ standalone: true }"
                                                (keyup.enter)="addNewSucTipiKodu(); $event.preventDefault(); $event.stopPropagation()"
                                                (click)="$event.stopPropagation()"
                                                class="flex-1"
                                            />
                                            <p-button icon="pi pi-plus" size="small" [disabled]="!newSucTipiKodu.trim()" (onClick)="addNewSucTipiKodu()"> </p-button>
                                        </div>
                                    </div>
                                </ng-template>
                            </p-multiSelect>
                            <small *ngIf="isFieldInvalid('mahkemeSucTipiKodlari', idUzatmaKarariForm)" class="text-red-500">
                                {{ getFieldError('mahkemeSucTipiKodlari', idUzatmaKarariForm) }}
                            </small>
                        </div>
                    </p-card>

                    <!-- Aidiyat Kodları -->
                    <p-card header="Aidiyat Kodları">
                        <div class="flex flex-col">
                            <label class="text-sm font-medium text-gray-700 mb-2"> Aidiyat Kodları </label>
                            <p-multiSelect
                                formControlName="mahkemeAidiyatKodlari"
                                [options]="aidiyatKodlariOptions"
                                [showClear]="true"
                                [filter]="false"
                                [showToggleAll]="false"
                                display="chip"
                                placeholder="Aidiyat kodları seçin veya yeni ekleyin..."
                                class="w-full"
                                [dropdownIcon]="'pi pi-chevron-down'"
                                [style]="{ maxHeight: '200px' }"
                                panelStyle="{maxHeight: '200px', overflow: 'auto'}"
                                appendTo="body"
                                [maxSelectedLabels]="10"
                            >
                                <ng-template pTemplate="header">
                                    <div class="p-2 border-b">
                                        <div class="flex gap-2">
                                            <input
                                                #newAidiyatInputUzatma
                                                type="text"
                                                pInputText
                                                placeholder="Yeni aidiyat kodu ekle..."
                                                [(ngModel)]="newAidiyatKodu"
                                                [ngModelOptions]="{ standalone: true }"
                                                (keyup.enter)="addNewAidiyatKodu(); $event.preventDefault(); $event.stopPropagation()"
                                                (click)="$event.stopPropagation()"
                                                class="flex-1"
                                            />
                                            <p-button icon="pi pi-plus" size="small" [disabled]="!newAidiyatKodu.trim()" (onClick)="addNewAidiyatKodu()"> </p-button>
                                        </div>
                                    </div>
                                </ng-template>
                            </p-multiSelect>
                        </div>
                    </p-card>

                    <!-- Hedef Detayları -->
                    <p-card header="Hedef Detayları" class="lg:col-span-2">
                        <div class="flex justify-between items-center mb-4">
                            <span class="text-sm text-gray-600"> Toplam {{ hedefler.length }} hedef </span>
                            <p-button icon="pi pi-plus" label="Hedef Ekle" severity="success" size="small" (onClick)="hedefEkleDialog()"> </p-button>
                        </div>

                        <p-table [value]="hedefler" responsiveLayout="scroll" styleClass="p-datatable-sm" *ngIf="hedefler.length > 0">
                            <ng-template pTemplate="header">
                                <tr>
                                    <th>Hedef No</th>
                                    <th>Hedef Tipi</th>
                                    <th>Ad Soyad</th>
                                    <th>Başlama Tarihi</th>
                                    <th>Süre</th>
                                    <th>Hedef Aidiyat Kodları</th>
                                    <th>İşlemler</th>
                                </tr>
                            </ng-template>

                            <ng-template pTemplate="body" let-hedef let-i="rowIndex">
                                <tr>
                                    <td>{{ hedef.hedefNo }}</td>
                                    <td>{{ getHedefTipLabel(hedef.hedefTip) }}</td>
                                    <td>{{ hedef.hedefAd }} {{ hedef.hedefSoyad }}</td>
                                    <td>{{ formatTarih(hedef.baslamaTarihi) }}</td>
                                    <td>{{ hedef.sure }} {{ getSureTipiLabel(hedef.sureTip) }}</td>
                                    <td>
                                        <p-chip *ngFor="let kod of hedef.hedefAidiyatKodlari" [label]="kod" class="mr-1"></p-chip>
                                    </td>
                                    <td>
                                        <div class="flex gap-1">
                                            <p-button icon="pi pi-pencil" size="small" severity="info" (onClick)="hedefDuzenleDialog(i)" pTooltip="Düzenle" tooltipPosition="top"> </p-button>
                                            <p-button icon="pi pi-trash" size="small" severity="danger" (onClick)="hedefSil(i)" pTooltip="Sil" tooltipPosition="top"> </p-button>
                                        </div>
                                    </td>
                                </tr>
                            </ng-template>

                            <ng-template pTemplate="emptymessage">
                                <tr>
                                    <td colspan="7" class="text-center py-8">
                                        <i class="pi pi-info-circle text-4xl text-gray-400 mb-2"></i>
                                        <p class="text-gray-500">Henüz hedef eklenmedi.</p>
                                    </td>
                                </tr>
                            </ng-template>
                        </p-table>

                        <div *ngIf="hedefler.length === 0" class="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
                            <i class="pi pi-info-circle text-4xl text-gray-400 mb-2"></i>
                            <p class="text-gray-500 mb-4">Henüz hedef eklenmedi.</p>
                            <p-button icon="pi pi-plus" label="İlk Hedefi Ekle" severity="success" (onClick)="hedefEkleDialog()"> </p-button>
                        </div>
                    </p-card>
                </div>
            </p-tabPanel>

            <p-tabPanel header="ID Sonlandırma Kararı" *ngIf="kararTuru === KararTuruEnum.IletisiminDenetlenmesiSonlandirmaKarari">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6" [formGroup]="idSonlandirmaKarariForm">
                    <!-- Suç Tipi Kodları -->
                    <p-card header="Suç Tipi Kodları">
                        <div class="flex flex-col">
                            <label class="text-sm font-medium text-gray-700 mb-2"> Suç Tipi Kodları </label>
                            <p-multiSelect
                                formControlName="mahkemeSucTipiKodlari"
                                [options]="sucTipleri"
                                optionLabel="label"
                                optionValue="value"
                                [showClear]="true"
                                [filter]="false"
                                [showToggleAll]="false"
                                display="chip"
                                placeholder="Suç tipi kodları seçin veya yeni ekleyin..."
                                class="w-full"
                                [dropdownIcon]="'pi pi-chevron-down'"
                                [style]="{ maxHeight: '200px' }"
                                panelStyle="{maxHeight: '200px', overflow: 'auto'}"
                                appendTo="body"
                                [maxSelectedLabels]="10"
                            >
                                <ng-template pTemplate="header">
                                    <div class="p-2 border-b">
                                        <div class="flex gap-2">
                                            <input
                                                #newSucTipiInputSonlandirma
                                                type="text"
                                                pInputText
                                                placeholder="Yeni suç tipi kodu ekle..."
                                                [(ngModel)]="newSucTipiKodu"
                                                [ngModelOptions]="{ standalone: true }"
                                                (keyup.enter)="addNewSucTipiKodu(); $event.preventDefault(); $event.stopPropagation()"
                                                (click)="$event.stopPropagation()"
                                                class="flex-1"
                                            />
                                            <p-button icon="pi pi-plus" size="small" [disabled]="!newSucTipiKodu.trim()" (onClick)="addNewSucTipiKodu()"> </p-button>
                                        </div>
                                    </div>
                                </ng-template>
                            </p-multiSelect>
                            <small *ngIf="isFieldInvalid('mahkemeSucTipiKodlari', idSonlandirmaKarariForm)" class="text-red-500">
                                {{ getFieldError('mahkemeSucTipiKodlari', idSonlandirmaKarariForm) }}
                            </small>
                        </div>
                    </p-card>

                    <!-- Aidiyat Kodları -->
                    <p-card header="Aidiyat Kodları">
                        <div class="flex flex-col">
                            <label class="text-sm font-medium text-gray-700 mb-2"> Aidiyat Kodları </label>
                            <p-multiSelect
                                formControlName="mahkemeAidiyatKodlari"
                                [options]="aidiyatKodlariOptions"
                                [showClear]="true"
                                [filter]="false"
                                [showToggleAll]="false"
                                display="chip"
                                placeholder="Aidiyat kodları seçin veya yeni ekleyin..."
                                class="w-full"
                                [dropdownIcon]="'pi pi-chevron-down'"
                                [style]="{ maxHeight: '200px' }"
                                panelStyle="{maxHeight: '200px', overflow: 'auto'}"
                                appendTo="body"
                                [maxSelectedLabels]="10"
                            >
                                <ng-template pTemplate="header">
                                    <div class="p-2 border-b">
                                        <div class="flex gap-2">
                                            <input
                                                #newAidiyatInputSonlandirma
                                                type="text"
                                                pInputText
                                                placeholder="Yeni aidiyat kodu ekle..."
                                                [(ngModel)]="newAidiyatKodu"
                                                [ngModelOptions]="{ standalone: true }"
                                                (keyup.enter)="addNewAidiyatKodu(); $event.preventDefault(); $event.stopPropagation()"
                                                (click)="$event.stopPropagation()"
                                                class="flex-1"
                                            />
                                            <p-button icon="pi pi-plus" size="small" [disabled]="!newAidiyatKodu.trim()" (onClick)="addNewAidiyatKodu()"> </p-button>
                                        </div>
                                    </div>
                                </ng-template>
                            </p-multiSelect>
                        </div>
                    </p-card>

                    <!-- Hedef Detayları -->
                    <p-card header="Hedef Detayları" class="lg:col-span-2">
                        <div class="flex justify-between items-center mb-4">
                            <span class="text-sm text-gray-600"> Toplam {{ hedefler.length }} hedef </span>
                            <p-button icon="pi pi-plus" label="Hedef Ekle" severity="success" size="small" (onClick)="hedefEkleDialog()"> </p-button>
                        </div>

                        <p-table [value]="hedefler" responsiveLayout="scroll" styleClass="p-datatable-sm" *ngIf="hedefler.length > 0">
                            <ng-template pTemplate="header">
                                <tr>
                                    <th>Hedef No</th>
                                    <th>Hedef Tipi</th>
                                    <th>Ad Soyad</th>
                                    <th>Başlama Tarihi</th>
                                    <th>Süre</th>
                                    <th>Hedef Aidiyat Kodları</th>
                                    <th>İşlemler</th>
                                </tr>
                            </ng-template>

                            <ng-template pTemplate="body" let-hedef let-i="rowIndex">
                                <tr>
                                    <td>{{ hedef.hedefNo }}</td>
                                    <td>{{ getHedefTipLabel(hedef.hedefTip) }}</td>
                                    <td>{{ hedef.hedefAd }} {{ hedef.hedefSoyad }}</td>
                                    <td>{{ formatTarih(hedef.baslamaTarihi) }}</td>
                                    <td>{{ hedef.sure }} {{ getSureTipiLabel(hedef.sureTip) }}</td>
                                    <td>
                                        <p-chip *ngFor="let kod of hedef.hedefAidiyatKodlari" [label]="kod" class="mr-1"></p-chip>
                                    </td>
                                    <td>
                                        <div class="flex gap-1">
                                            <p-button icon="pi pi-pencil" size="small" severity="info" (onClick)="hedefDuzenleDialog(i)" pTooltip="Düzenle" tooltipPosition="top"> </p-button>
                                            <p-button icon="pi pi-trash" size="small" severity="danger" (onClick)="hedefSil(i)" pTooltip="Sil" tooltipPosition="top"> </p-button>
                                        </div>
                                    </td>
                                </tr>
                            </ng-template>

                            <ng-template pTemplate="emptymessage">
                                <tr>
                                    <td colspan="7" class="text-center py-8">
                                        <i class="pi pi-info-circle text-4xl text-gray-400 mb-2"></i>
                                        <p class="text-gray-500">Henüz hedef eklenmedi.</p>
                                    </td>
                                </tr>
                            </ng-template>
                        </p-table>

                        <div *ngIf="hedefler.length === 0" class="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
                            <i class="pi pi-info-circle text-4xl text-gray-400 mb-2"></i>
                            <p class="text-gray-500 mb-4">Henüz hedef eklenmedi.</p>
                            <p-button icon="pi pi-plus" label="İlk Hedefi Ekle" severity="success" (onClick)="hedefEkleDialog()"> </p-button>
                        </div>
                    </p-card>
                </div>
            </p-tabPanel>

            <p-tabPanel header="ID Hedef Güncelleme" *ngIf="kararTuru === KararTuruEnum.IletisiminDenetlenmesiHedefGuncelleme">
                <div class="grid grid-cols-1 gap-6" [formGroup]="idHedefGuncellemeForm">
                    <!-- Hedef Güncelleme Karar Detayları -->
                    <p-card header="Hedef Güncelleme Karar Detayları" class="w-full">
                        <div class="flex justify-between items-center mb-4">
                            <span class="text-sm text-gray-600"> Toplam {{ hedefGuncellemeKararDetayListesi.length }} karar detayı </span>
                            <p-button icon="pi pi-plus" label="Karar Detayı Ekle" severity="success" size="small" (onClick)="hedefGuncellemeKararDetayEkleDialog()"> </p-button>
                        </div>

                        <p-table [value]="hedefGuncellemeKararDetayListesi" responsiveLayout="scroll" styleClass="p-datatable-sm" *ngIf="hedefGuncellemeKararDetayListesi.length > 0">
                            <ng-template pTemplate="header">
                                <tr>
                                    <th>Mahkeme Kodu</th>
                                    <th>Mahkeme Karar No</th>
                                    <th>Soruşturma No</th>
                                    <th>Güncelleme Detayları</th>
                                    <th>İşlemler</th>
                                </tr>
                            </ng-template>

                            <ng-template pTemplate="body" let-kararDetay let-i="rowIndex">
                                <tr>
                                    <td>{{ kararDetay.mahkemeKararDetay?.mahkemeKodu }}</td>
                                    <td>{{ kararDetay.mahkemeKararDetay?.mahkemeKararNo }}</td>
                                    <td>{{ kararDetay.mahkemeKararDetay?.sorusturmaNo }}</td>
                                    <td>
                                        <div *ngFor="let detay of kararDetay.hedefGuncellemeDetayListesi" class="mb-2">
                                            <strong>{{ detay.hedef?.hedefNo }} ({{ detay.hedef?.hedefTip }}):</strong>
                                            <div *ngFor="let bilgi of detay.hedefGuncellemeBilgiListesi" class="ml-4 text-sm">
                                                {{ getHedefGuncellemeAlanLabel(bilgi.hedefGuncellemeAlan) }}: {{ bilgi.yeniDegeri }}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="flex gap-1">
                                            <p-button icon="pi pi-pencil" size="small" severity="info" (onClick)="hedefGuncellemeKararDetayDuzenleDialog(i)" pTooltip="Düzenle" tooltipPosition="top"> </p-button>
                                            <p-button icon="pi pi-trash" size="small" severity="danger" (onClick)="hedefGuncellemeKararDetaySil(i)" pTooltip="Sil" tooltipPosition="top"> </p-button>
                                        </div>
                                    </td>
                                </tr>
                            </ng-template>

                            <ng-template pTemplate="emptymessage">
                                <tr>
                                    <td colspan="5" class="text-center py-8">
                                        <i class="pi pi-info-circle text-4xl text-gray-400 mb-2"></i>
                                        <p class="text-gray-500">Henüz hedef güncelleme karar detayı eklenmedi.</p>
                                    </td>
                                </tr>
                            </ng-template>
                        </p-table>

                        <div *ngIf="hedefGuncellemeKararDetayListesi.length === 0" class="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
                            <i class="pi pi-info-circle text-4xl text-gray-400 mb-2"></i>
                            <p class="text-gray-500 mb-4">Henüz hedef güncelleme karar detayı eklenmedi.</p>
                            <p-button icon="pi pi-plus" label="İlk Karar Detayını Ekle" severity="success" (onClick)="hedefGuncellemeKararDetayEkleDialog()"> </p-button>
                        </div>
                    </p-card>
                </div>
            </p-tabPanel>

            <p-tabPanel header="ID Mahkeme Karar Güncelleme" *ngIf="kararTuru === KararTuruEnum.IletisiminDenetlenmesiMahkemekararGuncelleme">
                <div class="grid grid-cols-1 gap-6" [formGroup]="idMahkemeKararGuncellemeForm">
                    <!-- Mahkeme Karar Güncelleme Detayları -->
                    <p-card header="Mahkeme Karar Güncelleme Detayları" class="w-full">
                        <div class="flex justify-between items-center mb-4">
                            <span class="text-sm text-gray-600"> Toplam {{ mahkemeKararGuncellemeDetayListesi.length }} güncelleme detayı </span>
                            <p-button icon="pi pi-plus" label="Güncelleme Detayı Ekle" severity="success" size="small" (onClick)="mahkemeKararGuncellemeDetayEkleDialog()"> </p-button>
                        </div>

                        <p-table [value]="mahkemeKararGuncellemeDetayListesi" responsiveLayout="scroll" styleClass="p-datatable-sm" *ngIf="mahkemeKararGuncellemeDetayListesi.length > 0">
                            <ng-template pTemplate="header">
                                <tr>
                                    <th>Mahkeme Kodu</th>
                                    <th>Mahkeme Karar No</th>
                                    <th>Soruşturma No</th>
                                    <th>Güncelleme Bilgileri</th>
                                    <th>İşlemler</th>
                                </tr>
                            </ng-template>

                            <ng-template pTemplate="body" let-guncellemeDetay let-i="rowIndex">
                                <tr>
                                    <td>{{ guncellemeDetay.mahkemeKararDetay?.mahkemeKodu }}</td>
                                    <td>{{ guncellemeDetay.mahkemeKararDetay?.mahkemeKararNo }}</td>
                                    <td>{{ guncellemeDetay.mahkemeKararDetay?.sorusturmaNo }}</td>
                                    <td>
                                        <div *ngFor="let bilgi of guncellemeDetay.mahkemeKararGuncellemeBilgiListesi" class="mb-1">
                                            <span class="text-sm">
                                                <strong>{{ getMahkemeKararGuncellemeAlanLabel(bilgi.mahkemeKararGuncellemeAlanTuru) }}:</strong> {{ bilgi.yeniDegeri }}
                                            </span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="flex gap-1">
                                            <p-button icon="pi pi-pencil" size="small" severity="info" (onClick)="mahkemeKararGuncellemeDetayDuzenleDialog(i)" pTooltip="Düzenle" tooltipPosition="top"> </p-button>
                                            <p-button icon="pi pi-trash" size="small" severity="danger" (onClick)="mahkemeKararGuncellemeDetaySil(i)" pTooltip="Sil" tooltipPosition="top"> </p-button>
                                        </div>
                                    </td>
                                </tr>
                            </ng-template>

                            <ng-template pTemplate="emptymessage">
                                <tr>
                                    <td colspan="5" class="text-center py-8">
                                        <i class="pi pi-info-circle text-4xl text-gray-400 mb-2"></i>
                                        <p class="text-gray-500">Henüz mahkeme karar güncelleme detayı eklenmedi.</p>
                                    </td>
                                </tr>
                            </ng-template>
                        </p-table>

                        <div *ngIf="mahkemeKararGuncellemeDetayListesi.length === 0" class="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
                            <i class="pi pi-info-circle text-4xl text-gray-400 mb-2"></i>
                            <p class="text-gray-500 mb-4">Henüz mahkeme karar güncelleme detayı eklenmedi.</p>
                            <p-button icon="pi pi-plus" label="İlk Güncelleme Detayını Ekle" severity="success" (onClick)="mahkemeKararGuncellemeDetayEkleDialog()"> </p-button>
                        </div>
                    </p-card>
                </div>
            </p-tabPanel>

            <p-tabPanel header="ID Aidiyat Güncelleme" *ngIf="kararTuru === KararTuruEnum.IletisiminDenetlenmesiAidiyatGuncelleme">
                <div class="grid grid-cols-1 gap-6" [formGroup]="idAidiyatGuncellemeForm">
                    <!-- Aidiyat Güncelleme Karar Detayları -->
                    <p-card header="Aidiyat Güncelleme Karar Detayları" class="w-full">
                        <div class="flex justify-between items-center mb-4">
                            <span class="text-sm text-gray-600"> Toplam {{ aidiyatGuncellemeKararDetayListesi.length }} aidiyat güncelleme detayı </span>
                            <p-button icon="pi pi-plus" label="Aidiyat Güncelleme Detayı Ekle" severity="success" size="small" (onClick)="aidiyatGuncellemeKararDetayEkleDialog()"> </p-button>
                        </div>

                        <p-table [value]="aidiyatGuncellemeKararDetayListesi" responsiveLayout="scroll" styleClass="p-datatable-sm" *ngIf="aidiyatGuncellemeKararDetayListesi.length > 0">
                            <ng-template pTemplate="header">
                                <tr>
                                    <th>Mahkeme Kodu</th>
                                    <th>Mahkeme Karar No</th>
                                    <th>Soruşturma No</th>
                                    <th>Aidiyat Güncelleme Bilgileri</th>
                                    <th>İşlemler</th>
                                </tr>
                            </ng-template>

                            <ng-template pTemplate="body" let-aidiyatDetay let-i="rowIndex">
                                <tr>
                                    <td>{{ aidiyatDetay.mahkemeKararDetay?.mahkemeKodu }}</td>
                                    <td>{{ aidiyatDetay.mahkemeKararDetay?.mahkemeKararNo }}</td>
                                    <td>{{ aidiyatDetay.mahkemeKararDetay?.sorusturmaNo }}</td>
                                    <td>
                                        <div *ngFor="let bilgi of aidiyatDetay.aidiyatGuncellemeBilgiListesi" class="mb-1">
                                            <span class="text-sm">
                                                <strong>{{ getAidiyatGuncellemeAlanLabel(bilgi.aidiyatGuncellemeAlanTuru) }}:</strong> {{ bilgi.yeniDegeri }}
                                            </span>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="flex gap-1">
                                            <p-button icon="pi pi-pencil" size="small" severity="info" (onClick)="aidiyatGuncellemeKararDetayDuzenleDialog(i)" pTooltip="Düzenle" tooltipPosition="top"> </p-button>
                                            <p-button icon="pi pi-trash" size="small" severity="danger" (onClick)="aidiyatGuncellemeKararDetaySil(i)" pTooltip="Sil" tooltipPosition="top"> </p-button>
                                        </div>
                                    </td>
                                </tr>
                            </ng-template>

                            <ng-template pTemplate="emptymessage">
                                <tr>
                                    <td colspan="5" class="text-center py-8">
                                        <i class="pi pi-info-circle text-4xl text-gray-400 mb-2"></i>
                                        <p class="text-gray-500">Henüz aidiyat güncelleme karar detayı eklenmedi.</p>
                                    </td>
                                </tr>
                            </ng-template>
                        </p-table>

                        <div *ngIf="aidiyatGuncellemeKararDetayListesi.length === 0" class="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
                            <i class="pi pi-info-circle text-4xl text-gray-400 mb-2"></i>
                            <p class="text-gray-500 mb-4">Henüz aidiyat güncelleme karar detayı eklenmedi.</p>
                            <p-button icon="pi pi-plus" label="İlk Aidiyat Güncelleme Detayını Ekle" severity="success" (onClick)="aidiyatGuncellemeKararDetayEkleDialog()"> </p-button>
                        </div>
                    </p-card>
                </div>
            </p-tabPanel>

            <p-tabPanel header="IT Karar" *ngIf="kararTuru === KararTuruEnum.IletisiminTespiti">
                <div class="grid grid-cols-1 gap-6" [formGroup]="itKararForm">
                    <!-- IT Hedef Detayları -->
                    <p-card header="İletişimin Tespiti Hedef Detayları" class="w-full">
                        <div class="flex justify-between items-center mb-4">
                            <span class="text-sm text-gray-600"> Toplam {{ itHedefDetayListesi.length }} IT hedef detayı </span>
                            <p-button icon="pi pi-plus" label="IT Hedef Detayı Ekle" severity="success" size="small" (onClick)="itHedefDetayEkleDialog()"> </p-button>
                        </div>

                        <p-table [value]="itHedefDetayListesi" responsiveLayout="scroll" styleClass="p-datatable-sm" *ngIf="itHedefDetayListesi.length > 0">
                            <ng-template pTemplate="header">
                                <tr>
                                    <th>Sorgu Tipi</th>
                                    <th>Hedef</th>
                                    <th>Karşı Hedef</th>
                                    <th>Başlama Tarihi</th>
                                    <th>Bitiş Tarihi</th>
                                    <th>Tespit Türü</th>
                                    <th>Açıklama</th>
                                    <th>İşlemler</th>
                                </tr>
                            </ng-template>

                            <ng-template pTemplate="body" let-itHedef let-i="rowIndex">
                                <tr>
                                    <td>{{ getSorguTipiLabel(itHedef.sorguTipi) }}</td>
                                    <td>
                                        <div>
                                            <strong>{{ itHedef.hedef?.hedefNo }}</strong>
                                            <br>
                                            <small>{{ getHedefTipLabel(itHedef.hedef?.hedefTip) }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div *ngIf="itHedef.karsiHedef">
                                            <strong>{{ itHedef.karsiHedef?.hedefNo }}</strong>
                                            <br>
                                            <small>{{ getHedefTipLabel(itHedef.karsiHedef?.hedefTip) }}</small>
                                        </div>
                                        <span *ngIf="!itHedef.karsiHedef" class="text-gray-400">-</span>
                                    </td>
                                    <td>{{ formatTarih(itHedef.baslamaTarihi) }}</td>
                                    <td>{{ formatTarih(itHedef.bitisTarihi) }}</td>
                                    <td>
                                        <div>
                                            {{ itHedef.tespitTuru }}
                                            <div *ngIf="itHedef.tespitTuruDetay" class="text-sm text-gray-600">
                                                Detay: {{ itHedef.tespitTuruDetay }}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span *ngIf="itHedef.aciklama" class="text-sm">{{ itHedef.aciklama }}</span>
                                        <span *ngIf="!itHedef.aciklama" class="text-gray-400">-</span>
                                    </td>
                                    <td>
                                        <div class="flex gap-1">
                                            <p-button icon="pi pi-pencil" size="small" severity="info" (onClick)="itHedefDetayDuzenleDialog(i)" pTooltip="Düzenle" tooltipPosition="top"> </p-button>
                                            <p-button icon="pi pi-trash" size="small" severity="danger" (onClick)="itHedefDetaySil(i)" pTooltip="Sil" tooltipPosition="top"> </p-button>
                                        </div>
                                    </td>
                                </tr>
                            </ng-template>

                            <ng-template pTemplate="emptymessage">
                                <tr>
                                    <td colspan="8" class="text-center py-8">
                                        <i class="pi pi-info-circle text-4xl text-gray-400 mb-2"></i>
                                        <p class="text-gray-500">Henüz IT hedef detayı eklenmedi.</p>
                                    </td>
                                </tr>
                            </ng-template>
                        </p-table>

                        <div *ngIf="itHedefDetayListesi.length === 0" class="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
                            <i class="pi pi-info-circle text-4xl text-gray-400 mb-2"></i>
                            <p class="text-gray-500 mb-4">Henüz IT hedef detayı eklenmedi.</p>
                            <p-button icon="pi pi-plus" label="İlk IT Hedef Detayını Ekle" severity="success" (onClick)="itHedefDetayEkleDialog()"> </p-button>
                        </div>
                    </p-card>
                </div>
            </p-tabPanel>
        </p-tabView>

        <!-- Action Buttons -->
        <div class="flex justify-center gap-3 mt-2 flex-wrap">
            <p-button
                label="JSON"
                icon="pi pi-file-export"
                severity="secondary"
                (onClick)="showJsonImportExport()"
                class="w-28"
                pTooltip="JSON İçe/Dışa Aktar"
                tooltipPosition="top"
            >
            </p-button>
            <p-button
                label="Önizle"
                icon="pi pi-eye"
                severity="info"
                (onClick)="showPreview()"
                [disabled]="!commonForm.valid"
                class="w-28"
            >
            </p-button>
            <p-button
                label="Gönder"
                icon="pi pi-send"
                severity="success"
                (onClick)="onSubmit()"
                [loading]="loading"
                [disabled]="!commonForm.valid"
                class="w-28"
            >
            </p-button>
        </div>
    </form>

    <!-- Preview Component -->
    <app-mahkeme-karar-preview
        [(visible)]="previewVisible"
        [formData]="getPreviewData()"
        [kararTuru]="kararTuru"
        [seciliDosya]="seciliDosya"
        (onConfirm)="onSubmit()"
    >
    </app-mahkeme-karar-preview>

    <!-- JSON Import/Export Component -->
    <app-json-import-export
        [(visible)]="jsonImportExportVisible"
        [formData]="getPreviewData()"
        [kararTuru]="kararTuru"
        (onImport)="importFromJson($event)"
    >
    </app-json-import-export>
</div>

<!-- Test Verisi Butonları -->
<div class="fixed bottom-4 right-4 flex flex-col gap-2" *ngIf="kararTuru">
    <p-button *ngIf="kararTuru === KararTuruEnum.IletisiminDenetlenmesiYeniKarar" label="ID Yeni Karar Test Verisi" icon="pi pi-play" severity="info" (onClick)="fillIDYeniKararTestData()" [rounded]="true" size="small"> </p-button>

    <p-button *ngIf="kararTuru === KararTuruEnum.IletisiminDenetlenmesiUzatmaKarari" label="ID Uzatma Kararı Test Verisi" icon="pi pi-play" severity="info" (onClick)="fillIDUzatmaKarariTestData()" [rounded]="true" size="small"> </p-button>

    <p-button *ngIf="kararTuru === KararTuruEnum.IletisiminDenetlenmesiSonlandirmaKarari" label="ID Sonlandırma Kararı Test Verisi" icon="pi pi-play" severity="info" (onClick)="fillIDSonlandirmaKarariTestData()" [rounded]="true" size="small">
    </p-button>

    <p-button *ngIf="kararTuru === KararTuruEnum.IletisiminDenetlenmesiHedefGuncelleme" label="ID Hedef Güncelleme Test Verisi" icon="pi pi-play" severity="info" (onClick)="fillIDHedefGuncellemeTestData()" [rounded]="true" size="small"> </p-button>

    <p-button
        *ngIf="kararTuru === KararTuruEnum.IletisiminDenetlenmesiMahkemekararGuncelleme"
        label="ID Mahkeme Karar Güncelleme Test Verisi"
        icon="pi pi-play"
        severity="info"
        (onClick)="fillIDMahkemeKararGuncellemeTestData()"
        [rounded]="true"
        size="small"
    >
    </p-button>

    <p-button *ngIf="kararTuru === KararTuruEnum.IletisiminDenetlenmesiAidiyatGuncelleme" label="ID Aidiyat Güncelleme Test Verisi" icon="pi pi-play" severity="info" (onClick)="fillIDAidiyatGuncellemeTestData()" [rounded]="true" size="small">
    </p-button>

    <p-button *ngIf="kararTuru === KararTuruEnum.IletisiminTespiti" label="İletişim Tespiti Kararı Test Verisi" icon="pi pi-play" severity="info" (onClick)="fillITKararTestData()" [rounded]="true" size="small"> </p-button>
</div>

<!-- Hedef Ekle/Düzenle Dialog -->
<p-dialog [header]="editingHedefIndex >= 0 ? 'Hedef Düzenle' : 'Hedef Ekle'" [(visible)]="hedefDialogVisible" [modal]="true" [style]="{ width: '70vw', maxWidth: '800px' }" [closable]="true" (onHide)="hedefDialogKapat()">
    <form [formGroup]="hedefForm">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Hedef No -->
            <div class="flex flex-col">
                <label for="hedefNo" class="text-sm font-medium text-gray-700 mb-1"> Hedef No <span class="text-red-500">*</span> </label>
                <input pInputText formControlName="hedefNo" placeholder="Hedef numarası giriniz" [class]="isHedefFieldInvalid('hedefNo') ? 'ng-invalid ng-dirty' : ''" class="w-full" />
                <small *ngIf="isHedefFieldInvalid('hedefNo')" class="text-red-500">
                    {{ getHedefFieldError('hedefNo') }}
                </small>
            </div>

            <!-- Hedef Tipi -->
            <div class="flex flex-col">
                <label for="hedefTip" class="text-sm font-medium text-gray-700 mb-1"> Hedef Tipi <span class="text-red-500">*</span> </label>
                <p-select
                    formControlName="hedefTip"
                    [options]="hedefTipOptions"
                    optionLabel="label"
                    optionValue="value"
                    placeholder="Hedef tipi seçiniz"
                    inputId="hedefTip"
                    [class]="isHedefFieldInvalid('hedefTip') ? 'ng-invalid ng-dirty' : ''"
                    class="w-full"
                >
                </p-select>
                <small *ngIf="isHedefFieldInvalid('hedefTip')" class="text-red-500">
                    {{ getHedefFieldError('hedefTip') }}
                </small>
            </div>

            <!-- Hedef Adı -->
            <div class="flex flex-col">
                <label for="hedefAd" class="text-sm font-medium text-gray-700 mb-1"> Hedef Adı <span class="text-red-500">*</span> </label>
                <input pInputText formControlName="hedefAd" placeholder="Hedef adı giriniz" [class]="isHedefFieldInvalid('hedefAd') ? 'ng-invalid ng-dirty' : ''" class="w-full" />
                <small *ngIf="isHedefFieldInvalid('hedefAd')" class="text-red-500">
                    {{ getHedefFieldError('hedefAd') }}
                </small>
            </div>

            <!-- Hedef Soyadı -->
            <div class="flex flex-col">
                <label for="hedefSoyad" class="text-sm font-medium text-gray-700 mb-1"> Hedef Soyadı <span class="text-red-500">*</span> </label>
                <input pInputText formControlName="hedefSoyad" placeholder="Hedef soyadı giriniz" [class]="isHedefFieldInvalid('hedefSoyad') ? 'ng-invalid ng-dirty' : ''" class="w-full" />
                <small *ngIf="isHedefFieldInvalid('hedefSoyad')" class="text-red-500">
                    {{ getHedefFieldError('hedefSoyad') }}
                </small>
            </div>

            <!-- Başlama Tarihi -->
            <div class="flex flex-col">
                <label for="baslamaTarihi" class="text-sm font-medium text-gray-700 mb-1"> Başlama Tarihi <span class="text-red-500">*</span> </label>
                <p-datepicker
                    formControlName="baslamaTarihi"
                    placeholder="Başlama tarihi seçiniz"
                    icon="pi pi-calendar"
                    iconDisplay="input"
                    dateFormat="dd/mm/yy"
                    [class]="isHedefFieldInvalid('baslamaTarihi') ? 'ng-invalid ng-dirty' : ''"
                    class="w-full"
                >
                </p-datepicker>
                <small *ngIf="isHedefFieldInvalid('baslamaTarihi')" class="text-red-500">
                    {{ getHedefFieldError('baslamaTarihi') }}
                </small>
            </div>

            <!-- Süre -->
            <div class="flex flex-col">
                <label for="sure" class="text-sm font-medium text-gray-700 mb-1"> Süre </label>
                <input pInputText formControlName="sure" placeholder="Süre giriniz" type="number" class="w-full" />
            </div>

            <!-- Süre Tipi -->
            <div class="flex flex-col">
                <label for="sureTip" class="text-sm font-medium text-gray-700 mb-1"> Süre Tipi </label>
                <p-select formControlName="sureTip" [options]="sureTipiOptions" optionLabel="label" optionValue="value" placeholder="Süre tipi seçiniz" class="w-full"> </p-select>
            </div>

            <!-- Hedef Aidiyat Kodları -->
            <div class="flex flex-col md:col-span-2">
                <label for="hedefAidiyatKodlari" class="text-sm font-medium text-gray-700 mb-1"> Hedef Aidiyat Kodları <span class="text-red-500">*</span> </label>
                <p-multiSelect
                    formControlName="hedefAidiyatKodlari"
                    [options]="hedefAidiyatKodlariOptions"
                    [showClear]="true"
                    [filter]="false"
                    [showToggleAll]="false"
                    display="chip"
                    placeholder="Aidiyat kodları seçin veya yeni ekleyin..."
                    class="w-full"
                    [dropdownIcon]="'pi pi-chevron-down'"
                    [style]="{ maxHeight: '200px' }"
                    panelStyle="{maxHeight: '200px', overflow: 'auto'}"
                    appendTo="body"
                    [maxSelectedLabels]="10"
                >
                    <ng-template pTemplate="header">
                        <div class="p-2 border-b">
                            <div class="flex gap-2">
                                <input
                                    #newHedefAidiyatInput
                                    type="text"
                                    pInputText
                                    placeholder="Yeni hedef aidiyat kodu ekle..."
                                    [(ngModel)]="newHedefAidiyatKodu"
                                    [ngModelOptions]="{ standalone: true }"
                                    (keyup.enter)="addNewHedefAidiyatKodu(); $event.preventDefault(); $event.stopPropagation()"
                                    (click)="$event.stopPropagation()"
                                    class="flex-1"
                                />
                                <p-button icon="pi pi-plus" size="small" [disabled]="!newHedefAidiyatKodu.trim()" (onClick)="addNewHedefAidiyatKodu()"> </p-button>
                            </div>
                        </div>
                    </ng-template>
                </p-multiSelect>
                <small *ngIf="isHedefFieldInvalid('hedefAidiyatKodlari')" class="text-red-500">
                    {{ getHedefFieldError('hedefAidiyatKodlari') }}
                </small>
            </div>

            <!-- Canak No -->
            <div class="flex flex-col">
                <label for="canakNo" class="text-sm font-medium text-gray-700 mb-1"> Canak No </label>
                <input pInputText formControlName="canakNo" placeholder="Canak numarası giriniz" class="w-full" />
            </div>

            <!-- İlgili Mahkeme Karar Detayı (sadece uzatma kararı için) -->
            <div *ngIf="kararTuru === KararTuruEnum.IletisiminDenetlenmesiUzatmaKarari" class="flex flex-col md:col-span-2">
                <label class="text-sm font-medium text-gray-700 mb-2">
                    İlgili Mahkeme Karar Detayı
                    <span class="text-red-500">*</span>
                </label>
                <app-mahkeme-karar-detaylari
                    formControlName="ilgiliMahkemeKararDetayi"
                    [required]="true"
                    [mahkemeKodlari]="mahkemeKodlari"
                    [iller]="iller"
                    [dropdownLoading]="dropdownLoading">
                </app-mahkeme-karar-detaylari>
                <small *ngIf="isHedefFieldInvalid('ilgiliMahkemeKararDetayi')" class="text-red-500">
                    {{ getHedefFieldError('ilgiliMahkemeKararDetayi') }}
                </small>
            </div>
        </div>
    </form>

    <ng-template pTemplate="footer">
        <div class="flex justify-end gap-2">
            <p-button label="İptal" icon="pi pi-times" (onClick)="hedefDialogKapat()" severity="secondary"> </p-button>
            <p-button [label]="editingHedefIndex >= 0 ? 'Güncelle' : 'Ekle'" icon="pi pi-check" (onClick)="hedefKaydet()" [disabled]="hedefForm.invalid"> </p-button>
        </div>
    </ng-template>
</p-dialog>
