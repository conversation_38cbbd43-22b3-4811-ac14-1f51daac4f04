package iym.db.jpa.service.impl.mk;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.mk.MahkemeSucTipiDetay;
import iym.common.service.db.mk.DbMahkemeSucTipiDetayService;
import iym.db.jpa.dao.mk.MahkemeSucTipiDetayRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class DbMahkemeSucTipiDetayServiceImp extends GenericDbServiceImpl<MahkemeSucTipiDetay, Long> implements DbMahkemeSucTipiDetayService {

    private final MahkemeSucTipiDetayRepo mahkemeSucTipiDetayRepo;

    @Autowired
    public DbMahkemeSucTipiDetayServiceImp(MahkemeSucTipiDetayRepo repository) {
        super(repository);
        this.mahkemeSucTipiDetayRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public List<MahkemeSucTipiDetay>  findByMahkemeKararDetayId(Long mahkemeKararDetayId){
        return mahkemeSucTipiDetayRepo.findByMahkemeKararDetayId(mahkemeKararDetayId);
    }

}
