<div class="p-m-4">
  <p-button label="<PERSON><PERSON>" icon="pi pi-plus" (onClick)="openNew()" severity="success"></p-button>

  <p-divider></p-divider>

  <p-toast />
  <p-confirmdialog />

  <p-table
    [value]="ulkeler"
    [lazy]="true"
    [paginator]="true"
    [rows]="10"
    [totalRecords]="totalRecords"
    [loading]="loading"
    [rowsPerPageOptions]="[1,5,10, 25, 50]"
    (onLazyLoad)="loadUlkeler($event)"
    tableStyleClass="p-datatable-sm"
  >
    <ng-template pTemplate="header">
      <tr>
        <th>Bayrak</th>
        <th>Ad</th>
        <th>Kod</th>
        <th>İşlemler</th>
      </tr>
    </ng-template>
    <ng-template pTemplate="body" let-ulke>
      <tr>
         <td style="width: 40px;">
        <span
          class="fi fis"
          [ngClass]="'fi-' + ulke.code.toLowerCase()"
          title="{{ ulke.code }}"
        ></span>
        <td>{{ ulke.name }}</td>
        <td>{{ ulke.code }}</td>
        <td>
          <p-button icon="pi pi-pencil" (onClick)="editUlke(ulke)" class="p-button-sm" severity="info"></p-button>
          <p-button icon="pi pi-trash" (onClick)="deleteUlke(ulke)" class="p-button-sm" severity="danger"></p-button>
        </td>
      </tr>
    </ng-template>
  </p-table>

  <p-dialog
    header="{{ isEditMode ? 'Ülke Düzenle' : 'Yeni Ülke Ekle' }}"
    [(visible)]="displayDialog"
    [modal]="true"
    [style]="{ width: '500px' }"
    [breakpoints]="{ '960px': '75vw', '640px': '90vw' }"
  >
    <p-tabView>
      <p-tabPanel header="Ülke Bilgileri">
        <div class="p-fluid formgrid grid p-3">
          <div class="field col-12 md:col-6">
            <label for="name">Ülke Adı</label>
            <input id="name" pInputText [(ngModel)]="selectedUlke.name" class="w-full" />
          </div>
          <div class="field col-12 md:col-6">
            <label for="code">Ülke Kodu</label>
            <input id="code" pInputText [(ngModel)]="selectedUlke.code" class="w-full" />
          </div>
        </div>
      </p-tabPanel>
    </p-tabView>

    <p-divider></p-divider>

    <ng-template pTemplate="footer">
      <div class="flex justify-end gap-2">
        <p-button label="İptal" icon="pi pi-times" (onClick)="displayDialog = false" severity="secondary"></p-button>
        <p-button label="Kaydet" icon="pi pi-check" (onClick)="saveUlke()" severity="success"></p-button>
      </div>
    </ng-template>
  </p-dialog>
</div>
