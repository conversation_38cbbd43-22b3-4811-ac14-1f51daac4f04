package iym.db.jpa.dao.mktalep;

import iym.common.model.entity.iym.talep.HedeflerTalep;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Repository interface for HedeflerTalep entity
 */
@Repository
public interface HedeflerTalepRepo extends JpaRepository<HedeflerTalep, Long> {

    List<HedeflerTalep> findByMahkemeKararTalepId(Long mahkemeKararTalepId);
    
    List<HedeflerTalep> findByHedefTipi(Long hedefTipi);

    List<HedeflerTalep> findByHedefNo(String hedefNo);

    List<HedeflerTalep> findByBaslamaTarihiBetween(LocalDateTime startDate, LocalDateTime endDate);
    
    List<HedeflerTalep> findByKayitTarihiBetween(LocalDateTime startDate, LocalDateTime endDate);
    
    List<HedeflerTalep> findByTanimlamaTarihiBetween(LocalDateTime startDate, LocalDateTime endDate);
    
    List<HedeflerTalep> findByKapatmaTarihiBetween(LocalDateTime startDate, LocalDateTime endDate);
    
    List<HedeflerTalep> findByImhaTarihiBetween(LocalDateTime startDate, LocalDateTime endDate);


}
