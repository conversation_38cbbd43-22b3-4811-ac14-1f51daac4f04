import { Component, Input, Output, EventEmitter, forwardRef } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { MessageService } from 'primeng/api';

@Component({
  selector: 'app-mahkeme-suc-tipi-kodlari',
  templateUrl: './mahkeme-suc-tipi-kodlari.component.html',
  styleUrls: ['./mahkeme-suc-tipi-kodlari.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => MahkemeSucTipiKodlariComponent),
      multi: true
    }
  ]
})
export class MahkemeSucTipiKodlariComponent implements ControlValueAccessor {
  @Input() label: string = 'Suç Tipi Kodları';
  @Input() placeholder: string = 'Suç tipi kodları seçin veya yeni ekleyin...';
  @Input() required: boolean = false;
  @Input() disabled: boolean = false;
  @Input() options: any[] = [];
  @Output() optionsChange = new EventEmitter<any[]>();

  value: string[] = [];
  newSucTipiKodu: string = '';

  private onChange = (value: string[]) => {};
  private onTouched = () => {};

  constructor(private messageService: MessageService) {}

  writeValue(value: string[]): void {
    this.value = value || [];
  }

  registerOnChange(fn: (value: string[]) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  onValueChange(newValue: string[]): void {
    this.value = newValue;
    this.onChange(this.value);
    this.onTouched();
  }

  addNewSucTipiKodu(): void {
    if (this.newSucTipiKodu && this.newSucTipiKodu.trim()) {
      const trimmedKod = this.newSucTipiKodu.trim();

      // Check if the code already exists in options
      const exists = this.options.some(option => option.value === trimmedKod);

      if (!exists) {
        // Create new array with new option
        const newOptions = [...this.options, {
          label: trimmedKod,
          value: trimmedKod
        }];
        this.options = newOptions;
        this.optionsChange.emit(this.options);

        // Add to current value
        const currentValues = [...(this.value || [])];
        if (!currentValues.includes(trimmedKod)) {
          currentValues.push(trimmedKod);
          this.onValueChange(currentValues);
        }

        this.messageService.add({
          severity: 'success',
          summary: 'Başarılı',
          detail: `Suç tipi kodu "${trimmedKod}" eklendi.`
        });
      } else {
        this.messageService.add({
          severity: 'warn',
          summary: 'Uyarı',
          detail: 'Bu suç tipi kodu zaten mevcut.'
        });
      }

      // Clear input
      this.newSucTipiKodu = '';
    }
  }
}
