import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { InputTextModule } from 'primeng/inputtext';
import { ToastModule } from 'primeng/toast';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ConfirmationService, MessageService } from 'primeng/api';
import { DividerModule } from 'primeng/divider';
import { TabViewModule } from 'primeng/tabview';

import { UlkeService } from './ulke.service';
import { UlkeDto } from './ulke.model';

@Component({
  selector: 'app-ulke-yonetimi',
  standalone: true,
  templateUrl: './ulke-yonetimi.component.html',
  styleUrls: ['./ulke-yonetimi.component.scss'],
  providers: [ConfirmationService, MessageService],
  imports: [
    CommonModule,
    FormsModule,
    TableModule,
    ButtonModule,
    DialogModule,
    InputTextModule,
    ToastModule,
    ConfirmDialogModule,
    DividerModule,
    TabViewModule
  ]
})
export class UlkeYonetimiComponent implements OnInit {
  ulkeler: UlkeDto[] = [];
  selectedUlke: UlkeDto = { name: '', code: '' };
  displayDialog = false;
  isEditMode = false;
  loading = false;
  totalRecords = 0;
  lastEvent: any;

  constructor(
    private ulkeService: UlkeService,
    private confirmationService: ConfirmationService,
    private messageService: MessageService
  ) {}

  ngOnInit(): void {}

  loadUlkeler(event: any) {
    this.lastEvent = event;
    this.loading = true;

    const page = event.first / event.rows;
    const size = event.rows;

    this.ulkeService.getPaged(page, size).subscribe({
      next: res => {
        this.ulkeler = res.content;
        this.totalRecords = res.totalElements;
        this.loading = false;
      },
      error: () => this.loading = false
    });
  }

  openNew() {
    this.selectedUlke = { name: '', code: '' };
    this.isEditMode = false;
    this.displayDialog = true;
  }

  editUlke(ulke: UlkeDto) {
    this.selectedUlke = { ...ulke };
    this.isEditMode = true;
    this.displayDialog = true;
  }

  saveUlke() {
    const observable = this.isEditMode
      ? this.ulkeService.update(this.selectedUlke)
      : this.ulkeService.create(this.selectedUlke);

    observable.subscribe(() => {
      this.messageService.add({ severity: 'success', summary: 'Başarılı', detail: 'Ülke kaydedildi' });
      this.displayDialog = false;
      this.reloadTable();
    });
  }

  deleteUlke(ulke: UlkeDto) {
    this.confirmationService.confirm({
      message: `"${ulke.name}" ülkesini silmek istiyor musunuz?`,
      accept: () => {
        this.ulkeService.delete(ulke.id!).subscribe(() => {
          this.messageService.add({ severity: 'success', summary: 'Silindi', detail: 'Ülke silindi' });
          this.reloadTable();
        });
      }
    });
  }

  reloadTable() {
    if (this.lastEvent) {
      this.loadUlkeler(this.lastEvent);
    }
  }
}
