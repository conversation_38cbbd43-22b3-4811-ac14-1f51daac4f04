<app-floating-configurator />
        <div class="bg-surface-50 dark:bg-surface-950 flex items-center justify-center min-h-screen min-w-[100vw] overflow-hidden">
            <div class="flex flex-col items-center justify-center">
                <div style="border-radius: 56px; padding: 0.3rem; background: linear-gradient(180deg, var(--primary-color) 10%, rgba(33, 150, 243, 0) 30%)">
                    <div class="w-full bg-surface-0 dark:bg-surface-900 py-20 px-8 sm:px-20" style="border-radius: 53px">
                        <div class="text-center mb-8">
                            <img src="assets/images/logo.png" alt="Logo" class="mb-1" style="width: 200px; height: auto; object-fit: contain;">
                            
                            <!--<div class="text-surface-900 dark:text-surface-0 text-3xl font-medium mb-4">Welcome to PrimeLand!</div>--><br/>
                            <span class="text-muted-color font-medium">Şifrenizi değiştirmeniz gerekmektedir</span>
                        </div>

                        <div>                          

                            <label for="curerentPassword" class="block text-surface-900 dark:text-surface-0 font-medium text-xl mb-2">Güncel Parolanız</label>
                            <p-password id="curerentPassword" [(ngModel)]="currentPassword" placeholder="Güncel Parolanız" [toggleMask]="true" styleClass="mb-4" [fluid]="true" [feedback]="false"></p-password>

                            <label for="newPassword" class="block text-surface-900 dark:text-surface-0 font-medium text-xl mb-2">Yeni Parolanız</label>
                            <p-password id="newPassword" [(ngModel)]="newPassword" placeholder="Yeni Parolanız" [toggleMask]="true" styleClass="mb-4" [fluid]="true" [feedback]="false"></p-password>

                            <label for="newPassword2" class="block text-surface-900 dark:text-surface-0 font-medium text-xl mb-2">Yeni Parolanız Tekrar</label>
                            <p-password id="newPassword2" [(ngModel)]="newPassword2" placeholder="Yeni Parolanız Tekrar" [toggleMask]="true" styleClass="mb-4" [fluid]="true" [feedback]="false"></p-password>
                           
                            <p-button label="Parola Değiştir" styleClass="w-full" (onClick)="changePassword()"></p-button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
