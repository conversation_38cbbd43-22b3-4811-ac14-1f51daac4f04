-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create sequence for HTS_MAHKEME_KARAR_TALEP if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = 'HTS_MAHKEME_KARAR_TALEP_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.HTS_MAHKEME_KARAR_TALEP_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

-- Create HTS_MAHKEME_KARAR_TALEP table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'HTS_MAHKEME_KARAR_TALEP';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.HTS_MAHKE<PERSON>_KARAR_TALEP (
      ID NUMBER NOT NULL,
      <PERSON><PERSON>AK_ID NUMBER NOT NULL,
      <PERSON>U<PERSON><PERSON><PERSON>I_ID NUMBER NOT NULL,
      KAYIT_TARIHI DATE NOT NULL,
      DURUM VARCHAR2(100 BYTE),
      KARAR_TIP VARCHAR2(100 BYTE) NOT NULL,
      HUKUK_BIRIM VARCHAR2(100 BYTE) NOT NULL,
      MAHKEME_ILI VARCHAR2(100 BYTE) NOT NULL,
      MAHKEME_KODU VARCHAR2(100 BYTE) NOT NULL,
      MAHKEME_ADI VARCHAR2(1000 BYTE) NOT NULL,
      ACIKLAMA VARCHAR2(1000 BYTE),
      MAHKEME_KARAR_NO VARCHAR2(100 BYTE),
      SORUSTURMA_NO VARCHAR2(100 BYTE),
      CONSTRAINT PK_HTS_MAHKEME_ID PRIMARY KEY (ID) ENABLE
    )';
    
    -- Create index
    EXECUTE IMMEDIATE 'CREATE INDEX iym.EVRAK_ID_IDX ON iym.HTS_MAHKEME_KARAR_TALEP (EVRAK_ID ASC)';
  END IF;
END;
/

-- Insert sample data if table is empty
DECLARE
  row_count NUMBER;
BEGIN
  SELECT COUNT(*) INTO row_count FROM iym.HTS_MAHKEME_KARAR_TALEP;
  IF row_count = 0 THEN
    -- Make sure we have evrak_kayit and users in the respective tables
    DECLARE
      evrak_count NUMBER;
      user_count NUMBER;
    BEGIN
      SELECT COUNT(*) INTO evrak_count FROM iym.EVRAK_KAYIT WHERE EVRAK_TIPI = 'TESPIT';
      SELECT COUNT(*) INTO user_count FROM iym.KULLANICILAR;
      
      IF evrak_count > 0 AND user_count > 0 THEN
        -- Get the ID of the admin user
        DECLARE
          admin_id NUMBER;
        BEGIN
          SELECT ID INTO admin_id FROM iym.KULLANICILAR WHERE KULLANICI_ADI = 'admin';
          
          -- Get the ID of the TESPIT type evrak
          FOR evrak_rec IN (
            SELECT ID FROM iym.EVRAK_KAYIT WHERE EVRAK_TIPI = 'TESPIT'
          ) LOOP
            -- Sample data - HTS Mahkeme Kararı
            INSERT INTO iym.HTS_MAHKEME_KARAR_TALEP (
              ID, EVRAK_ID, KULLANICI_ID, KAYIT_TARIHI, DURUM,
              KARAR_TIP, HUKUK_BIRIM, MAHKEME_ILI, MAHKEME_KODU,
              MAHKEME_ADI, ACIKLAMA, MAHKEME_KARAR_NO, SORUSTURMA_NO
            ) VALUES (
              iym.HTS_MAHKEME_KARAR_TALEP_SEQ.NEXTVAL, evrak_rec.ID, admin_id, SYSDATE, 'AKTIF',
              'ILETISIM_TESPITI', 'AĞIR CEZA', '0600', 'ACM01',
              'ANKARA 1. AĞIR CEZA MAHKEMESİ', 'İletişim tespiti kararı', 'HTS-2023-001', '2023/125'
            );
          END LOOP;
        EXCEPTION
          WHEN NO_DATA_FOUND THEN
            NULL; -- Required data not found, skip insertions
        END;
      END IF;
    END;
  END IF;
END;
/

COMMIT;
