package iym.makos.domain.testdata;

import iym.common.enums.KararTuru;
import iym.common.validation.ValidationResult;
import iym.makos.domain.mktalep.requestprocessor.validator.custom.MakosRequestValid;
import iym.makos.model.dto.mktalep.request.MkTalepRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.jackson.Jacksonized;
import lombok.extern.slf4j.Slf4j;

@Jacksonized
@Data
@NoArgsConstructor
@SuperBuilder
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@MakosRequestValid
@Slf4j
public class DummyMKTalepRequest extends MkTalepRequest {
    @Override
    protected void assignKararTuru() {
        this.kararTuru = KararTuru.ILETISIMIN_DENETLENMESI_YENI_KARAR;
    }

    @Override
    public ValidationResult isValid() {
        return new ValidationResult(true);
    }
}