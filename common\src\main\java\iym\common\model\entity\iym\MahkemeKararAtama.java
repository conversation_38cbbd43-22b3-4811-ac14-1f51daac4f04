package iym.common.model.entity.iym;

import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;


@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "MahkemeKararAtama")
@Table(name = "MAHKEME_KARAR_ATAMA")
public class MahkemeKararAtama implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "MAHKEME_KARAR_ATAMA_SEQ")
    @SequenceGenerator(name = "MAHKEME_KARAR_ATAMA_SEQ", sequenceName = "MAHKEME_KARAR_ATAMA_SEQ", allocationSize = 1)
    private Long id;

    @Column(name = "EVRAK_ID")
    private Long evrakId;

    @Column(name = "KULLANICI_ID")
    private Long kullaniciId;

    @Column(name = "TARIH")
    private LocalDateTime tarih;

    @Column(name = "DURUM", length = 10)
    @Size(max = 10)
    private String durum;

    @Column(name = "SEVIYE", length = 1)
    @Size(max = 1)
    private String seviye;

    @Column(name = "ACIKLAMA", length = 2000)
    @Size(max = 2000)
    private String aciklama;

    @Column(name = "SEBEBI")
    private Long sebebi;

    @Column(name = "GONDEREN_ID")
    private Long gonderenId;

    @Column(name = "GONDERILEN_ID")
    private Long gonderilenId;

}
