package iym.db.jpa.dao.mkislem;

import iym.common.model.entity.iym.mkislem.HedeflerDetayIslem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface HedeflerDetayIslemRepo extends JpaRepository<HedeflerDetayIslem, Long> {

    List<HedeflerDetayIslem> findByMahkemeKararIslemId(Long mahkemeKararIslemId);

    List<HedeflerDetayIslem> findBydetayMahkemeKararIslemId(Long detayMahkemeKararTalepId);

    Optional<HedeflerDetayIslem> findByMahkemeKararIslemIdAndHedefNoAndHedefTipi(Long mahkemeKararIslemId, String hedefNo, Integer hedefTipi);

}
