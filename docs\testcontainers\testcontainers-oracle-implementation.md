# Oracle Testcontainer Implementation Guide

## Proje Durumu

Bu doküman Oracle bağımlı column definition içeren entity'lerin birim testlerinin H2 yerine Oracle Testcontainer ile çalıştırılması için yapılan implementasyonu açıklar.

## Problem Tanımı

- Oracle-specific column definition'lar (CHAR(1), SEQUENCE, DATE) H2 ile uyumsuz
- H2 in-memory database Oracle davranışlarını tam olarak simüle edemiyor
- False positive test sonuçları riski
- Gerçek Oracle veritabanı davranışlarının test edilememesi

## Çözüm: Oracle Testcontainer

Oracle 11 XE Docker container kullanarak gerçek Oracle veritabanında testler çalıştırılması.

## Tamamlanan İşler ✅

### 1. Testcontainers Bağımlılığı Eklendi

**Dosya:** `pom.xml`
```xml
<properties>
    <testcontainers.version>1.19.3</testcontainers.version>
</properties>

<dependencies>
    <!-- Testcontainers for Oracle integration testing -->
    <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>junit-jupiter</artifactId>
        <version>${testcontainers.version}</version>
        <scope>test</scope>
    </dependency>
    <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>oracle-xe</artifactId>
        <version>${testcontainers.version}</version>
        <scope>test</scope>
    </dependency>
    <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>testcontainers</artifactId>
        <version>${testcontainers.version}</version>
        <scope>test</scope>
    </dependency>
</dependencies>
```

### 2. AbstractOracleTestContainer Sınıfı Oluşturuldu

**Dosya:** `common/src/test/java/iym/common/testcontainer/AbstractOracleTestContainer.java`

**Özellikler:**
- Oracle 11 XE Docker container (gvenzl/oracle-xe:11.2.0.2-slim-faststart)
- Otomatik schema oluşturma (IYM schema)
- Connection pooling (HikariCP)
- SQL logging aktif
- Container reuse devre dışı (parallel execution güvenliği için)

### 3. Oracle Test Profilleri Oluşturuldu

**Dosyalar:**
- `common/src/test/resources/application-testcontainers-oracle.properties`
- `makos/src/test/resources/application-testcontainers-oracle.properties`
- `database/src/test/resources/application-testcontainers-oracle.properties`

**Yapılandırma:**
- OracleDialect kullanımı
- IYM schema default
- DDL auto: validate
- SQL logging aktif
- Connection pool optimizasyonu

### 4. Oracle Initialization Script

**Dosya:** `common/src/test/resources/testcontainers-oracle-init.sql`

**İçerik:**
- IYM schema oluşturma
- Oracle SEQUENCE'ler (MAKOS_USER_SEQ, HEDEFLER_SEQ, vb.)
- Oracle-specific table DDL'leri
- Test data seed'leri

### 5. Oracle Integration Testleri Yazıldı

#### KullanicilarRepoOracleIntegrationTest
**Dosya:** `database/src/test/java/iym/db/jpa/dao/KullanicilarRepoOracleIntegrationTest.java`

**Test Edilen Özellikler:**
- Oracle CHAR(1) column definitions (IMZA_YETKISI)
- Oracle CHAR(32 BYTE) column (SIFRE)
- Oracle DATE column behavior
- Oracle-specific SQL queries

#### MakosUserRepoOracleIntegrationTest
**Dosya:** `database/src/test/java/iym/db/jpa/dao/MakosUserRepoOracleIntegrationTest.java`

**Test Edilen Özellikler:**
- Oracle SEQUENCE generator (MAKOS_USER_SEQ)
- Sequential ID generation
- Oracle constraint behavior
- Enum mapping (MakosUserRoleType, UserStatusType, KullaniciKurum)

#### HedefTipleriRepoOracleIntegrationTest
**Dosya:** `database/src/test/java/iym/db/jpa/dao/HedefTipleriRepoOracleIntegrationTest.java`

**Test Edilen Özellikler:**
- Oracle CHAR(1) column (SONLANDIRMAMI)
- Oracle VARCHAR2 columns
- Oracle NUMBER columns
- Primary key constraints

### 6. Maven Yapılandırması

**Common modülü test-jar oluşturma:**
```xml
<build>
    <plugins>
        <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-jar-plugin</artifactId>
            <executions>
                <execution>
                    <goals>
                        <goal>test-jar</goal>
                    </goals>
                </execution>
            </executions>
        </plugin>
    </plugins>
</build>
```

**Database modülü test-jar bağımlılığı:**
```xml
<dependency>
    <groupId>iym</groupId>
    <artifactId>common</artifactId>
    <version>1.0</version>
    <type>test-jar</type>
    <scope>test</scope>
</dependency>
```



## Devam Etmek İçin Komutlar

```bash
# 1. Docker Desktop'ı başlat (Windows'ta)
# Docker Desktop uygulamasını manuel olarak başlat

# 2. Docker'ın çalıştığını doğrula
docker ps

# 3. Oracle Testcontainer testlerini çalıştır
mvn test -Dtest=KullanicilarRepoOracleIntegrationTest -pl database

# 4. Diğer Oracle testlerini de çalıştır
mvn test -Dtest=MakosUserRepoOracleIntegrationTest -pl database
mvn test -Dtest=HedefTipleriRepoOracleIntegrationTest -pl database

# 5. Tüm Oracle testlerini birlikte çalıştır
mvn test -Dtest="*OracleIntegrationTest" -pl database
```

## Test Çalıştırma Rehberi

### Tek Test Çalıştırma
```bash
mvn test -Dtest=KullanicilarRepoOracleIntegrationTest -pl database
```

### Profil ile Çalıştırma
```bash
mvn test -Dtest=KullanicilarRepoOracleIntegrationTest -pl database -Dspring.profiles.active=testcontainers-oracle
```

### Debug Mode
```bash
mvn test -Dtest=KullanicilarRepoOracleIntegrationTest -pl database -X
```

## Beklenen Sonuçlar

### Başarılı Test Çıktısı
- Oracle container başlatılması
- IYM schema oluşturulması
- Test data seed'lenmesi
- Oracle-specific DDL/SQL ifadelerinin çalışması
- Tüm testlerin PASS olması

### Test Edilen Oracle Özellikleri
1. **CHAR(1) Columns:** Padding davranışı
2. **CHAR(32 BYTE) Columns:** Byte semantics
3. **Oracle SEQUENCE:** Auto-increment davranışı
4. **Oracle DATE:** Precision ve format
5. **Oracle Constraints:** Primary key, unique
6. **Oracle VARCHAR2:** Length semantics

## Faydalar

1. **Gerçek Oracle Davranışı:** H2 simülasyonu yerine gerçek Oracle
2. **False Positive Önleme:** Oracle-specific hatalar yakalanır
3. **Production Uyumluluğu:** Aynı veritabanı motoru kullanımı
4. **DDL Doğrulaması:** Oracle-specific DDL ifadeleri test edilir
5. **Performance Testing:** Gerçek Oracle performance karakteristikleri

## Notlar

- Container reuse devre dışı (parallel execution güvenliği için)
- Test isolation @Transactional ile sağlanıyor
- Oracle 11 XE kullanımı (production ile uyumlu)
- Automatic cleanup test sonrası
- SQL logging debug için aktif

## Sonraki Adımlar

1. Docker Desktop başlatılması
2. Testlerin çalıştırılması ve doğrulanması
3. Diğer modüller için benzer testlerin yazılması
