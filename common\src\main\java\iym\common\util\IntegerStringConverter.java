package iym.common.util;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

@Converter(autoApply = true)
public class IntegerStringConverter implements AttributeConverter<Integer, String> {

    @Override
    public String convertToDatabaseColumn(Integer attribute) {
        return attribute != null ? attribute.toString() : null;
    }

    @Override
    public Integer convertToEntityAttribute(String dbData) {
        try {
            return dbData != null ? Integer.valueOf(dbData) : null;
        } catch (NumberFormatException e) {
            return null;
        }
    }
}