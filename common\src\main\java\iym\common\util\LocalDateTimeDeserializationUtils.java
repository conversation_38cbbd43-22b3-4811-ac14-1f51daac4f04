package iym.common.util;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Utility class for configuring ObjectMapper to enforce string-only LocalDateTime deserialization
 * This ensures that only properly formatted date-time strings are accepted, rejecting numeric timestamps
 * across all IYM modules
 */
@Slf4j
public class LocalDateTimeDeserializationUtils {

    private LocalDateTimeDeserializationUtils() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    /**
     * Configures an ObjectMapper to enforce string-only LocalDateTime deserialization
     * This method can be used to configure any ObjectMapper instance across the project
     *
     * @param objectMapper the ObjectMapper to configure
     */
    public static void configureObjectMapperForLocalDateTime(ObjectMapper objectMapper) {
        try {
            // Enable features for better deserialization
            objectMapper.enable(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY);
            objectMapper.enable(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT);

            // Create a custom module for LocalDateTime handling
            SimpleModule localDateTimeModule = new SimpleModule();

            // Custom deserializer that only accepts string values for LocalDateTime
            localDateTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(DateTimeFormatter.ISO_LOCAL_DATE_TIME) {
                @Override
                public LocalDateTime deserialize(com.fasterxml.jackson.core.JsonParser p, com.fasterxml.jackson.databind.DeserializationContext ctxt) throws java.io.IOException {
                    if (p.getCurrentToken().isNumeric()) {
                        // Reject numeric values - only string deserialization is allowed
                        throw new java.io.IOException("LocalDateTime deserialization from numeric values is not allowed. Expected string format (ISO_LOCAL_DATE_TIME) but received numeric value: " + p.getText());
                    } else {
                        // Handle formatted string
                        return super.deserialize(p, ctxt);
                    }
                }
            });

            // Register the module
            objectMapper.registerModule(localDateTimeModule);

            log.debug("Successfully configured ObjectMapper for string-only LocalDateTime deserialization");
        } catch (Exception e) {
            log.error("Failed to configure ObjectMapper for LocalDateTime handling", e);
        }
    }

    /**
     * Configures the ObjectMapper in a MappingJackson2HttpMessageConverter to enforce string-only LocalDateTime deserialization
     * This method is specifically for configuring RestTemplate message converters
     *
     * @param converter the MappingJackson2HttpMessageConverter to configure
     */
    public static void configureMessageConverterForLocalDateTime(MappingJackson2HttpMessageConverter converter) {
        configureObjectMapperForLocalDateTime(converter.getObjectMapper());
    }

    /**
     * Configures all MappingJackson2HttpMessageConverter instances in a RestTemplate to enforce string-only LocalDateTime deserialization
     * This method is specifically for configuring RestTemplate instances
     *
     * @param restTemplate the RestTemplate to configure
     */
    public static void configureRestTemplateForLocalDateTime(org.springframework.web.client.RestTemplate restTemplate) {
        try {
            for (Object converter : restTemplate.getMessageConverters()) {
                if (converter instanceof MappingJackson2HttpMessageConverter jacksonConverter) {
                    configureObjectMapperForLocalDateTime(jacksonConverter.getObjectMapper());
                }
            }
        } catch (Exception e) {
            log.error("Failed to configure RestTemplate for LocalDateTime handling", e);
        }
    }
}
