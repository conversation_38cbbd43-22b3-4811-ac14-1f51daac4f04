package gov.tib.iym.mahkemekarar.base.model;

import java.util.ArrayList;

public class XmlEvrakBase {
	private Long id;
	private String evrakSiraNo;
	private String evrakNo;
	private String girisTarih;
	private String evrakTarihi;
	private Long kayitEdenKullanici;
	private String kayitEdenKullaniciAdi;
	private String evrakTipi;
	private String havaleBirim;
	private String aciklama;
	private String gelenIl;
	private String evrakGeldigiKurum;
	private String evrakKonusu;
	private String acilMi;
	private String sorusturmaNo;
	private String mahkemeKararNo;
	private String evrakYonu;
	private String uniqCol;
	private String durumu;
	
	private ArrayList<String> hatalar = new ArrayList<String>();

	
	public String getEvrakSiraNo() {
		return evrakSiraNo;
	}
	public void setEvrakSiraNo(String evrakSiraNo) {
		this.evrakSiraNo = evrakSiraNo;
	}
	public String getEvrakNo() {
		return evrakNo;
	}
	public void setEvrakNo(String evrakNo) {
		this.evrakNo = evrakNo;
	}
	public String getGirisTarih() {
		return girisTarih;
	}
	public void setGirisTarih(String girisTarih) {
		this.girisTarih = girisTarih;
	}
	public String getEvrakTarihi() {
		return evrakTarihi;
	}
	public void setEvrakTarihi(String evrakTarihi) {
		this.evrakTarihi = evrakTarihi;
	}
	public Long getKayitEdenKullanici() {
		return kayitEdenKullanici;
	}
	public void setKayitEdenKullanici(Long kayitEdenKullanici) {
		this.kayitEdenKullanici = kayitEdenKullanici;
	}
	public String getEvrakTipi() {
		return evrakTipi;
	}
	public void setEvrakTipi(String evrakTipi) {
		this.evrakTipi = evrakTipi;
	}
	public String getHavaleBirim() {
		return havaleBirim;
	}
	public void setHavaleBirim(String havaleBirim) {
		this.havaleBirim = havaleBirim;
	}
	public String getAciklama() {
		return aciklama;
	}
	public void setAciklama(String aciklama) {
		this.aciklama = aciklama;
	}
	public String getGelenIl() {
		return gelenIl;
	}
	public void setGelenIl(String gelenIl) {
		this.gelenIl = gelenIl;
	}
	public String getEvrakGeldigiKurum() {
		return evrakGeldigiKurum;
	}
	public void setEvrakGeldigiKurum(String evrakGeldigiKurum) {
		this.evrakGeldigiKurum = evrakGeldigiKurum;
	}
	public String getEvrakKonusu() {
		return evrakKonusu;
	}
	public void setEvrakKonusu(String evrakKonusu) {
		this.evrakKonusu = evrakKonusu;
	}
	public String getAcilMi() {
		return acilMi;
	}
	public void setAcilMi(String acilMi) {
		this.acilMi = acilMi;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public ArrayList<String> getHatalar() {
		return hatalar;
	}
	public void setHatalar(ArrayList<String> hatalar) {
		this.hatalar = hatalar;
	}
	public void addHata(String message) {
		this.hatalar.add(message);
		
	}
	public String getSorusturmaNo() {
		return sorusturmaNo;
	}
	public void setSorusturmaNo(String sorusturmaNo) {
		this.sorusturmaNo = sorusturmaNo;
	}
	public String getMahkemeKararNo() {
		return mahkemeKararNo;
	}
	public void setMahkemeKararNo(String mahkemeKararNo) {
		this.mahkemeKararNo = mahkemeKararNo;
	}
	public String getEvrakYonu() {
		return evrakYonu;
	}
	public void setEvrakYonu(String evrakYonu) {
		this.evrakYonu = evrakYonu;
	}
	public String getUniqCol() {
		return uniqCol;
	}
	public void setUniqCol(String uniqCol) {
		this.uniqCol = uniqCol;
	}
	public String getDurumu() {
		return durumu;
	}
	public void setDurumu(String durumu) {
		this.durumu = durumu;
	}
	public String getKayitEdenKullaniciAdi() {
		return kayitEdenKullaniciAdi;
	}
	public void setKayitEdenKullaniciAdi(String kayitEdenKullaniciAdi) {
		this.kayitEdenKullaniciAdi = kayitEdenKullaniciAdi;
	}	
	
	
}
