package iym.common.service.db;

import iym.common.model.entity.iym.Gorevler2;
import iym.common.model.entity.iym.Gorevler2PK;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Service interface for Gorevler2 entity
 */
public interface DbGorevler2Service extends GenericDbService<Gorevler2, Gorevler2PK> {

    List<Gorevler2> findByGorev(String gorev);
    
    List<Gorevler2> findByGorevKodu(Long gorevKodu);
    
    Optional<Gorevler2> findByGorevAndGorevKodu(String gorev, Long gorevKodu);
    
    List<Gorevler2> findByGorevImzaAdi(String gorevImzaAdi);
    
    List<Gorevler2> findByImzaYetki(String imzaYetki);
    
    List<Gorevler2> findBySilindi(Long silindi);
    
    List<Gorevler2> findByGorevKodu2(String gorevKodu2);
    
    List<Gorevler2> findByGorevTipi(String gorevTipi);
    
    List<Gorevler2> findByOncelik(Long oncelik);
    
    List<Gorevler2> findByBaslamaTarihiBetween(LocalDateTime startDate, LocalDateTime endDate);
    
    List<Gorevler2> findByBitisTarihiBetween(LocalDateTime startDate, LocalDateTime endDate);
    
    List<Gorevler2> findByBaslamaTarihiLessThanEqualAndBitisTarihiGreaterThanEqual(LocalDateTime date1, LocalDateTime date2);
    
    List<Gorevler2> findByGorevContainingIgnoreCase(String gorev);
    
    List<Gorevler2> findByGorevImzaAdiContainingIgnoreCase(String gorevImzaAdi);
    
    List<Gorevler2> findByGorevTipiAndImzaYetki(String gorevTipi, String imzaYetki);
    
    List<Gorevler2> findByGorevTipiAndSilindi(String gorevTipi, Long silindi);
    
    boolean existsByGorevAndGorevKodu(String gorev, Long gorevKodu);
}
