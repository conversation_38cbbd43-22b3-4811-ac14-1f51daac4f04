<p-dialog 
  header="Mahkeme Karar Talebi Önizleme" 
  [(visible)]="visible" 
  (onHide)="onDialogHide()"
  [modal]="true" 
  [style]="{width: '80vw', maxHeight: '90vh'}" 
  [draggable]="false" 
  [resizable]="false"
  styleClass="preview-dialog"
>
  <div class="preview-content max-h-96 overflow-y-auto">
    <!-- <PERSON><PERSON> Bilgiler -->
    <div class="mb-6">
      <h3 class="text-lg font-semibold text-gray-800 mb-4 border-b pb-2">📋 Genel Bilgiler</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="bg-gray-50 p-3 rounded">
          <label class="text-sm font-medium text-gray-600">Karar Türü:</label>
          <p class="text-sm text-gray-800 mt-1">{{ getKararTuruLabel(kararTuru!) }}</p>
        </div>
        <div class="bg-gray-50 p-3 rounded" *ngIf="formData.evrakTuru">
          <label class="text-sm font-medium text-gray-600">Evrak Türü:</label>
          <p class="text-sm text-gray-800 mt-1">{{ getEvrakTuruLabel(formData.evrakTuru) }}</p>
        </div>
        <div class="bg-gray-50 p-3 rounded" *ngIf="formData.mahkemeKararTipi">
          <label class="text-sm font-medium text-gray-600">Mahkeme Karar Tipi:</label>
          <p class="text-sm text-gray-800 mt-1">{{ getMahkemeKararTipiLabel(formData.mahkemeKararTipi) }}</p>
        </div>
        <div class="bg-gray-50 p-3 rounded" *ngIf="formData.kurum">
          <label class="text-sm font-medium text-gray-600">Kurum:</label>
          <p class="text-sm text-gray-800 mt-1">{{ formData.kurum }}</p>
        </div>
      </div>
    </div>

    <!-- Mahkeme Bilgileri -->
    <div class="mb-6" *ngIf="formData.mahkemeKodu || formData.mahkemeKararNo">
      <h3 class="text-lg font-semibold text-gray-800 mb-4 border-b pb-2">⚖️ Mahkeme Bilgileri</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="bg-gray-50 p-3 rounded" *ngIf="formData.mahkemeKodu">
          <label class="text-sm font-medium text-gray-600">Mahkeme Kodu:</label>
          <p class="text-sm text-gray-800 mt-1">{{ formData.mahkemeKodu }}</p>
        </div>
        <div class="bg-gray-50 p-3 rounded" *ngIf="formData.mahkemeKararNo">
          <label class="text-sm font-medium text-gray-600">Mahkeme Karar No:</label>
          <p class="text-sm text-gray-800 mt-1">{{ formData.mahkemeKararNo }}</p>
        </div>
        <div class="bg-gray-50 p-3 rounded" *ngIf="formData.sorusturmaNo">
          <label class="text-sm font-medium text-gray-600">Soruşturma No:</label>
          <p class="text-sm text-gray-800 mt-1">{{ formData.sorusturmaNo }}</p>
        </div>
        <div class="bg-gray-50 p-3 rounded" *ngIf="formData.mahkemeIlIlceKodu">
          <label class="text-sm font-medium text-gray-600">Mahkeme İl/İlçe Kodu:</label>
          <p class="text-sm text-gray-800 mt-1">{{ formData.mahkemeIlIlceKodu }}</p>
        </div>
      </div>
    </div>

    <!-- Dosya Bilgisi -->
    <div class="mb-6" *ngIf="seciliDosya">
      <h3 class="text-lg font-semibold text-gray-800 mb-4 border-b pb-2">📎 Yüklenen Dosya</h3>
      <div class="bg-gray-50 p-4 rounded flex items-center gap-3">
        <i class="pi pi-file text-2xl text-blue-500"></i>
        <div>
          <p class="text-sm font-medium text-gray-800">{{ seciliDosya.name }}</p>
          <p class="text-xs text-gray-600">{{ formatFileSize(seciliDosya.size) }}</p>
        </div>
      </div>
    </div>

    <!-- Hedef Detayları (ID Karar Türleri için) -->
    <div class="mb-6" *ngIf="hasHedefDetayListesi()">
      <h3 class="text-lg font-semibold text-gray-800 mb-4 border-b pb-2">🎯 Hedef Detayları</h3>
      <div class="space-y-3">
        <div *ngFor="let hedef of formData.hedefDetayListesi; let i = index" class="bg-gray-50 p-4 rounded">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
            <div>
              <label class="text-xs font-medium text-gray-600">Hedef No:</label>
              <p class="text-sm text-gray-800">{{ hedef.hedefNo }}</p>
            </div>
            <div>
              <label class="text-xs font-medium text-gray-600">Hedef Tipi:</label>
              <p class="text-sm text-gray-800">{{ getHedefTipLabel(hedef.hedefTip) }}</p>
            </div>
            <div>
              <label class="text-xs font-medium text-gray-600">Ad Soyad:</label>
              <p class="text-sm text-gray-800">{{ hedef.hedefAd }} {{ hedef.hedefSoyad }}</p>
            </div>
            <div>
              <label class="text-xs font-medium text-gray-600">Başlama Tarihi:</label>
              <p class="text-sm text-gray-800">{{ formatTarih(hedef.baslamaTarihi) }}</p>
            </div>
            <div>
              <label class="text-xs font-medium text-gray-600">Süre:</label>
              <p class="text-sm text-gray-800">{{ hedef.sure }} {{ getSureTipiLabel(hedef.sureTip) }}</p>
            </div>
            <div *ngIf="hedef.hedefAidiyatKodlari?.length > 0">
              <label class="text-xs font-medium text-gray-600">Aidiyat Kodları:</label>
              <div class="flex flex-wrap gap-1 mt-1">
                <span *ngFor="let kod of hedef.hedefAidiyatKodlari" class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">{{ kod }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- IT Hedef Detayları -->
    <div class="mb-6" *ngIf="hasITHedefDetaylari()">
      <h3 class="text-lg font-semibold text-gray-800 mb-4 border-b pb-2">🔍 İletişimin Tespiti Hedef Detayları</h3>
      <div class="space-y-3">
        <div *ngFor="let itHedef of formData.itHedefDetayListesi; let i = index" class="bg-gray-50 p-4 rounded">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
            <div>
              <label class="text-xs font-medium text-gray-600">Sorgu Tipi:</label>
              <p class="text-sm text-gray-800">{{ getSorguTipiLabel(itHedef.sorguTipi) }}</p>
            </div>
            <div>
              <label class="text-xs font-medium text-gray-600">Hedef:</label>
              <p class="text-sm text-gray-800">{{ itHedef.hedef?.hedefNo }} ({{ getHedefTipLabel(itHedef.hedef?.hedefTip) }})</p>
            </div>
            <div *ngIf="itHedef.karsiHedef">
              <label class="text-xs font-medium text-gray-600">Karşı Hedef:</label>
              <p class="text-sm text-gray-800">{{ itHedef.karsiHedef?.hedefNo }} ({{ getHedefTipLabel(itHedef.karsiHedef?.hedefTip) }})</p>
            </div>
            <div>
              <label class="text-xs font-medium text-gray-600">Başlama - Bitiş Tarihi:</label>
              <p class="text-sm text-gray-800">{{ formatTarih(itHedef.baslamaTarihi) }} - {{ formatTarih(itHedef.bitisTarihi) }}</p>
            </div>
            <div>
              <label class="text-xs font-medium text-gray-600">Tespit Türü:</label>
              <p class="text-sm text-gray-800">{{ itHedef.tespitTuru }} <span *ngIf="itHedef.tespitTuruDetay">({{ itHedef.tespitTuruDetay }})</span></p>
            </div>
            <div *ngIf="itHedef.aciklama">
              <label class="text-xs font-medium text-gray-600">Açıklama:</label>
              <p class="text-sm text-gray-800">{{ itHedef.aciklama }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Aidiyat Kodları -->
    <div class="mb-6" *ngIf="hasAidiyatKodlari()">
      <h3 class="text-lg font-semibold text-gray-800 mb-4 border-b pb-2">🏢 Aidiyat Kodları</h3>
      <div class="bg-gray-50 p-4 rounded">
        <div class="flex flex-wrap gap-2">
          <span *ngFor="let kod of formData.mahkemeAidiyatKodlari" class="bg-green-100 text-green-800 text-sm px-3 py-1 rounded">{{ kod }}</span>
        </div>
      </div>
    </div>

    <!-- Suç Tipi Kodları -->
    <div class="mb-6" *ngIf="hasSucTipiKodlari()">
      <h3 class="text-lg font-semibold text-gray-800 mb-4 border-b pb-2">⚖️ Suç Tipi Kodları</h3>
      <div class="bg-gray-50 p-4 rounded">
        <div class="flex flex-wrap gap-2">
          <span *ngFor="let kod of formData.mahkemeSucTipiKodlari" class="bg-red-100 text-red-800 text-sm px-3 py-1 rounded">{{ kod }}</span>
        </div>
      </div>
    </div>

    <!-- Güncelleme Detayları -->
    <div class="mb-6" *ngIf="hasGuncellemeDetaylari()">
      <h3 class="text-lg font-semibold text-gray-800 mb-4 border-b pb-2">🔄 Güncelleme Detayları</h3>
      <div class="bg-gray-50 p-4 rounded">
        <p class="text-sm text-gray-600">Güncelleme detayları mevcut ancak önizleme için henüz implement edilmedi.</p>
      </div>
    </div>

    <!-- Açıklama -->
    <div class="mb-6" *ngIf="formData.aciklama">
      <h3 class="text-lg font-semibold text-gray-800 mb-4 border-b pb-2">📝 Açıklama</h3>
      <div class="bg-gray-50 p-4 rounded">
        <p class="text-sm text-gray-800">{{ formData.aciklama }}</p>
      </div>
    </div>
  </div>

  <ng-template pTemplate="footer">
    <div class="flex justify-end gap-3">
      <p-button 
        label="Geri Dön" 
        severity="secondary" 
        icon="pi pi-arrow-left"
        (onClick)="onDialogHide()"
      >
      </p-button>
      <p-button 
        label="Onayla ve Gönder" 
        severity="success" 
        icon="pi pi-check"
        (onClick)="onConfirmClick()"
      >
      </p-button>
    </div>
  </ng-template>
</p-dialog>
