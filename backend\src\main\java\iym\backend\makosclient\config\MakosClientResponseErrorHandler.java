package iym.backend.makosclient.config;

import iym.backend.makosclient.exception.MakosApiClientException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.util.StreamUtils;
import org.springframework.web.client.ResponseErrorHandler;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Optional;

/**
 * Custom error handler for MAKOS API client that provides access to HTTP status code
 * and response body separately through custom exception
 */
@Slf4j
public class MakosClientResponseErrorHandler implements ResponseErrorHandler {

    public MakosClientResponseErrorHandler() {
    }

    @Override
    public boolean hasError(ClientHttpResponse response) throws IOException {
        HttpStatus.Series series = HttpStatus.Series.resolve(response.getStatusCode().value());
        return series == HttpStatus.Series.CLIENT_ERROR || series == HttpStatus.Series.SERVER_ERROR;
    }

    @Override
    public void handleError(ClientHttpResponse response) throws IOException {
        HttpStatus statusCode = Optional.ofNullable(HttpStatus.resolve(response.getStatusCode().value()))
                .orElse(HttpStatus.INTERNAL_SERVER_ERROR);
        String statusText = response.getStatusText();

        // Read the response body as string
        String responseBody = StreamUtils.copyToString(response.getBody(), StandardCharsets.UTF_8);

        log.error("MAKOS API Error: HTTP {} {}. Response body: {}",
                statusCode.value(), statusText, responseBody);

        // Throw custom exception with status code and response body
        throw new MakosApiClientException(statusCode, responseBody);
    }
}
