package iym.common.model.entity.iym.mkislem;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Entity class for MAHKEME_AIDIYAT_DETAY_TALEP table
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "MahkemeSucTipiDetayIslem")
@Table(name = "MAHKEME_SUCTIPI_DETAY_ISLEM")
public class MahkemeSucTipiDetayIslem implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "MAH_ST_DET_ISLEM_SEQ")
    @SequenceGenerator(name = "MAH_ST_DET_ISLEM_SEQ", sequenceName = "MAH_ST_DET_ISLEM_SEQ", allocationSize = 1)
    private Long id;

    @Column(name = "ILISKILI_MAHKEME_KARAR_ID")
    private Long iliskiliMahkemeKararId;

    @Column(name = "MAHKEME_KARAR_ID")
    private Long mahkemeKararIslemId;

    @Column(name = "MAHKEME_SUC_TIPI_EKLE", length = 25)
    @Size(max = 25)
    private String mahkemeSucTipiKoduEkle;

    @Column(name = "MAHKEME_SUC_TIPI_CIKAR", length = 25)
    @Size(max = 25)
    private String mahkemeSucTipiKoduCikar;

    @Column(name = "TARIH", nullable = false)
    @NotNull
    private LocalDateTime tarih;

    @Column(name = "DURUM", length = 15)
    @Size(max = 15)
    private String durum;

    @Column(name = "MAHKEME_KARAR_DETAY_ID")
    private Long mahkemeKararDetayIslemId;
}
