package iym.common.service.db.mkislem;

import iym.common.model.entity.iym.mkislem.MahkemeKararGuncellemeIslem;
import iym.common.service.db.GenericDbService;

import java.util.Optional;

public interface DbMahkemeKararGuncelleIslemService extends GenericDbService<MahkemeKararGuncellemeIslem, Long> {

    Optional<MahkemeKararGuncellemeIslem> findByDetayMahkemeKararIslemId(Long detayMahkemeKararIslemId);
    
}
