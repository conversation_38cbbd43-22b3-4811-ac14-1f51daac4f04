package iym.db.jpa.dao.mk;

import iym.common.model.entity.iym.mk.MahkemeAidiyat;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for MahkemeKararAidiyat entity
 */
@Repository
public interface MahkemeKararAidiyatRepo extends JpaRepository<MahkemeAidiyat, Long> {

    List<MahkemeAidiyat> findByMahkemeKararId(Long mahkemeKararId);

    Optional<MahkemeAidiyat> findByMahkemeKararIdAndAidiyatKod(Long mahkemeKararId, String aidiyatKod);
}
