/**
 * IYM Backend OpenAPI definition
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { HedefWithAdSoyad } from './hedefWithAdSoyad';
import { MahkemeKararDetay } from './mahkemeKararDetay';


export interface IDHedefDetay { 
    hedefNoAdSoyad: HedefWithAdSoyad;
    baslamaTarihi: string;
    sureTip: IDHedefDetay.SureTipEnum;
    sure: number;
    ilgiliMahkemeKararDetayi?: MahkemeKararDetay;
    uzatmaSayisi?: number;
    hedefAidiyatKodlari?: Array<string>;
    canakNo?: string;
}
export namespace IDHedefDetay {
    export const SureTipEnum = {
        Gun: 'GUN',
        Ay: 'AY',
        Hicbiri: 'HICBIRI'
    } as const;
    export type SureTipEnum = typeof SureTipEnum[keyof typeof SureTipEnum];
}


