/**
 * IYM Backend OpenAPI definition
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { IDAidiyatBilgisiGuncellemeResponse } from './iDAidiyatBilgisiGuncellemeResponse';
import { ResponseIDYeniKararResponseException } from './responseIDYeniKararResponseException';


export interface ResponseIDAidiyatBilgisiGuncellemeResponse { 
    resultCode?: ResponseIDAidiyatBilgisiGuncellemeResponse.ResultCodeEnum;
    resultDetails?: string;
    exception?: ResponseIDYeniKararResponseException;
    result?: IDAidiyatBilgisiGuncellemeResponse;
    success?: boolean;
}
export namespace ResponseIDAidiyatBilgisiGuncellemeResponse {
    export const ResultCodeEnum = {
        Success: 'SUCCESS',
        Failed: 'FAILED',
        Rejected: 'REJECTED'
    } as const;
    export type ResultCodeEnum = typeof ResultCodeEnum[keyof typeof ResultCodeEnum];
}


