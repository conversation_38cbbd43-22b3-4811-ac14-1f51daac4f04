package iym.makos.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import iym.common.testcontainer.OracleTestContainerConfiguration;
import iym.makos.MakosApplication;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.testcontainers.junit.jupiter.Testcontainers;

import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * Integration test to verify that JacksonConfig works correctly in test contexts
 * without requiring the OpenApiConfig or LocalDateTimeDeserializationUtils
 */
@SpringBootTest(classes = {MakosApplication.class})
@Import(OracleTestContainerConfiguration.class)
@Testcontainers
@ActiveProfiles("testcontainers-oracle")
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestPropertySource(properties = {
    "spring.datasource.embedded-database-connection=none",
    "spring.test.database.replace=none"
})
@DisplayName("Jackson Configuration Integration Tests")
@Slf4j
public class JacksonConfigIntegrationTest {

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    @DisplayName("Should load ObjectMapper bean successfully in test context")
    void shouldLoadObjectMapperBean() {
        log.info("✅ Testing ObjectMapper bean availability in test context");
        
        assertNotNull(objectMapper, "ObjectMapper should be available as a bean");
        log.info("✅ ObjectMapper bean is available: {}", objectMapper.getClass().getName());
        
        // Verify basic JSON serialization works
        try {
            String json = objectMapper.writeValueAsString("test");
            assertNotNull(json, "JSON serialization should work");
            log.info("✅ JSON serialization works: {}", json);
        } catch (Exception e) {
            throw new AssertionError("JSON serialization should work", e);
        }
    }

    @Test
    @DisplayName("Should load Spring context without OpenApiConfig when LocalDateTimeDeserializationUtils is not available")
    void shouldLoadContextWithoutOpenApiConfig() {
        log.info("✅ Spring context loaded successfully without OpenApiConfig");
        log.info("✅ This test passing means @ConditionalOnClass is working correctly");
    }
}
