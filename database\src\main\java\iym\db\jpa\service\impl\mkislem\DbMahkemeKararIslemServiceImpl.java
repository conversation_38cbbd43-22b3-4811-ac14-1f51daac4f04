package iym.db.jpa.service.impl.mkislem;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.mkislem.MahkemeKararIslem;
import iym.common.service.db.mkislem.DbMahkemeKararIslemService;
import iym.db.jpa.dao.mkislem.MahkemeKararIslemRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class DbMahkemeKararIslemServiceImpl extends GenericDbServiceImpl<MahkemeKararIslem, Long> implements DbMahkemeKararIslemService {

    private final MahkemeKararIslemRepo mahkemeKararIslemRepo;

    @Autowired
    public DbMahkemeKararIslemServiceImpl(MahkemeKararIslemRepo repository) {
        super(repository);
        this.mahkemeKararIslemRepo = repository;
    }


}
