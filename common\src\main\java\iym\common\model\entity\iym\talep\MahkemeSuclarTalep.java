package iym.common.model.entity.iym.talep;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;

/**
 * Entity class for MAHKEME_SUCLAR_TALEP table
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "MahkemeSuclarTalep")
@Table(name = "MAHKEME_SUCLAR_TALEP")
public class MahkemeSuclarTalep implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "MAHKEME_SUCLAR_TALEP_SEQ")
    @SequenceGenerator(name = "MAHKEME_SUCLAR_TALEP_SEQ", sequenceName = "MAHKEME_SUCLAR_TALEP_SEQ", allocationSize = 1)
    private Long id;

    @Column(name = "MAHKEME_KARAR_ID", nullable = false)
    @NotNull
    private Long mahkemeKararTalepId;

    @Column(name = "MAHKEME_SUC_TIP_KOD", nullable = false, length = 10)
    @NotNull
    @Size(max = 10)
    private String sucTipKodu;

    @Column(name = "DURUMU", length = 20)
    @Size(max = 20)
    private String durumu;
}
