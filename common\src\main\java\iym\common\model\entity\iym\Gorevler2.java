package iym.common.model.entity.iym;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Entity class for GOREVLER2 table
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "Gorevler2")
@Table(name = "GOREVLER2")
@IdClass(Gorevler2PK.class)
public class Gorevler2 implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "GOREV", nullable = false, length = 75)
    @NotNull
    @Size(max = 75)
    private String gorev;

    @Id
    @Column(name = "GOREV_KODU", nullable = false)
    @NotNull
    private Long gorevKodu;

    @Column(name = "GOREV_IMZA_ADI", length = 100)
    @Size(max = 100)
    private String gorevImzaAdi;

    @Column(name = "IMZA_YETKI", length = 1, columnDefinition = "CHAR(1)")
    @Size(max = 1)
    private String imzaYetki;

    @Column(name = "SILINDI")
    private Long silindi;

    @Column(name = "GOREV_KODU2", length = 100)
    @Size(max = 100)
    private String gorevKodu2;

    @Column(name = "GOREV_TIPI", length = 100)
    @Size(max = 100)
    private String gorevTipi;

    @Column(name = "ONCELIK")
    private Long oncelik;

    @Column(name = "BASLAMA_TARIHI")
    private LocalDateTime baslamaTarihi;

    @Column(name = "BITIS_TARIHI")
    private LocalDateTime bitisTarihi;
}
