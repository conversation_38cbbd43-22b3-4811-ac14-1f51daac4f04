-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Enable DBMS_OUTPUT for debugging messages
SET SERVEROUTPUT ON;

BEGIN
  DBMS_OUTPUT.PUT_LINE('=== STARTING: 98_create_tables.sql ===');
  DBMS_OUTPUT.PUT_LINE('This script creates MAKOS-related tables, indexes, and sequences');
  DBMS_OUTPUT.PUT_LINE('Debug messages will show progress and any errors');
END;
/

-- Create sequence for MAKOS_USER if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  DBMS_OUTPUT.PUT_LINE('=== Starting MAKOS_USER_SEQ sequence creation check ===');
  
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = 'MAKOS_USER_SEQ';
  
  IF seq_exists = 0 THEN
    DBMS_OUTPUT.PUT_LINE('MAKOS_USER_SEQ sequence does not exist, creating...');
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.MAKOS_USER_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
    DBMS_OUTPUT.PUT_LINE('MAKOS_USER_SEQ sequence created successfully');
  ELSE
    DBMS_OUTPUT.PUT_LINE('MAKOS_USER_SEQ sequence already exists, skipping creation');
  END IF;
  
  DBMS_OUTPUT.PUT_LINE('=== Completed MAKOS_USER_SEQ sequence creation check ===');
EXCEPTION
  WHEN OTHERS THEN
    DBMS_OUTPUT.PUT_LINE('ERROR in MAKOS_USER_SEQ sequence creation: ' || SQLCODE || ' - ' || SQLERRM);
    RAISE;
END;
/

-- Create MAKOS_USER table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  DBMS_OUTPUT.PUT_LINE('=== Starting MAKOS_USER table creation check ===');
  
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'MAKOS_USER';
  
  IF table_exists = 0 THEN
    DBMS_OUTPUT.PUT_LINE('MAKOS_USER table does not exist, creating...');
    EXECUTE IMMEDIATE 'CREATE TABLE iym.MAKOS_USER (
      ID NUMBER NOT NULL,
      USERNAME VARCHAR2(100 BYTE) NOT NULL,
      PASSWORD VARCHAR2(100 BYTE) NOT NULL,
      STATUS VARCHAR2(20 BYTE) NOT NULL,
      ROLE VARCHAR2(20 BYTE) NOT NULL,
      KURUM  VARCHAR2(10 BYTE) NOT NULL,
      CONSTRAINT MAKOS_USER_ID_IDX PRIMARY KEY (ID),
      CONSTRAINT makos_user_un UNIQUE (USERNAME)
    )';
    DBMS_OUTPUT.PUT_LINE('MAKOS_USER table created successfully');
  ELSE
    DBMS_OUTPUT.PUT_LINE('MAKOS_USER table already exists, skipping creation');
  END IF;
  
  DBMS_OUTPUT.PUT_LINE('=== Completed MAKOS_USER table creation check ===');
EXCEPTION
  WHEN OTHERS THEN
    DBMS_OUTPUT.PUT_LINE('ERROR in MAKOS_USER table creation: ' || SQLCODE || ' - ' || SQLERRM);
    RAISE;
END;
/

-- Create indexes for MAKOS_USER if they don't exist
BEGIN
  DBMS_OUTPUT.PUT_LINE('=== Starting MAKOS_USER indexes creation ===');
  
  -- Try to create index on STATUS for filtering active/inactive users
  BEGIN
    DBMS_OUTPUT.PUT_LINE('Creating MAKOS_USER_STATUS_IDX index...');
    EXECUTE IMMEDIATE 'CREATE INDEX iym.MAKOS_USER_STATUS_IDX ON iym.MAKOS_USER (STATUS)';
    DBMS_OUTPUT.PUT_LINE('MAKOS_USER_STATUS_IDX index created successfully');
  EXCEPTION
    WHEN OTHERS THEN
      IF SQLCODE = -1408 OR SQLCODE = -955 THEN -- ORA-01408: such column list already indexed, ORA-00955: name already used
        DBMS_OUTPUT.PUT_LINE('MAKOS_USER_STATUS_IDX index already exists, skipping');
      ELSE
        DBMS_OUTPUT.PUT_LINE('ERROR creating MAKOS_USER_STATUS_IDX: ' || SQLCODE || ' - ' || SQLERRM);
        RAISE; -- Re-raise any other error
      END IF;
  END;

  -- Try to create index on KURUM for filtering by institution
  BEGIN
    DBMS_OUTPUT.PUT_LINE('Creating MAKOS_USER_KURUM_IDX index...');
    EXECUTE IMMEDIATE 'CREATE INDEX iym.MAKOS_USER_KURUM_IDX ON iym.MAKOS_USER (KURUM)';
    DBMS_OUTPUT.PUT_LINE('MAKOS_USER_KURUM_IDX index created successfully');
  EXCEPTION
    WHEN OTHERS THEN
      IF SQLCODE = -1408 OR SQLCODE = -955 THEN -- ORA-01408: such column list already indexed, ORA-00955: name already used
        DBMS_OUTPUT.PUT_LINE('MAKOS_USER_KURUM_IDX index already exists, skipping');
      ELSE
        DBMS_OUTPUT.PUT_LINE('ERROR creating MAKOS_USER_KURUM_IDX: ' || SQLCODE || ' - ' || SQLERRM);
        RAISE; -- Re-raise any other error
      END IF;
  END;

  -- Try to create composite index on ROLE and STATUS for common queries
  BEGIN
    DBMS_OUTPUT.PUT_LINE('Creating MAKOS_USER_ROLE_STATUS_IDX index...');
    EXECUTE IMMEDIATE 'CREATE INDEX iym.MAKOS_USER_ROLE_STATUS_IDX ON iym.MAKOS_USER (ROLE, STATUS)';
    DBMS_OUTPUT.PUT_LINE('MAKOS_USER_ROLE_STATUS_IDX index created successfully');
  EXCEPTION
    WHEN OTHERS THEN
      IF SQLCODE = -1408 OR SQLCODE = -955 THEN -- ORA-01408: such column list already indexed, ORA-00955: name already used
        DBMS_OUTPUT.PUT_LINE('MAKOS_USER_ROLE_STATUS_IDX index already exists, skipping');
      ELSE
        DBMS_OUTPUT.PUT_LINE('ERROR creating MAKOS_USER_ROLE_STATUS_IDX: ' || SQLCODE || ' - ' || SQLERRM);
        RAISE; -- Re-raise any other error
      END IF;
  END;
  
  DBMS_OUTPUT.PUT_LINE('=== Completed MAKOS_USER indexes creation ===');
EXCEPTION
  WHEN OTHERS THEN
    DBMS_OUTPUT.PUT_LINE('ERROR in MAKOS_USER indexes creation: ' || SQLCODE || ' - ' || SQLERRM);
    RAISE;
END;
/

-- Create sequence for MAKOS_USER_AUDIT_LOG if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  DBMS_OUTPUT.PUT_LINE('=== Starting MAKOS_USER_AUDIT_LOG sequence creation check ===');

  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = 'MAKOS_USER_AUDIT_LOG_SEQ';

  IF seq_exists = 0 THEN
    DBMS_OUTPUT.PUT_LINE('MAKOS_USER_AUDIT_LOG_SEQ sequence does not exist, creating...');
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.MAKOS_USER_AUDIT_LOG_SEQ START WITH 1 INCREMENT BY 1 NOCACHE';
    DBMS_OUTPUT.PUT_LINE('MAKOS_USER_AUDIT_LOG_SEQ sequence created successfully');
  ELSE
    DBMS_OUTPUT.PUT_LINE('MAKOS_USER_AUDIT_LOG_SEQ sequence already exists, skipping creation');
  END IF;

  DBMS_OUTPUT.PUT_LINE('=== Completed MAKOS_USER_AUDIT_LOG sequence creation check ===');
EXCEPTION
  WHEN OTHERS THEN
    DBMS_OUTPUT.PUT_LINE('ERROR in MAKOS_USER_AUDIT_LOG sequence creation: ' || SQLCODE || ' - ' || SQLERRM);
    RAISE;
END;
/

-- Create MAKOS_USER_AUDIT_LOG table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  DBMS_OUTPUT.PUT_LINE('=== Starting MAKOS_USER_AUDIT_LOG table creation check ===');
  
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'MAKOS_USER_AUDIT_LOG';
  
  IF table_exists = 0 THEN
    DBMS_OUTPUT.PUT_LINE('MAKOS_USER_AUDIT_LOG table does not exist, creating...');
    EXECUTE IMMEDIATE 'CREATE TABLE iym.MAKOS_USER_AUDIT_LOG (
      ID NUMBER NOT NULL PRIMARY KEY,
      USER_AUDIT_TYPE VARCHAR2(100 BYTE) NOT NULL,
      USERNAME VARCHAR2(100 BYTE),
      ACTING_USERNAME VARCHAR2(100 BYTE),
      USER_IP VARCHAR2(100 BYTE),
      ADMIN_OPERATED_USERNAME VARCHAR2(100 BYTE),
      REQUEST_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
      RESPONSE_TIME TIMESTAMP,
      RESPONSE_CODE NUMBER
    )';
    DBMS_OUTPUT.PUT_LINE('MAKOS_USER_AUDIT_LOG table created successfully');
  ELSE
    DBMS_OUTPUT.PUT_LINE('MAKOS_USER_AUDIT_LOG table already exists, skipping creation');
  END IF;
  
  DBMS_OUTPUT.PUT_LINE('=== Completed MAKOS_USER_AUDIT_LOG table creation check ===');
EXCEPTION
  WHEN OTHERS THEN
    DBMS_OUTPUT.PUT_LINE('ERROR in MAKOS_USER_AUDIT_LOG table creation: ' || SQLCODE || ' - ' || SQLERRM);
    RAISE;
END;
/

-- Create indexes for MAKOS_USER_AUDIT_LOG if they don't exist
BEGIN
  DBMS_OUTPUT.PUT_LINE('=== Starting MAKOS_USER_AUDIT_LOG indexes creation ===');

  -- Try to create index on USER_AUDIT_TYPE for filtering by audit type
  BEGIN
    DBMS_OUTPUT.PUT_LINE('Creating MAKOS_USER_AUDIT_LOG_TYPE_IDX index...');
    EXECUTE IMMEDIATE 'CREATE INDEX iym.MAKOS_USER_AUDIT_LOG_TYPE_IDX ON iym.MAKOS_USER_AUDIT_LOG (USER_AUDIT_TYPE)';
    DBMS_OUTPUT.PUT_LINE('MAKOS_USER_AUDIT_LOG_TYPE_IDX index created successfully');
  EXCEPTION
    WHEN OTHERS THEN
      IF SQLCODE = -1408 OR SQLCODE = -955 THEN -- ORA-01408: such column list already indexed, ORA-00955: name already used
        DBMS_OUTPUT.PUT_LINE('MAKOS_USER_AUDIT_LOG_TYPE_IDX index already exists, skipping');
      ELSE
        DBMS_OUTPUT.PUT_LINE('ERROR creating MAKOS_USER_AUDIT_LOG_TYPE_IDX: ' || SQLCODE || ' - ' || SQLERRM);
        RAISE; -- Re-raise any other error
      END IF;
  END;

  -- Try to create index on USERNAME for filtering by user
  BEGIN
    DBMS_OUTPUT.PUT_LINE('Creating MAKOS_USER_AUDIT_LOG_USER_IDX index...');
    EXECUTE IMMEDIATE 'CREATE INDEX iym.MAKOS_USER_AUDIT_LOG_USER_IDX ON iym.MAKOS_USER_AUDIT_LOG (USERNAME)';
    DBMS_OUTPUT.PUT_LINE('MAKOS_USER_AUDIT_LOG_USER_IDX index created successfully');
  EXCEPTION
    WHEN OTHERS THEN
      IF SQLCODE = -1408 OR SQLCODE = -955 THEN -- ORA-01408: such column list already indexed, ORA-00955: name already used
        DBMS_OUTPUT.PUT_LINE('MAKOS_USER_AUDIT_LOG_USER_IDX index already exists, skipping');
      ELSE
        DBMS_OUTPUT.PUT_LINE('ERROR creating MAKOS_USER_AUDIT_LOG_USER_IDX: ' || SQLCODE || ' - ' || SQLERRM);
        RAISE; -- Re-raise any other error
      END IF;
  END;

  -- Try to create index on REQUEST_TIME for time-based queries
  BEGIN
    DBMS_OUTPUT.PUT_LINE('Creating MAKOS_USER_AUDIT_LOG_TIME_IDX index...');
    EXECUTE IMMEDIATE 'CREATE INDEX iym.MAKOS_USER_AUDIT_LOG_TIME_IDX ON iym.MAKOS_USER_AUDIT_LOG (REQUEST_TIME)';
    DBMS_OUTPUT.PUT_LINE('MAKOS_USER_AUDIT_LOG_TIME_IDX index created successfully');
  EXCEPTION
    WHEN OTHERS THEN
      IF SQLCODE = -1408 OR SQLCODE = -955 THEN -- ORA-01408: such column list already indexed, ORA-00955: name already used
        DBMS_OUTPUT.PUT_LINE('MAKOS_USER_AUDIT_LOG_TIME_IDX index already exists, skipping');
      ELSE
        DBMS_OUTPUT.PUT_LINE('ERROR creating MAKOS_USER_AUDIT_LOG_TIME_IDX: ' || SQLCODE || ' - ' || SQLERRM);
        RAISE; -- Re-raise any other error
      END IF;
  END;

  -- Try to create composite index on USERNAME and REQUEST_TIME for common queries
  BEGIN
    DBMS_OUTPUT.PUT_LINE('Creating MAKOS_USER_AUDIT_USR_TIME_IDX index...');
    EXECUTE IMMEDIATE 'CREATE INDEX iym.MAKOS_USER_AUDIT_USR_TIME_IDX ON iym.MAKOS_USER_AUDIT_LOG (USERNAME, REQUEST_TIME)';
    DBMS_OUTPUT.PUT_LINE('MAKOS_USER_AUDIT_USR_TIME_IDX index created successfully');
  EXCEPTION
    WHEN OTHERS THEN
      IF SQLCODE = -1408 OR SQLCODE = -955 THEN -- ORA-01408: such column list already indexed, ORA-00955: name already used
        DBMS_OUTPUT.PUT_LINE('MAKOS_USER_AUDIT_USR_TIME_IDX index already exists, skipping');
      ELSE
        DBMS_OUTPUT.PUT_LINE('ERROR creating MAKOS_USER_AUDIT_USR_TIME_IDX: ' || SQLCODE || ' - ' || SQLERRM);
        RAISE; -- Re-raise any other error
      END IF;
  END;

  DBMS_OUTPUT.PUT_LINE('=== Completed MAKOS_USER_AUDIT_LOG indexes creation ===');
EXCEPTION
  WHEN OTHERS THEN
    DBMS_OUTPUT.PUT_LINE('ERROR in MAKOS_USER_AUDIT_LOG indexes creation: ' || SQLCODE || ' - ' || SQLERRM);
    RAISE;
END;
/

-- Create sequence for MAKOS_KARAR_REQUEST_LOG if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  DBMS_OUTPUT.PUT_LINE('=== Starting MAKOS_KARAR_REQUEST_LOG sequence creation check ===');

  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = 'MAKOS_KARAR_REQUEST_LOG_SEQ';

  IF seq_exists = 0 THEN
    DBMS_OUTPUT.PUT_LINE('MAKOS_KARAR_REQUEST_LOG_SEQ sequence does not exist, creating...');
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.MAKOS_KARAR_REQUEST_LOG_SEQ START WITH 1 INCREMENT BY 1 NOCACHE';
    DBMS_OUTPUT.PUT_LINE('MAKOS_KARAR_REQUEST_LOG_SEQ sequence created successfully');
  ELSE
    DBMS_OUTPUT.PUT_LINE('MAKOS_KARAR_REQUEST_LOG_SEQ sequence already exists, skipping creation');
  END IF;

  DBMS_OUTPUT.PUT_LINE('=== Completed MAKOS_KARAR_REQUEST_LOG sequence creation check ===');
EXCEPTION
  WHEN OTHERS THEN
    DBMS_OUTPUT.PUT_LINE('ERROR in MAKOS_KARAR_REQUEST_LOG sequence creation: ' || SQLCODE || ' - ' || SQLERRM);
    RAISE;
END;
/

-- Create MAKOS_KARAR_REQUEST_LOG table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  DBMS_OUTPUT.PUT_LINE('=== Starting MAKOS_KARAR_REQUEST_LOG table creation check ===');

  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'MAKOS_KARAR_REQUEST_LOG';

  IF table_exists = 0 THEN
    DBMS_OUTPUT.PUT_LINE('MAKOS_KARAR_REQUEST_LOG table does not exist, creating...');
    EXECUTE IMMEDIATE 'CREATE TABLE iym.MAKOS_KARAR_REQUEST_LOG (
      ID NUMBER NOT NULL,
      REQUEST_ID VARCHAR2(36) NOT NULL,
      REQUEST_URL VARCHAR2(500 BYTE),
      REQUEST_TIME TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
      USERNAME VARCHAR2(100 BYTE),
      ACTING_USERNAME VARCHAR2(100 BYTE),
      USER_IP VARCHAR2(100 BYTE),
      EVRAK_NO VARCHAR2(50 BYTE),
      MAHKEME_KARAR_NO VARCHAR2(20 BYTE),
      SORUSTURMA_NO VARCHAR2(20 BYTE),
      REQUEST_BODY CLOB,
      RESPONSE_CODE NUMBER,
      RESPONSE_TIME TIMESTAMP,
      RESPONSE_BODY CLOB,
      CONSTRAINT MAKOS_KARAR_REQUEST_LOG_PK PRIMARY KEY (ID)
    )';
    DBMS_OUTPUT.PUT_LINE('MAKOS_KARAR_REQUEST_LOG table created successfully');
  ELSE
    DBMS_OUTPUT.PUT_LINE('MAKOS_KARAR_REQUEST_LOG table already exists, skipping creation');
  END IF;

  DBMS_OUTPUT.PUT_LINE('=== Completed MAKOS_KARAR_REQUEST_LOG table creation check ===');
EXCEPTION
  WHEN OTHERS THEN
    DBMS_OUTPUT.PUT_LINE('ERROR in MAKOS_KARAR_REQUEST_LOG table creation: ' || SQLCODE || ' - ' || SQLERRM);
    RAISE;
END;
/

-- Create indexes for MAKOS_KARAR_REQUEST_LOG if they don't exist
BEGIN
  DBMS_OUTPUT.PUT_LINE('=== Starting MAKOS_KARAR_REQUEST_LOG indexes creation ===');

  -- Try to create index on REQUEST_TIME for time-based queries
  BEGIN
    DBMS_OUTPUT.PUT_LINE('Creating MAKOS_KARAR_REQ_LOG_TIME_IDX index...');
    EXECUTE IMMEDIATE 'CREATE INDEX iym.MAKOS_KARAR_REQ_LOG_TIME_IDX ON iym.MAKOS_KARAR_REQUEST_LOG (REQUEST_TIME)';
    DBMS_OUTPUT.PUT_LINE('MAKOS_KARAR_REQ_LOG_TIME_IDX index created successfully');
  EXCEPTION
    WHEN OTHERS THEN
      IF SQLCODE = -1408 OR SQLCODE = -955 THEN -- ORA-01408: such column list already indexed, ORA-00955: name already used
        DBMS_OUTPUT.PUT_LINE('MAKOS_KARAR_REQ_LOG_TIME_IDX index already exists, skipping');
      ELSE
        DBMS_OUTPUT.PUT_LINE('ERROR creating MAKOS_KARAR_REQ_LOG_TIME_IDX: ' || SQLCODE || ' - ' || SQLERRM);
        RAISE; -- Re-raise any other error
      END IF;
  END;

  -- Try to create index on USERNAME for filtering by user
  BEGIN
    DBMS_OUTPUT.PUT_LINE('Creating MAKOS_KARAR_REQ_LOG_USER_IDX index...');
    EXECUTE IMMEDIATE 'CREATE INDEX iym.MAKOS_KARAR_REQ_LOG_USER_IDX ON iym.MAKOS_KARAR_REQUEST_LOG (USERNAME)';
    DBMS_OUTPUT.PUT_LINE('MAKOS_KARAR_REQ_LOG_USER_IDX index created successfully');
  EXCEPTION
    WHEN OTHERS THEN
      IF SQLCODE = -1408 OR SQLCODE = -955 THEN -- ORA-01408: such column list already indexed, ORA-00955: name already used
        DBMS_OUTPUT.PUT_LINE('MAKOS_KARAR_REQ_LOG_USER_IDX index already exists, skipping');
      ELSE
        DBMS_OUTPUT.PUT_LINE('ERROR creating MAKOS_KARAR_REQ_LOG_USER_IDX: ' || SQLCODE || ' - ' || SQLERRM);
        RAISE; -- Re-raise any other error
      END IF;
  END;

  -- Try to create index on MAHKEME_KARAR_NO for filtering by court decision number
  BEGIN
    DBMS_OUTPUT.PUT_LINE('Creating MAKOS_KARAR_REQ_LOG_KARAR_IDX index...');
    EXECUTE IMMEDIATE 'CREATE INDEX iym.MAKOS_KARAR_REQ_LOG_KARAR_IDX ON iym.MAKOS_KARAR_REQUEST_LOG (MAHKEME_KARAR_NO)';
    DBMS_OUTPUT.PUT_LINE('MAKOS_KARAR_REQ_LOG_KARAR_IDX index created successfully');
  EXCEPTION
    WHEN OTHERS THEN
      IF SQLCODE = -1408 OR SQLCODE = -955 THEN -- ORA-01408: such column list already indexed, ORA-00955: name already used
        DBMS_OUTPUT.PUT_LINE('MAKOS_KARAR_REQ_LOG_KARAR_IDX index already exists, skipping');
      ELSE
        DBMS_OUTPUT.PUT_LINE('ERROR creating MAKOS_KARAR_REQ_LOG_KARAR_IDX: ' || SQLCODE || ' - ' || SQLERRM);
        RAISE; -- Re-raise any other error
      END IF;
  END;

  -- Try to create index on SORUSTURMA_NO for filtering by investigation number
  BEGIN
    DBMS_OUTPUT.PUT_LINE('Creating MAKOS_KARAR_REQ_LOG_SOR_IDX index...');
    EXECUTE IMMEDIATE 'CREATE INDEX iym.MAKOS_KARAR_REQ_LOG_SOR_IDX ON iym.MAKOS_KARAR_REQUEST_LOG (SORUSTURMA_NO)';
    DBMS_OUTPUT.PUT_LINE('MAKOS_KARAR_REQ_LOG_SOR_IDX index created successfully');
  EXCEPTION
    WHEN OTHERS THEN
      IF SQLCODE = -1408 OR SQLCODE = -955 THEN -- ORA-01408: such column list already indexed, ORA-00955: name already used
        DBMS_OUTPUT.PUT_LINE('MAKOS_KARAR_REQ_LOG_SOR_IDX index already exists, skipping');
      ELSE
        DBMS_OUTPUT.PUT_LINE('ERROR creating MAKOS_KARAR_REQ_LOG_SOR_IDX: ' || SQLCODE || ' - ' || SQLERRM);
        RAISE; -- Re-raise any other error
      END IF;
  END;

  -- Try to create composite index on USERNAME and REQUEST_TIME for common queries
  BEGIN
    DBMS_OUTPUT.PUT_LINE('Creating MAKOS_KARAR_REQ_USR_TIME_IDX index...');
    EXECUTE IMMEDIATE 'CREATE INDEX iym.MAKOS_KARAR_REQ_USR_TIME_IDX ON iym.MAKOS_KARAR_REQUEST_LOG (USERNAME, REQUEST_TIME)';
    DBMS_OUTPUT.PUT_LINE('MAKOS_KARAR_REQ_USR_TIME_IDX index created successfully');
  EXCEPTION
    WHEN OTHERS THEN
      IF SQLCODE = -1408 OR SQLCODE = -955 THEN -- ORA-01408: such column list already indexed, ORA-00955: name already used
        DBMS_OUTPUT.PUT_LINE('MAKOS_KARAR_REQ_USR_TIME_IDX index already exists, skipping');
      ELSE
        DBMS_OUTPUT.PUT_LINE('ERROR creating MAKOS_KARAR_REQ_USR_TIME_IDX: ' || SQLCODE || ' - ' || SQLERRM);
        RAISE; -- Re-raise any other error
      END IF;
  END;

  DBMS_OUTPUT.PUT_LINE('=== Completed MAKOS_KARAR_REQUEST_LOG indexes creation ===');
EXCEPTION
  WHEN OTHERS THEN
    DBMS_OUTPUT.PUT_LINE('ERROR in MAKOS_KARAR_REQUEST_LOG indexes creation: ' || SQLCODE || ' - ' || SQLERRM);
    RAISE;
END;
/

BEGIN
  DBMS_OUTPUT.PUT_LINE('=== COMPLETED: 98_create_tables.sql ===');
  DBMS_OUTPUT.PUT_LINE('All MAKOS-related tables, indexes, and sequences have been processed');
  DBMS_OUTPUT.PUT_LINE('Check the output above for any errors or warnings');
END;
/
