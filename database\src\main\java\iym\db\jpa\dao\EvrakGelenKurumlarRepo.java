package iym.db.jpa.dao;

import iym.common.model.entity.iym.EvrakGelenKurumlar;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for EvrakGelenKurumlar entity
 */
@Repository
public interface EvrakGelenKurumlarRepo extends JpaRepository<EvrakGelenKurumlar, Long> {

    Optional<EvrakGelenKurumlar> findByKurumKod(String kurumKod);
    
    List<EvrakGelenKurumlar> findByKurumAdi(String kurumAdi);
    
    List<EvrakGelenKurumlar> findByKurum(String kurum);
    
    List<EvrakGelenKurumlar> findByIdx(Long idx);
    
    List<EvrakGelenKurumlar> findByKurumAdiContainingIgnoreCase(String kurumAdi);
    
    List<EvrakGelenKurumlar> findByKurumContainingIgnoreCase(String kurum);
    
    List<EvrakGelenKurumlar> findByKurumKodContaining(String kurumKod);
    
    List<EvrakGelenKurumlar> findAllByOrderByIdxAsc();
    
    boolean existsByKurumKod(String kurumKod);
    
    boolean existsByKurumAdi(String kurumAdi);
}
