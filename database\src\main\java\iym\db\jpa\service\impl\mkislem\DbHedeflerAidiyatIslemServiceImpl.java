package iym.db.jpa.service.impl.mkislem;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.mkislem.HedeflerAidiyatIslem;
import iym.common.service.db.mkislem.DbHedeflerAidiyatIslemService;
import iym.db.jpa.dao.mkislem.HedeflerAidiyatIslemRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Service
public class DbHedeflerAidiyatIslemServiceImpl extends GenericDbServiceImpl<HedeflerAidiyatIslem, Long> implements DbHedeflerAidiyatIslemService {

    private final HedeflerAidiyatIslemRepo hedeflerAidiyatIslemRepo;

    @Autowired
    public DbHedeflerAidiyatIslemServiceImpl(HedeflerAidiyatIslemRepo repository) {
        super(repository);
        this.hedeflerAidiyatIslemRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public List<HedeflerAidiyatIslem> findByHedeflerIslemId(Long hedefId){
        return null;//return hedeflerAidiyatIslemRepo.fin
    }

}
