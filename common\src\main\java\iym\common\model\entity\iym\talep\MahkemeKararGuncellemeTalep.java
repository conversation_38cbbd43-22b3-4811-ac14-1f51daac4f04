package iym.common.model.entity.iym.talep;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;

/**
 * Entity class for MAHKEME_KARAR_BILGI_DETAY_TALEP table
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "MahkemeKararGuncellemeTalep")
@Table(name = "MAHKEME_KARAR_GUNCELLEME_TALEP")
public class MahkemeKararGuncellemeTalep implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "MK_GUNCELLEME_TALEP_SEQ")
    @SequenceGenerator(name = "MK_GUNCELLEME_TALEP_SEQ", sequenceName = "MK_GUNCELLEME_TALEP_SEQ", allocationSize = 1)
    private Long id;

    @Column(name = "MAHKEME_KARAR_DETAY_ID", nullable = false)
    @NotNull
    private Long detayMahkemeKararTalepId;

    @Column(name = "MAHKEME_KODU", length = 25)
    @Size(max = 25)
    private String mahkemeKodu;

    @Column(name = "SORUSTURMA_NO", length = 50)
    @Size(max = 25)
    private String sorusturmaNo;

    @Column(name = "MAHKEME_KARAR_NO", length = 50)
    @Size(max = 25)
    private String mahkemeKararNo;

    @Column(name = "DURUMU", length = 10)
    @Size(max = 10)
    private String durumu;

    @Column(name = "UPDATE_COLUMN_NAMES", length = 11)
    @Size(max = 11)
    private String updateColumnNames;

}
