package iym.backend.shared.service;

import iym.backend.shared.mapper.BaseMapper;
import iym.backend.shared.repository.BaseRepository;

import java.util.List;

public abstract class BaseServiceImpl<TEntity, TDto, ID> implements BaseService<TDto, ID> {

    protected final BaseRepository<TEntity, ID> repository;
    protected final BaseMapper<TEntity, TDto> mapper;

    public BaseServiceImpl(BaseRepository<TEntity, ID> repository, BaseMapper<TEntity, TDto> mapper) {
        this.repository = repository;
        this.mapper = mapper;
    }

    @Override
    public List<TDto> findAll() {
        return mapper.toDtoList(repository.findAll());
    }

    @Override
    public TDto findById(ID id) {
        TEntity entity = repository.findById(id).orElseThrow(() -> new RuntimeException("Veri bulunamadı"));
        return mapper.toDto(entity);
    }

    @Override
    public TDto save(TDto dto) {
        TEntity entity = mapper.toEntity(dto);
        TEntity newEntity = repository.save(entity);
        return mapper.toDto(newEntity);
    }

    @Override
    public void delete(ID id) {
        TEntity entity = repository.findById(id)
                .orElseThrow(() -> new RuntimeException("Silinecek veri bulunamadı"));
        repository.delete(entity);
    }


    @Override
    public TDto update(ID id, TDto dto) {
        TEntity existing = repository.findById(id)
                .orElseThrow(() -> new RuntimeException("Güncellenecek veri bulunamadı"));

        // mapper üzerinden doğrudan güncelleme
        mapper.updateEntityFromDto(dto, existing);

        return mapper.toDto(repository.save(existing));
    }



}

