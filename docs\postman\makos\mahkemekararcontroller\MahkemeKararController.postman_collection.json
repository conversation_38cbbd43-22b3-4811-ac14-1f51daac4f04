{"info": {"_postman_id": "b1e1e1e1-0000-0000-0000-000000000001", "name": "MahkemeKararTalepController Test Collection", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "description": "MakosKararRequestLogFilter testleri için <PERSON>ararTalepController endpointleri. Basic Auth: makos_admin / 123456"}, "item": [{"name": "yeniKararID", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{makos_basic_auth}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "ma<PERSON>kemeKara<PERSON><PERSON><PERSON>", "type": "file", "src": ""}, {"key": "mahkemeKararDetay", "type": "text", "value": "{\"id\":\"11111111-1111-1111-1111-111111111111\",\"kararTuru\":\"ILETISIMIN_DENETLENMESI_YENI\",\"evrakDetay\":{\"evrakNo\":\"EV123456\",\"evrak<PERSON><PERSON><PERSON>\":\"2024-05-01T10:00:00\",\"evrakKurumKodu\":\"01\",\"evrakTuru\":\"GENEL_EVRAK\",\"havaleBirimi\":\"BIRIM1\",\"aciklama\":\"Açıklama örnek\",\"geldigiIlIlceKodu\":\"34\",\"acilmi\":false,\"evrakKonusu\":\"<PERSON><PERSON> örnek\"},\"mahkemeKararBilgisi\":{\"mahkemeKararTipi\":\"ONLEYICI_HAKIM_KARARI\",\"mahkemeKararDetay\":{\"mahkemeKodu\":\"08030100\",\"mahkemeIlIlceKodu\":\"34\",\"mahkemeKararNo\":\"MKNO123\",\"sorusturmaNo\":\"SRST123\",\"aciklama\":\"<PERSON><PERSON> açı<PERSON>\"}},\"hedefDetayListesi\":[{\"hedefNoAdSoyad\":{\"hedef\":{\"hedefNo\":\"9050517\",\"hedefTip\":\"GSM\"},\"hedefAd\":\"Veli\",\"hedefSoyad\":\"Ali\"},\"baslamaTarihi\":\"2024-05-01T10:00:00\",\"sureTip\":\"GUN\",\"sure\":30,\"ilgiliMahkemeKararDetayi\":null,\"uzatmaSayisi\":null,\"hedefAidiyatKodlari\":[\"AID1\",\"AID2\"],\"canakNo\":null}],\"mahkemeAidiyatKodlari\":[\"MT12345\",\"MİT1\"],\"mahkemeSucTipiKodlari\":[\"00\",\"37\"]}"}]}, "url": {"raw": "{{baseUrl}}{{makosApiPath}}/mahkemeKararTalep/yeniKararID", "host": ["{{baseUrl}}"], "path": ["{{makosApiPath}}", "mahkemeKararTalep", "yeniKararID"]}}}, {"name": "kararGonderIT", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{makos_basic_auth}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "mahkemeKararDosyasiIT", "type": "file", "src": ""}, {"key": "mahkemeKararDetayIT", "type": "text", "value": "{\n  \"id\": \"22222222-2222-2222-2222-222222222222\",\n  \"kararTuru\": \"YEN<PERSON>\",\n  \"evrakDetay\": {\n    \"evrakNo\": \"EV654321\",\n    \"evrak<PERSON><PERSON><PERSON>\": \"2024-05-01T10:00:00\",\n    \"evrakKurumKodu\": \"KURUM02\",\n    \"evrakTuru\": \"GENEL_EVRAK\",\n    \"havaleBirimi\": \"BIRIM2\",\n    \"aciklama\": \"Açıklama IT\",\n    \"geldigiIlIlceKodu\": \"06\",\n    \"acilmi\": false,\n    \"evrakKonusu\": \"Konu IT\"\n  },\n  \"mahkemeKararDetay\": {\n    \"mahkemeKararBilgisi\": {\n      \"mahkemeKararTipi\": \"ONLEYICI_HAKIM_KARARI\",\n      \"mahkemeKararDetay\": {\n        \"mahkemeKodu\": \"08030300\",\n        \"mahkemeIlIlceKodu\": \"06\",\n        \"mahkemeKararNo\": \"MKNO456\",\n        \"sorusturmaNo\": \"SRST456\",\n        \"aciklama\": \"<PERSON>r açıklaması IT\"\n      }\n    },\n    \"hedefDetayListesi\": []\n  }\n}"}]}, "url": {"raw": "{{baseUrl}}{{makosApiPath}}/mahkemeKararTalep/kararGonderIT", "host": ["{{baseUrl}}"], "path": ["{{makosApiPath}}", "mahkemeKararTalep", "kararGonderIT"]}}}, {"name": "aidiyatBilgisiGuncelle", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{makos_basic_auth}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "ma<PERSON>kemeKara<PERSON><PERSON><PERSON>", "type": "file", "src": ""}, {"key": "mahkemeKararDetay", "type": "text", "value": "{\n  \"id\": \"33333333-3333-3333-3333-333333333333\",\n  \"evrakDetay\": {\n    \"evrakNo\": \"EV111222\",\n    \"evrakT<PERSON><PERSON>\": \"2024-05-01T10:00:00\",\n    \"evrak<PERSON>urumKodu\": \"KURUM03\",\n    \"evrak<PERSON><PERSON>\": \"GENEL_EVRAK\",\n    \"havaleBirimi\": \"BIRIM3\",\n    \"aciklama\": \"Aidiyat açıklama\",\n    \"geldigiIlIlceKodu\": \"35\",\n    \"acilmi\": false,\n    \"evrakKonusu\": \"Aidiyat <PERSON>nu\"\n  },\n  \"mahkemeKararBilgisi\": {\n    \"mahkemeKararTipi\": \"ONLEYICI_HAKIM_KARARI\",\n    \"mahkemeKararDetay\": {\n      \"mahkemeKodu\": \"08000100\",\n      \"mahkemeIlIlceKodu\": \"35\",\n      \"mahkemeKararNo\": \"MKNO789\",\n      \"sorusturmaNo\": \"SRST789\",\n      \"aciklama\": \"Aidiyat karar açıkla<PERSON>\"\n    }\n  },\n  \"aidiyatGuncellemeKararDetayListesi\": [\n    {\n      \"mahkemeKararDetay\": {\n        \"mahkemeKodu\": \"08000100\",\n        \"mahkemeIlIlceKodu\": \"35\",\n        \"mahkemeKararNo\": \"MKNO789\",\n        \"sorusturmaNo\": \"SRST789\",\n        \"aciklama\": \"Aidiyat karar açıklaması\"\n      },\n      \"aidiyatGuncellemeDetayList\": [\n        {\n          \"guncellemeTip\": \"EKLE\",\n          \"aidiyatKodu\": \"AID2\"\n        }\n      ]\n    }\n  ]\n}"}]}, "url": {"raw": "{{baseUrl}}{{makosApiPath}}/mahkemeKararTalep/aidiyatBilgisiGuncelle", "host": ["{{baseUrl}}"], "path": ["{{makosApiPath}}", "mahkemeKararTalep", "aidiyatBilgisiGuncelle"]}}}, {"name": "he<PERSON>fBilgi<PERSON><PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{makos_basic_auth}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "ma<PERSON>kemeKara<PERSON><PERSON><PERSON>", "type": "file", "src": ""}, {"key": "mahkemeKararDetay", "type": "text", "value": "{\n  \"id\": \"44444444-4444-4444-4444-444444444444\",\n  \"evrakDetay\": {\n    \"evrakNo\": \"EV333444\",\n    \"evrakT<PERSON><PERSON>\": \"2024-05-01T10:00:00\",\n    \"evrak<PERSON>urumKodu\": \"KURUM04\",\n    \"evrak<PERSON><PERSON>\": \"GENEL_EVRAK\",\n    \"havaleBirimi\": \"BIRIM4\",\n    \"aciklama\": \"Ad soyad açıklama\",\n    \"geldigiIlIlceKodu\": \"01\",\n    \"acilmi\": false,\n    \"evrakKonusu\": \"Ad Soyad Konu\"\n  },\n  \"mahkemeKararBilgisi\": {\n    \"mahkemeKararTipi\": \"ONLEYICI_HAKIM_KARARI\",\n    \"mahkemeKararDetay\": {\n      \"mahkemeKodu\": \"06000101\",\n      \"mahkemeIlIlceKodu\": \"01\",\n      \"mahkemeKararNo\": \"MKNO101\",\n      \"sorusturmaNo\": \"SRST101\",\n      \"aciklama\": \"Ad soyad karar açıklaması\"\n    }\n  },\n  \"hedefAdSoyadGuncellemeKararDetayListesi\": [\n    {\n      \"mahkemeKararDetay\": {\n        \"mahkemeKodu\": \"06000101\",\n        \"mahkemeIlIlceKodu\": \"01\",\n        \"mahkemeKararNo\": \"MKNO101\",\n        \"sorusturmaNo\": \"SRST101\",\n        \"aciklama\": \"Ad soyad karar açıklaması\"\n      },\n      \"hedefAdSoyadListesi\": [\n        {\n          \"hedef\": \"9050517\",\n          \"hedefTip\": \"GSM\",\n          \"hedefAd\": \"Veli\",\n          \"hedefSoyad\": \"Ali\"\n        }\n      ]\n    }\n  ]\n}"}]}, "url": {"raw": "{{baseUrl}}{{makosApiPath}}/mahkemeKararTalep/hedefBilgiGuncelle", "host": ["{{baseUrl}}"], "path": ["{{makosApiPath}}", "mahkemeKararTalep", "he<PERSON>fBilgi<PERSON><PERSON><PERSON><PERSON>"]}}}, {"name": "mahkemeBilgiGuncelle", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{makos_basic_auth}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "ma<PERSON>kemeKara<PERSON><PERSON><PERSON>", "type": "file", "src": ""}, {"key": "mahkemeKararDetay", "type": "text", "value": "{\n  \"id\": \"55555555-5555-5555-5555-555555555555\",\n  \"evrakDetay\": {\n    \"evrakNo\": \"EV555666\",\n    \"evrakT<PERSON><PERSON>\": \"2024-05-01T10:00:00\",\n    \"evrak<PERSON><PERSON>mKodu\": \"KURUM05\",\n    \"evrak<PERSON><PERSON>\": \"GENEL_EVRAK\",\n    \"havaleBirimi\": \"BIRIM5\",\n    \"aciklama\": \"Mahkeme kodu açıklama\",\n    \"geldigiIlIlceKodu\": \"16\",\n    \"acilmi\": false,\n    \"evrakKonusu\": \"Mahke<PERSON> Kodu Konu\"\n  },\n  \"mahkemeKararBilgisi\": {\n    \"mahkemeKararTipi\": \"ONLEYICI_HAKIM_KARARI\",\n    \"mahkemeKararDetay\": {\n      \"mahkemeKodu\": \"06000102\",\n      \"mahkemeIlIlceKodu\": \"16\",\n      \"mahkemeKararNo\": \"MKNO202\",\n      \"sorusturmaNo\": \"SRST202\",\n      \"aciklama\": \"Mahkeme kodu karar açıklaması\"\n    }\n  },\n  \"mahkemeKoduGuncellemeDetayListesi\": [\n    {\n      \"mahkemeKararDetay\": {\n        \"mahkemeKodu\": \"06000102\",\n        \"mahkemeIlIlceKodu\": \"16\",\n        \"mahkemeKararNo\": \"MKNO202\",\n        \"sorusturmaNo\": \"SRST202\",\n        \"aciklama\": \"Mahkeme kodu karar açıklaması\"\n      },\n      \"yeniMahkemeKodu\": \"06000102\"\n    }\n  ]\n}"}]}, "url": {"raw": "{{baseUrl}}{{makosApiPath}}/mahkemeKararTalep/mahkemeBilgiGuncelle", "host": ["{{baseUrl}}"], "path": ["{{makosApiPath}}", "mahkemeKararTalep", "mahkemeBilgiGuncelle"]}}}, {"name": "sucTipiGuncelle", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "{{makos_basic_auth}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "ma<PERSON>kemeKara<PERSON><PERSON><PERSON>", "type": "file", "src": ""}, {"key": "mahkemeKararDetay", "type": "text", "value": "{\n  \"id\": \"66666666-6666-6666-6666-666666666666\",\n  \"kararTuru\": \"YEN<PERSON>\",\n  \"evrakDetay\": {\n    \"evrakNo\": \"EV777888\",\n    \"evrak<PERSON><PERSON><PERSON>\": \"2024-05-01T10:00:00\",\n    \"evrak<PERSON>urumKodu\": \"KURUM06\",\n    \"evrakT<PERSON>\": \"GENEL_EVRAK\",\n    \"havaleBirimi\": \"BIRIM6\",\n    \"aciklama\": \"Çanak açıklama\",\n    \"geldigiIlIlceKodu\": \"41\",\n    \"acilmi\": false,\n    \"evrakKonusu\": \"<PERSON><PERSON><PERSON>\"\n  },\n  \"mahkemeKararBilgisi\": {\n    \"mahkemeKararTipi\": \"ONLEYICI_HAKIM_KARARI\",\n    \"mahkemeKararDetay\": {\n      \"mahkemeKodu\": \"06000102\",\n      \"mahkemeIlIlceKodu\": \"41\",\n      \"mahkemeKararNo\": \"MKNO303\",\n      \"sorusturmaNo\": \"SRST303\",\n      \"aciklama\": \"Çanak karar açıklaması\"\n    }\n  },\n  \"canakGuncellemeKararDetayListesi\": [\n    {\n      \"mahkemeKararDetay\": {\n        \"mahkemeKodu\": \"06000102\",\n        \"mahkemeIlIlceKodu\": \"41\",\n        \"mahkemeKararNo\": \"MKNO303\",\n        \"sorusturmaNo\": \"SRST303\",\n        \"aciklama\": \"Çanak karar açıklaması\"\n      },\n      \"canakGuncellemeDetayList\": [\n        {\n          \"guncellemeTip\": \"EKLE\",\n          \"canakHedefDetay\": {\n            \"hedef\": {\n              \"hedef\": \"9050518\",\n              \"hedefTip\": \"GSM\",\n              \"hedefAd\": \"Mehmet\",\n              \"hedefSoyad\": \"Can\"\n            },\n            \"canakHedefNo\": \"CNK001\"\n          }\n        }\n      ]\n    }\n  ]\n}"}]}, "url": {"raw": "{{baseUrl}}{{makosApiPath}}/mahkemeKararTalep/sucTipiGuncelle", "host": ["{{baseUrl}}"], "path": ["{{makosApiPath}}", "mahkemeKararTalep", "sucTipiGuncelle"]}}}], "variable": []}