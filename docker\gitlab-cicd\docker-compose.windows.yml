services:
  web:
    image: gitlab/gitlab-ce:latest
    restart: always
    hostname: gitlab.local
    environment:
      GITLAB_OMNIBUS_CONFIG: |
        external_url 'http://web'
        nginx['client_max_body_size'] = '4g'
        gitlab_rails['artifacts_max_size'] = 512 * 1024 * 1024
        gitlab_workhorse['max_artifacts_size'] = 512 * 1024 * 1024
        nginx['listen_port'] = 80
        nginx['listen_https'] = false
        gitlab_rails['gitlab_shell_ssh_port'] = 2222
        registry_external_url 'http://localhost:5050'
        # Enable automatic database migrations
        gitlab_rails['auto_migrate'] = true
        # Configure email settings
        gitlab_rails['smtp_enable'] = false
        # Configure clone URL for runners
        gitlab_rails['*********************'] = 'http://web'
        # Configure external URL for clone operations
        gitlab_rails['gitlab_shell_git_timeout'] = 800
    ports:
      - '8929:80'  # HTTP port changed from 80 to 8929
      - '8443:443' # HTTPS port changed from 443 to 8443
      - '2222:22'  # SSH
      - '5050:5050' # Container Registry
    volumes:
      - ${GITLAB_HOME:-C:/gitlab-data}/config:/etc/gitlab
      - ${GITLAB_HOME:-C:/gitlab-data}/logs:/var/log/gitlab
      - ${GITLAB_HOME:-C:/gitlab-data}/data:/var/opt/gitlab
    shm_size: '256m'
    networks:
      - gitlab_network

  runner:
    image: gitlab/gitlab-runner:latest
    restart: always
    depends_on:
      - web
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ${GITLAB_HOME:-C:/gitlab-data}/runner-config:/etc/gitlab-runner
    networks:
      - gitlab_network

networks:
  gitlab_network:
    driver: bridge