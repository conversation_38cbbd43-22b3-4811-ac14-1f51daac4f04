package iym.common.util;

import org.springframework.http.HttpStatus;
import org.springframework.web.server.ResponseStatusException;

public interface ExceptionUtils {

    ResponseStatusException USER_NOT_FOUND =
            new ResponseStatusException(HttpStatus.NOT_FOUND, "Kullanici Bulunamadi.");

    ResponseStatusException UNAUTHORIZED =
            new ResponseStatusException(HttpStatus.UNAUTHORIZED, "Kullanicinin bu işlem için yetkisi yok.");

    ResponseStatusException RECORD_NOT_FOUND =
            new ResponseStatusException(HttpStatus.NOT_FOUND, "Record not found.");

    ResponseStatusException INVALID_CURRENT_PASSWORD =
            new ResponseStatusException(HttpStatus.BAD_REQUEST, "Mevcut parola hatalı");

    ResponseStatusException PASSWORD_MISMATCH =
            new ResponseStatusException(HttpStatus.BAD_REQUEST, "Yeni parolalar aynı değil");

    static ResponseStatusException newBadRequest(String reason ) {
        return new ResponseStatusException(HttpStatus.BAD_REQUEST, reason);
    }
}
