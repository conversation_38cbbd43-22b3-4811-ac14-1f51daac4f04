package iym.makos.domain.mktaleprequest.dbhandler;


import iym.common.enums.HedefTip;
import iym.common.model.api.Hedef;
import iym.common.model.entity.iym.mk.Hedefler;
import iym.common.model.entity.iym.mk.MahkemeKarar;
import iym.common.model.entity.iym.talep.DetayMahkemeKararTalep;
import iym.common.model.entity.iym.talep.HedeflerDetayTalep;
import iym.common.model.entity.iym.talep.HedeflerTalep;
import iym.common.model.entity.iym.talep.MahkemeKararTalep;
import iym.common.service.db.DbHedeflerTalepService;
import iym.common.service.db.mk.DbHedeflerService;
import iym.common.service.db.mk.DbMahkemeKararService;
import iym.common.service.db.mktalep.DbDetayMahkemeKararTalepService;
import iym.common.service.db.mktalep.DbHedeflerDetayTalepService;
import iym.common.service.db.mktalep.DbMahkemeKararTalepService;
import iym.makos.domain.mktalep.requestprocessor.dbhandler.IDUzatmaKarariDBSaveHandler;
import iym.makos.domain.mktalep.requestprocessor.dbhandler.MahkemeKararRequestCommonDbSaver;
import iym.makos.errors.MakosResponseException;
import iym.makos.mapper.KararRequestMapper;
import iym.makos.model.api.IDHedefDetay;
import iym.makos.model.api.MahkemeKararDetay;
import iym.makos.model.dto.mktalep.request.id.IDUzatmaKarariRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.*;


@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.STRICT_STUBS)
@DisplayName("IDUzatmaKarariDBSaveHandlerTest Unit Tests")
public class IDUzatmaKarariDBSaveHandlerTest {

    @InjectMocks
    private IDUzatmaKarariDBSaveHandler handler;

    @Mock
    private DbMahkemeKararService dbMahkemeKararService;
    @Mock
    private DbMahkemeKararTalepService dbMahkemeKararTalepService;
    @Mock
    private DbDetayMahkemeKararTalepService dbDetayMahkemeKararTalepService;
    @Mock
    private DbHedeflerTalepService dbHedeflerTalepService;
    @Mock
    private DbHedeflerDetayTalepService dbHedeflerDetayTalepService;
    @Mock
    private DbHedeflerService dbHedeflerService;
    @Mock
    private KararRequestMapper kararRequestMapper;
    @Mock
    private MahkemeKararRequestCommonDbSaver mahkemeKararRequestCommonDbSaver;

    private IDUzatmaKarariRequest request;
    private LocalDateTime now;
    private Long kullaniciId;

    @BeforeEach
    void setUp() {
        now = LocalDateTime.now();
        kullaniciId = 123L;

        // Manually inject the mocked dependency since the base class uses setter injection
        handler.setMahkemeKararRequestCommonDbSaver(mahkemeKararRequestCommonDbSaver);

        // Create a mock request object for proper testing
        request = mock(IDUzatmaKarariRequest.class);
    }

    @Test
    void kaydet_ShouldReturnTalepId_WhenFlowIsSuccessful() {
        // GIVEN
        Long talepId = 111L;
        Long hedefId = 222L;
        String hedefNo = "HDF123";
        Long evrakId = 456L;

        when(mahkemeKararRequestCommonDbSaver.handleDbSave(any(), any(), any())).thenReturn(talepId);

        // Mock MahkemeKararTalep to provide evrakId
        MahkemeKararTalep mahkemeKararTalep = new MahkemeKararTalep();
        mahkemeKararTalep.setEvrakId(evrakId);
        when(dbMahkemeKararTalepService.findById(talepId)).thenReturn(Optional.of(mahkemeKararTalep));

        // Sahte hedef detay listesi
        IDHedefDetay hedefDetay = mock(IDHedefDetay.class);
        Hedef hedef = mock(Hedef.class);
        HedefTip hedefTip = HedefTip.GSM; // örnek enum

        // HedefWithAdSoyad mock'u oluştur
        iym.common.model.api.HedefWithAdSoyad hedefWithAdSoyad = mock(iym.common.model.api.HedefWithAdSoyad.class);

        when(hedef.getHedefNo()).thenReturn(hedefNo);
        when(hedef.getHedefTip()).thenReturn(hedefTip);
        when(hedefWithAdSoyad.getHedef()).thenReturn(hedef);
        when(hedefDetay.getHedefNoAdSoyad()).thenReturn(hedefWithAdSoyad);

        MahkemeKararDetay mahkemeKararDetay = mock(MahkemeKararDetay.class);
        when(hedefDetay.getIlgiliMahkemeKararDetayi()).thenReturn(mahkemeKararDetay);

        // MahkemeKarar bulundu
        MahkemeKarar mahkemeKarar = new MahkemeKarar();
        mahkemeKarar.setId(999L);
        when(dbMahkemeKararService.findBy(any(), any(), any(), any())).thenReturn(Optional.of(mahkemeKarar));

        // Hedef bulundu
        Hedefler hedefler = new Hedefler();
        hedefler.setId(hedefId);
        when(dbHedeflerService.findByMahkemeKararIdAndHedefNoAndHedefTipi(anyLong(), anyString(), anyInt()))
                .thenReturn(Optional.of(hedefler));

        // Mapper dönüşleri
        DetayMahkemeKararTalep detayTalep = new DetayMahkemeKararTalep();
        detayTalep.setId(333L);
        when(kararRequestMapper.toDMahkemeKararTalepDetay(any(), anyLong(), any(), anyLong(), any())).thenReturn(detayTalep);
        when(dbDetayMahkemeKararTalepService.save(any())).thenReturn(detayTalep);

        HedeflerDetayTalep detayHedef = new HedeflerDetayTalep();
        detayHedef.setId(444L);
        when(kararRequestMapper.toHedeflerDetayTalep(any(), anyLong(), anyLong(), anyLong(), any())).thenReturn(detayHedef);
        when(dbHedeflerDetayTalepService.save(any())).thenReturn(detayHedef);

        HedeflerTalep hedeflerTalep = new HedeflerTalep();
        hedeflerTalep.setId(555L);
        hedeflerTalep.setHedefNo(hedefNo);
        when(kararRequestMapper.toHedeflerTalep(any(), anyLong(), anyLong(), any())).thenReturn(hedeflerTalep);
        when(dbHedeflerTalepService.save(any())).thenReturn(hedeflerTalep);

        when(request.getHedefDetayListesi()).thenReturn(List.of(hedefDetay));

        // WHEN
        Long result = handler.kaydet(request, now, kullaniciId);

        // THEN
        assertThat(result).isEqualTo(talepId);
        verify(mahkemeKararRequestCommonDbSaver).handleDbSave(request, now, kullaniciId);
        verify(dbMahkemeKararService).findBy(any(), any(), any(), any());
        verify(dbHedeflerService).findByMahkemeKararIdAndHedefNoAndHedefTipi(anyLong(), eq(hedefNo), anyInt());
        verify(dbDetayMahkemeKararTalepService).save(any());
        verify(dbHedeflerDetayTalepService).save(any());
        verify(dbHedeflerTalepService).save(any());
    }

    @Test
    void kaydet_ShouldThrowException_WhenTalepIdIsNull() {
        when(mahkemeKararRequestCommonDbSaver.handleDbSave(any(), any(), any())).thenReturn(null);

        assertThatThrownBy(() -> handler.kaydet(request, now, kullaniciId))
                .isInstanceOf(MakosResponseException.class)
                .extracting("formattedMessage")
                .isEqualTo("Mahkeme karar kaydedilemedi");
    }

    @Test
    void kaydet_ShouldThrowException_WhenMahkemeKararNotFound() {
        when(mahkemeKararRequestCommonDbSaver.handleDbSave(any(), any(), any())).thenReturn(123L);

        // Mock MahkemeKararTalep to provide evrakId
        MahkemeKararTalep mahkemeKararTalep = new MahkemeKararTalep();
        mahkemeKararTalep.setEvrakId(456L);
        when(dbMahkemeKararTalepService.findById(123L)).thenReturn(Optional.of(mahkemeKararTalep));

        IDHedefDetay hedefDetay = mock(IDHedefDetay.class);
        MahkemeKararDetay mahkemeKararDetay = mock(MahkemeKararDetay.class);
        when(hedefDetay.getIlgiliMahkemeKararDetayi()).thenReturn(mahkemeKararDetay);

        // Setup hedef mock properly to avoid NullPointerException
        Hedef hedef = mock(Hedef.class);
        when(hedef.getHedefNo()).thenReturn("12345");
        when(hedef.getHedefTip()).thenReturn(HedefTip.GSM);

        iym.common.model.api.HedefWithAdSoyad hedefWithAdSoyad = mock(iym.common.model.api.HedefWithAdSoyad.class);
        when(hedefWithAdSoyad.getHedef()).thenReturn(hedef);
        when(hedefDetay.getHedefNoAdSoyad()).thenReturn(hedefWithAdSoyad);

        // Setup request with hedef detay list
        when(request.getHedefDetayListesi()).thenReturn(List.of(hedefDetay));
        when(dbMahkemeKararService.findBy(any(), any(), any(), any())).thenReturn(Optional.empty());

        assertThatThrownBy(() -> handler.kaydet(request, now, kullaniciId))
                .isInstanceOf(MakosResponseException.class)
                .extracting("formattedMessage")
                .asString()
                .startsWith("Mahkeme Karar Bulunamadı:");
    }

    @Test
    void kaydet_ShouldThrowException_WhenHedefNotFound() {
        when(mahkemeKararRequestCommonDbSaver.handleDbSave(any(), any(), any())).thenReturn(123L);

        // Mock MahkemeKararTalep to provide evrakId
        MahkemeKararTalep mahkemeKararTalep = new MahkemeKararTalep();
        mahkemeKararTalep.setEvrakId(456L);
        when(dbMahkemeKararTalepService.findById(123L)).thenReturn(Optional.of(mahkemeKararTalep));

        IDHedefDetay hedefDetay = mock(IDHedefDetay.class);
        MahkemeKararDetay mahkemeKararDetay = mock(MahkemeKararDetay.class);
        when(hedefDetay.getIlgiliMahkemeKararDetayi()).thenReturn(mahkemeKararDetay);

        MahkemeKarar mahkemeKarar = new MahkemeKarar();
        mahkemeKarar.setId(777L);
        when(dbMahkemeKararService.findBy(any(), any(), any(), any())).thenReturn(Optional.of(mahkemeKarar));

        // Setup hedef mock properly
        Hedef hedef = mock(Hedef.class);
        when(hedef.getHedefNo()).thenReturn("12345");
        when(hedef.getHedefTip()).thenReturn(HedefTip.GSM);

        iym.common.model.api.HedefWithAdSoyad hedefWithAdSoyad = mock(iym.common.model.api.HedefWithAdSoyad.class);
        when(hedefWithAdSoyad.getHedef()).thenReturn(hedef);
        when(hedefDetay.getHedefNoAdSoyad()).thenReturn(hedefWithAdSoyad);

        // Setup request with hedef detay list
        when(request.getHedefDetayListesi()).thenReturn(List.of(hedefDetay));
        when(dbHedeflerService.findByMahkemeKararIdAndHedefNoAndHedefTipi(anyLong(), anyString(), anyInt()))
                .thenReturn(Optional.empty());

        assertThatThrownBy(() -> handler.kaydet(request, now, kullaniciId))
                .isInstanceOf(MakosResponseException.class)
                .extracting("formattedMessage")
                .asString()
                .startsWith("Hedef bulunmadı.");
    }

    @Test
    void kaydet_ShouldSetEvrakIdCorrectly_WhenProcessingHedefDetay() {
        // GIVEN
        Long mahkemeKararTalepId = 123L;
        Long expectedEvrakId = 456L;

        when(mahkemeKararRequestCommonDbSaver.handleDbSave(any(), any(), any())).thenReturn(mahkemeKararTalepId);

        // Mock MahkemeKararTalep with evrakId
        MahkemeKararTalep mahkemeKararTalep = new MahkemeKararTalep();
        mahkemeKararTalep.setEvrakId(expectedEvrakId);
        when(dbMahkemeKararTalepService.findById(mahkemeKararTalepId)).thenReturn(Optional.of(mahkemeKararTalep));

        IDHedefDetay hedefDetay = mock(IDHedefDetay.class);
        MahkemeKararDetay mahkemeKararDetay = mock(MahkemeKararDetay.class);
        when(hedefDetay.getIlgiliMahkemeKararDetayi()).thenReturn(mahkemeKararDetay);

        MahkemeKarar mahkemeKarar = new MahkemeKarar();
        mahkemeKarar.setId(777L);
        when(dbMahkemeKararService.findBy(any(), any(), any(), any())).thenReturn(Optional.of(mahkemeKarar));

        Hedefler hedefler = new Hedefler();
        hedefler.setId(888L);
        when(dbHedeflerService.findByMahkemeKararIdAndHedefNoAndHedefTipi(anyLong(), anyString(), anyInt()))
                .thenReturn(Optional.of(hedefler));

        // Setup hedef mock properly
        Hedef hedef = mock(Hedef.class);
        when(hedef.getHedefNo()).thenReturn("12345");
        when(hedef.getHedefTip()).thenReturn(HedefTip.GSM);

        iym.common.model.api.HedefWithAdSoyad hedefWithAdSoyad = mock(iym.common.model.api.HedefWithAdSoyad.class);
        when(hedefWithAdSoyad.getHedef()).thenReturn(hedef);
        when(hedefDetay.getHedefNoAdSoyad()).thenReturn(hedefWithAdSoyad);

        // Setup request with hedef detay list
        when(request.getHedefDetayListesi()).thenReturn(List.of(hedefDetay));

        // Mock the mapper to return a DetayMahkemeKararTalep
        DetayMahkemeKararTalep detayMahkemeKararTalep = new DetayMahkemeKararTalep();
        when(kararRequestMapper.toDMahkemeKararTalepDetay(any(MahkemeKarar.class), eq(mahkemeKararTalepId), eq(expectedEvrakId), eq(kullaniciId), any(LocalDateTime.class)))
                .thenReturn(detayMahkemeKararTalep);

        when(dbDetayMahkemeKararTalepService.save(any(DetayMahkemeKararTalep.class)))
                .thenReturn(detayMahkemeKararTalep);

        // Mock other required services
        when(kararRequestMapper.toHedeflerDetayTalep(any(), any(), any(), any(), any()))
                .thenReturn(new HedeflerDetayTalep());
        when(dbHedeflerDetayTalepService.save(any())).thenReturn(new HedeflerDetayTalep());
        when(kararRequestMapper.toHedeflerTalep(any(), any(), any(), any()))
                .thenReturn(new HedeflerTalep());
        when(dbHedeflerTalepService.save(any())).thenReturn(new HedeflerTalep());

        // WHEN
        Long result = handler.kaydet(request, now, kullaniciId);

        // THEN
        assertThat(result).isEqualTo(mahkemeKararTalepId);

        // Verify that toDMahkemeKararTalepDetay was called with the correct evrakId
        verify(kararRequestMapper).toDMahkemeKararTalepDetay(
                any(MahkemeKarar.class),
                eq(mahkemeKararTalepId),
                eq(expectedEvrakId),  // This should not be null anymore
                eq(kullaniciId),
                any(LocalDateTime.class)
        );
    }
}
