package iym.db.jpa.dao.mk;

import iym.common.model.entity.iym.mk.MahkemeKarar;
import iym.db.jpa.dao.sorgu.MahkemeKararRepoDynamicQueries;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for MahkemeKarar entity
 */
@Repository
public interface MahkemeKararRepo extends JpaRepository<MahkemeKarar, Long>, MahkemeKararRepoDynamicQueries {
    String findBySqlStr = """
        SELECT mk.* FROM iym.MAHKEME_KARAR mk
               INNER JOIN EVRAK_KAYIT ek ON ek.ID = mk.EVRAK_ID
               WHERE
               	mk.MAHKEME_ILI = :mahkemeIlIlceKodu
               	AND mk.MAHKEME_KARAR_NO = :mahkemeKararNo
               	AND mk.SORUSTURMA_NO = :sorusturmaNo
               	AND mk.MAHKEME_KODU = :mahkemeKodu
     """;


    List<MahkemeKarar> findByEvrakId(Long evrakId);


    @Query(value =findBySqlStr
            , nativeQuery = true)
    Optional<MahkemeKarar> findBy(
            @Param("mahkemeIlIlceKodu") String mahkemeIlIlceKodu,
            @Param("mahkemeKodu") String mahkemeKodu,
            @Param("mahkemeKararNo") String mahkemeKararNo,
            @Param("sorusturmaNo") String sorusturmaNo
    );



}
