-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create sequence for KULLANICI_KURUM if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = 'KULLANICI_KURUM_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.KULLANICI_KURUM_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

-- Create KULLANICI_KURUM table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'KULLANICI_KURUM';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.KULLANICI_KURUM (
      KULLANICI_ID NUMBER NOT NULL,
      <PERSON>URUM_KOD VARCHAR2(20 BYTE) NOT NULL,
      ID NUMBER,
      CONSTRAINT KURUM_KULLANICI_IDX PRIMARY KEY (KULLANICI_ID, KURUM_KOD) ENABLE
    )';
  END IF;
END;
/

-- Insert sample data if table is empty
DECLARE
  row_count NUMBER;
BEGIN
  SELECT COUNT(*) INTO row_count FROM iym.KULLANICI_KURUM;
  IF row_count = 0 THEN
    -- Make sure we have users in KULLANICILAR table
    DECLARE
      user_count NUMBER;
    BEGIN
      SELECT COUNT(*) INTO user_count FROM iym.KULLANICILAR;
      IF user_count > 0 THEN
        -- Get the ID of the admin user
        DECLARE
          admin_id NUMBER;
        BEGIN
          SELECT ID INTO admin_id FROM iym.KULLANICILAR WHERE KULLANICI_ADI = 'admin';
          
          -- Sample data 1 - Admin user assigned to Adalet Bakanlığı
          INSERT INTO iym.KULLANICI_KURUM (KULLANICI_ID, KURUM_KOD, ID)
          VALUES (admin_id, '02', iym.KULLANICI_KURUM_SEQ.NEXTVAL);
          
          -- Get the ID of user 'ahmet'
          DECLARE
            ahmet_id NUMBER;
          BEGIN
            SELECT ID INTO ahmet_id FROM iym.KULLANICILAR WHERE KULLANICI_ADI = 'ahmet';
            
            -- Sample data 2 - Ahmet assigned to İçişleri Bakanlığı
            INSERT INTO iym.KULLANICI_KURUM (KULLANICI_ID, KURUM_KOD, ID)
            VALUES (ahmet_id, '03', iym.KULLANICI_KURUM_SEQ.NEXTVAL);
            
            -- Get the ID of user 'ayse'
            DECLARE
              ayse_id NUMBER;
            BEGIN
              SELECT ID INTO ayse_id FROM iym.KULLANICILAR WHERE KULLANICI_ADI = 'ayse';
              
              -- Sample data 3 - Ayşe assigned to Milli Eğitim Bakanlığı
              INSERT INTO iym.KULLANICI_KURUM (KULLANICI_ID, KURUM_KOD, ID)
              VALUES (ayse_id, '04', iym.KULLANICI_KURUM_SEQ.NEXTVAL);
            EXCEPTION
              WHEN NO_DATA_FOUND THEN
                NULL; -- User 'ayse' not found, skip this insertion
            END;
          EXCEPTION
            WHEN NO_DATA_FOUND THEN
              NULL; -- User 'ahmet' not found, skip this insertion
          END;
        EXCEPTION
          WHEN NO_DATA_FOUND THEN
            NULL; -- Admin user not found, skip all insertions
        END;
      END IF;
    END;
  END IF;
END;
/

COMMIT;
