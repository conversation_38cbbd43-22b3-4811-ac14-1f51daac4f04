services:
  web:
    image: gitlab/gitlab-ce:latest
    restart: always
    hostname: gitlab.local
    environment:
      GITLAB_OMNIBUS_CONFIG: |
        external_url 'http://web'
        nginx['client_max_body_size'] = '4g'
        gitlab_rails['artifacts_max_size'] = 512 * 1024 * 1024
        gitlab_workhorse['max_artifacts_size'] = 512 * 1024 * 1024
        nginx['listen_port'] = 80
        nginx['listen_https'] = false
        gitlab_rails['gitlab_shell_ssh_port'] = 2222
        registry_external_url 'http://localhost:5050'
        # Enable automatic database migrations
        gitlab_rails['auto_migrate'] = true
        # Configure email settings
        gitlab_rails['smtp_enable'] = false
        # Configure clone URL for runners
        gitlab_rails['*********************'] = 'http://web'
        # Configure external URL for clone operations
        gitlab_rails['gitlab_shell_git_timeout'] = 800
    ports:
      - '8929:80'    # HTTP port
      - '8443:443'   # HTTPS port
      - '2222:22'    # SSH
      - '5050:5050'  # Container Registry
    volumes:
      - gitlab_config:/etc/gitlab
      - gitlab_logs:/var/log/gitlab
      - gitlab_data:/var/opt/gitlab
    shm_size: '4g'
    networks:
      gitlab_network:
        aliases:
          - web
          - gitlab
          - gitlab.local

  runner:
    image: gitlab/gitlab-runner:latest
    restart: always
    depends_on:
      - web
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - gitlab_runner_config:/etc/gitlab-runner
    networks:
      gitlab_network:
        aliases:
          - gitlab-runner

networks:
  gitlab_network:
    driver: bridge

volumes:
  gitlab_config:
    driver: local
  gitlab_logs:
    driver: local
  gitlab_data:
    driver: local
  gitlab_runner_config:
    driver: local