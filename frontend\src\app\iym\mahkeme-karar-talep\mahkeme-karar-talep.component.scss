:host {
  display: block;
  padding: 1rem;
}

.form-container {
  max-width: 1200px;
  margin: 0 auto;
}

.header-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.section-card {
  margin-bottom: 1.5rem;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.required-field::after {
  content: ' *';
  color: red;
}

.error-message {
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.invalid-input {
  border-color: #ef4444;
}

.tab-content {
  padding: 1rem 0;
}

.file-upload-section {
  margin-top: 2rem;
}

.submit-section {
  margin-top: 2rem;
  display: flex;
  justify-content: flex-end;
}

.test-data-buttons {
  position: fixed;
  bottom: 1rem;
  right: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  z-index: 1000;
}

.test-button {
  min-width: 200px;
}

@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }
  
  .header-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .test-data-buttons {
    position: static;
    margin-top: 1rem;
  }
}

:host ::ng-deep {
  .p-card {
    margin-bottom: 1rem;
  }
  
  .p-card-header {
    font-weight: 600;
    color: #374151;
  }
  
  .p-tabview-nav {
    background: transparent;
    border-bottom: 2px solid #e5e7eb;
  }
  
  .p-tabview-nav-link {
    color: #6b7280;
    font-weight: 500;
    
    &:hover {
      color: #374151;
    }
  }
  
  .p-tabview-nav-link.p-highlight {
    color: #3b82f6;
    border-bottom-color: #3b82f6;
  }
  
  .p-button {
    &.p-button-sm {
      padding: 0.5rem 1rem;
      font-size: 0.875rem;
    }
  }
}