# MAKOS Domain Sınıfları Unit Test Rehberi

## Genel Bakış

MAKOS domain katmanı mahkeme kararı işlemlerini **Strategy Pattern**, **Factory Pattern** ve **Template Method Pattern** kullanarak yönetir. Bu rehber domain sınıflarının unit testlerinin nasıl yazılacağını açıklar.

## <PERSON><PERSON><PERSON>

### 1. <PERSON><PERSON> (Strategy Pattern)
- **Base**: `MakosRequestProcessorBase<T, R>`
- **Interface**: `IMakosRequestProcessor<T, R>`
- **Factory**: `ProcessorFactory`
- **Concrete**: `GenelKararRequestProcessor`, `IDYeniKararRequestProcessor`, vb.

### 2. <PERSON><PERSON><PERSON> (Template Method Pattern)
- **Base**: `MahkemeKararRequestValidatorBase<T>`
- **Interface**: `IMahkemeKararRequestValidator<T>`
- **Factory**: `MahkemeKararRequestValidatorFactory`
- **Common**: `MahkemeKararRequestCommonValidator`

### 3. DB Handler Katmanı (Strategy + Template Method)
- **Base**: `MahkemeKararRequestDbSaveHandlerBase<T>`
- **Interface**: `MahkemeKararDBSaveHandler<T>`
- **Factory**: `MahkemeKararRequestDBSaveHandlerFactory`
- **Common**: `MahkemeKararRequestCommonDbSaver`

## Test Stratejisi

### Test Türleri

**Unit Tests** (`@ExtendWith(MockitoExtension.class)`)
- Profile: `test`
- Database: Mock/Disabled
- Amaç: Sınıfları izole test etmek

**Integration Tests** (`@SpringBootTest`)
- Profile: `integration-test`
- Database: H2 in-memory
- Amaç: Sınıflar arası entegrasyonu test etmek

### Test Konfigürasyonu

```java
@ExtendWith(MockitoExtension.class)
class MyServiceTest {
    @Mock
    private Dependency dependency;
    
    @InjectMocks
    private MyService service;
    
    @Test
    void shouldDoSomething_whenConditionMet() {
        // Given - When - Then
    }
}
```

## Test Örnekleri

### 1. Factory Sınıfları

```java
@ExtendWith(MockitoExtension.class)
class ProcessorFactoryTest {
    
    @Mock
    private IMakosRequestProcessor<IDYeniKararRequest, IDYeniKararResponse> processor;
    
    @InjectMocks
    private ProcessorFactory factory;
    
    @Test
    void shouldReturnCorrectProcessor_whenRequestTypeMatches() {
        // Given
        when(processor.getRelatedRequestType()).thenReturn(IDYeniKararRequest.class);
        when(processor.getRelatedResponseType()).thenReturn(IDYeniKararResponse.class);
        
        // When
        IMakosRequestProcessor<?, ?> result = factory.getProcessor(
            IDYeniKararRequest.class, IDYeniKararResponse.class);
        
        // Then
        assertThat(result).isEqualTo(processor);
    }
}
```

### 2. Validator Sınıfları

```java
@ExtendWith(MockitoExtension.class)
class IDYeniKararValidatorTest {
    
    @Mock
    private MahkemeKararRequestCommonValidator commonValidator;
    
    @InjectMocks
    private IDYeniKararValidator validator;
    
    @Test
    void shouldFailValidation_whenOhalDateViolated() {
        // Given
        IDYeniKararRequest request = createValidRequest();
        request.getMahkemeKararBilgisi().setMahkemeKararTipi(MahkemeKararTip.ADLI_KHK_YAZILI_EMIR);
        
        when(commonValidator.validate(request)).thenReturn(ValidationResult.valid());
        
        // When
        ValidationResult result = validator.validate(request);
        
        // Then
        assertThat(result.isValid()).isFalse();
        assertThat(result.getReasons()).contains("19.07.2018 01:00 tarihinden sonra Adli KHK Yazılı Emir Gönderilemez");
    }
}
```

### 3. Processor Sınıfları

```java
@ExtendWith(MockitoExtension.class)
class GenelKararRequestProcessorTest {
    
    @Mock
    private IMahkemeKararRequestValidator<GenelEvrakRequest> validator;
    
    @Mock
    private MahkemeKararDBSaveHandler<GenelEvrakRequest> saver;
    
    @InjectMocks
    private GenelKararRequestProcessor processor;
    
    @Test
    void shouldReturnSuccessResponse_whenValidationPasses() {
        // Given
        GenelEvrakRequest request = createValidRequest();
        UserDetailsImpl user = createTestUser();
        
        when(validator.validate(request)).thenReturn(ValidationResult.valid());
        when(saver.kaydet(any(), any(), any())).thenReturn(123L);
        
        // When
        GenelKararResponse response = processor.process(request, user);
        
        // Then
        assertThat(response.getResponse().getResponseCode()).isEqualTo(MakosResponseCode.SUCCESS);
        assertThat(response.getEvrakId()).isEqualTo(123L);
    }
}
```

## Test Data Hazırlama

### Builder Pattern Kullanımı

```java
@BeforeEach
void setUp() {
    testRequest = IDYeniKararRequest.builder()
            .kararTuru(KararTuru.ILETISIMIN_DENETLENMESI_YENI_KARAR)
            .evrakDetay(createEvrakDetay())
            .mahkemeKararBilgisi(createMahkemeKararBilgisi())
            .hedefDetayListesi(createHedefDetayListesi())
            .build();
}

private EvrakDetay createEvrakDetay() {
    return EvrakDetay.builder()
            .evrakNo("2024/001")
            .evrakKurumKodu("BTK")
            .geldigiIlIlceKodu("0600")
            .build();
}
```

## Test Edilmesi Gereken Ana Alanlar

### 1. Factory Sınıfları
- Doğru implementation'ı döndürür mü?
- Null/invalid input handling
- Type safety

### 2. Validator Sınıfları
- Template method sırası doğru mu?
- Business rule validations
- Error message accuracy
- Edge cases

### 3. Processor Sınıfları
- Happy path scenarios
- Validation failure handling
- Exception handling
- Response mapping

### 4. DB Handler Sınıfları
- Common operations delegation
- Specific business logic
- Transaction handling
- Error scenarios

## Best Practices

1. **Given-When-Then** pattern kullanın
2. **Test method naming**: `should[ExpectedBehavior]_when[StateUnderTest]`
3. **Mock minimal**: Sadece gerekli dependency'leri mock'layın
4. **Test isolation**: Her test bağımsız olmalı
5. **Edge cases**: Boundary conditions test edin
6. **Error scenarios**: Exception handling test edin

## Çalıştırma

```bash
# Unit testler
mvn test -Dspring.profiles.active=test

# Integration testler  
mvn test -Dspring.profiles.active=integration-test

# Specific test class
mvn test -Dtest=ProcessorFactoryTest
```
