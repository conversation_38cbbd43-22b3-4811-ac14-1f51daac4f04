package iym.spring.db.loader;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.support.PropertySourcesPlaceholderConfigurer;

@Slf4j
public class DbConfigLoader {

    public DbConfigLoader(){
        log.info("DbConfigLoader initializing");
    }

    @Bean
    public static PropertySourcesPlaceholderConfigurer propertyConfigurer() {
        return new PropertySourcesPlaceholderConfigurer();
    }

    @Getter
    @Value("${LOCAL_DB_DRIVER_CLASS}")
    private String localDbDriverClass;

    @Getter
    @Value("${LOCAL_DB_ADDRESS}")
    private String localDbAddress;

    @Getter
    @Value("${LOCAL_DB_USERNAME}")
    private String localDbUsername;

    @Getter
    @Value("${LOCAL_DB_PASSWORD}")
    private String localDbPassword;

    @Value("${LOCAL_DB_INITIAL_POOL_SIZE}")
    private String localDbInitialPoolSize;

    @Value("${LOCAL_DB_MIN_POOL_SIZE}")
    private String localDbMinPoolSize;

    @Value("${LOCAL_DB_MAX_POOL_SIZE}")
    private String localDbMaxPoolSize;

    @Value("${LOCAL_DB_MAX_IDLE_TIME}")
    private String localDbMaxIdleTime;

    @Value("${LOCAL_DB_IDLE_CONNECTION_TEST_PERIOD}")
    private String localDbIdleConnectionTestPeriod;

    @Getter
    @Value("${LOCAL_DB_PREFERRED_TEST_QUERY}")
    private String localDbPreferedTestQuery;

    @Value("${LOCAL_DB_NUM_HELPER_THREADS}")
    private String localDbNumHelperThreads;

    @Value("${LOCAL_DB_MAX_STATEMENTS}")
    private String localDbMaxStatements;

    @Value("${LOCAL_DB_MAX_STATEMENTS_PER_CONNECTION}")
    private String localDbMaxStatementsPerConnection;

    public int getLocalDbInitialPoolSize() {
        return Integer.parseInt(localDbInitialPoolSize);
    }

    public int getLocalDbMinPoolSize() {
        return Integer.parseInt(localDbMinPoolSize);
    }

    public int getLocalDbMaxPoolSize() {
        return Integer.parseInt(localDbMaxPoolSize);
    }

    public int getLocalDbMaxIdleTime() {
        return Integer.parseInt(localDbMaxIdleTime);
    }

    public int getLocalDbIdleConnectionTestPeriod() {
        return Integer.parseInt(localDbIdleConnectionTestPeriod);
    }

    public int getLocalDbNumHelperThreads() {
        return Integer.parseInt(localDbNumHelperThreads);
    }

    public int getLocalDbMaxStatements() {
        return Integer.parseInt(localDbMaxStatements);
    }

    public int getLocalDbMaxStatementsPerConnection() {
        return Integer.parseInt(localDbMaxStatementsPerConnection);
    }

}