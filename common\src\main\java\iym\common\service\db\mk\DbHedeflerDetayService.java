package iym.common.service.db.mk;

import iym.common.model.entity.iym.mk.HedeflerDetay;
import iym.common.service.db.GenericDbService;

import java.util.List;
import java.util.Optional;


public interface DbHedeflerDetayService extends GenericDbService<HedeflerDetay, Long> {

    List<HedeflerDetay> findByMahkemeKararId(Long mahkemeKararId);

    Optional<HedeflerDetay> findByDetayMahkemeKararId(Long detayMahkemeKararId);

    Optional<HedeflerDetay> findHedeflerDetayIslem(Long mahkemeKararId, String hedefNo, Integer hedefTipi);


}
