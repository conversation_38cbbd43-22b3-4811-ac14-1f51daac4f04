package iym.makos.architecture;

import com.tngtech.archunit.core.domain.JavaClasses;
import com.tngtech.archunit.core.importer.ClassFileImporter;
import com.tngtech.archunit.lang.ArchRule;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;

import static com.tngtech.archunit.lang.syntax.ArchRuleDefinition.classes;

/**
 * Architecture test to enforce integration test naming conventions.
 * 
 * This test ensures that:
 * - Classes annotated with @SpringBootTest should end with "IntegrationTest"
 * - Classes annotated with @DataJpaTest should end with "IntegrationTest" 
 * - Classes annotated with @WebMvcTest with full Spring context should end with "IntegrationTest"
 * 
 * This helps maintain consistent naming conventions and makes it easier to:
 * - Identify integration tests vs unit tests
 * - Run specific test categories
 * - Apply different test configurations
 * 
 * <AUTHOR> Team
 */
class IntegrationTestNamingConventionTest {

    private static final String BASE_PACKAGE = "iym";

    @Test
    void springBootTestClassesShouldEndWithIntegrationTest() {
        JavaClasses importedClasses = new ClassFileImporter()
                .importPackages(BASE_PACKAGE);

        ArchRule rule = classes()
                .that().areAnnotatedWith(SpringBootTest.class)
                .should().haveSimpleNameEndingWith("IntegrationTest")
                .because("Classes annotated with @SpringBootTest are integration tests and should follow naming convention");

        rule.check(importedClasses);
    }

    @Test
    void dataJpaTestClassesShouldEndWithIntegrationTest() {
        JavaClasses importedClasses = new ClassFileImporter()
                .importPackages(BASE_PACKAGE);

        ArchRule rule = classes()
                .that().areAnnotatedWith(DataJpaTest.class)
                .should().haveSimpleNameEndingWith("IntegrationTest")
                .because("Classes annotated with @DataJpaTest are integration tests and should follow naming convention");

        rule.check(importedClasses);
    }

    @Test
    void webMvcTestWithSpringContextShouldEndWithIntegrationTest() {
        JavaClasses importedClasses = new ClassFileImporter()
                .importPackages(BASE_PACKAGE);

        // Note: @WebMvcTest can be both unit test (with mocked services) or integration test (with real services)
        // This rule focuses on those that load full Spring context or use real dependencies
        ArchRule rule = classes()
                .that().areAnnotatedWith(WebMvcTest.class)
                .and().areNotAnnotatedWith("org.springframework.boot.test.mock.mockito.MockBean")
                .should().haveSimpleNameEndingWith("IntegrationTest")
                .allowEmptyShould(true)
                .because("@WebMvcTest classes that use real dependencies are integration tests");

        rule.check(importedClasses);
    }

    @Test
    void integrationTestClassesShouldBeInIntegrationPackage() {
        JavaClasses importedClasses = new ClassFileImporter()
                .importPackages(BASE_PACKAGE);

        ArchRule rule = classes()
                .that().haveSimpleNameEndingWith("IntegrationTest")
                .should().resideInAPackage("..integration..")
                .because("Integration tests should be organized in integration packages");

        rule.check(importedClasses);
    }

    @Test
    void classesInIntegrationPackageShouldEndWithIntegrationTest() {
        JavaClasses importedClasses = new ClassFileImporter()
                .importPackages(BASE_PACKAGE);

        ArchRule rule = classes()
                .that().resideInAPackage("..integration..")
                .and().haveSimpleNameContaining("Test")
                .should().haveSimpleNameEndingWith("IntegrationTest")
                .because("Test classes in integration packages should follow naming convention");

        rule.check(importedClasses);
    }
}
