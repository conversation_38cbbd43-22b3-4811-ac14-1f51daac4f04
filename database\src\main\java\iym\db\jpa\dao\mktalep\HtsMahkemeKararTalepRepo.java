package iym.db.jpa.dao.mktalep;

import iym.common.model.entity.iym.talep.HtsMahkemeKararTalep;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for HtsMahkemeKararTalep entity
 */
@Repository
public interface HtsMahkemeKararTalepRepo extends JpaRepository<HtsMahkemeKararTalep, Long> {

    List<HtsMahkemeKararTalep> findByEvrakId(Long evrakId);
    
    List<HtsMahkemeKararTalep> findByKullaniciId(Long kullaniciId);
    
    List<HtsMahkemeKararTalep> findByDurum(String durum);
    
    List<HtsMahkemeKararTalep> findByKararTip(String kararTip);
    
    List<HtsMahkemeKararTalep> findByHukukBirim(String hukukBirim);
    
    List<HtsMahkemeKararTalep> findByMahkemeIli(String mahkemeIli);
    
    List<HtsMahkemeKararTalep> findByMahkemeKodu(String mahkemeKodu);
    
    List<HtsMahkemeKararTalep> findByMahkemeAdiContainingIgnoreCase(String mahkemeAdi);
    
    Optional<HtsMahkemeKararTalep> findByMahkemeKararNo(String mahkemeKararNo);
    
    List<HtsMahkemeKararTalep> findBySorusturmaNo(String sorusturmaNo);
    
    List<HtsMahkemeKararTalep> findByKayitTarihiBetween(LocalDateTime startDate, LocalDateTime endDate);
    
    List<HtsMahkemeKararTalep> findByMahkemeIliAndMahkemeKodu(String mahkemeIli, String mahkemeKodu);
    
    List<HtsMahkemeKararTalep> findByKararTipAndDurum(String kararTip, String durum);
    
    boolean existsByMahkemeKararNo(String mahkemeKararNo);
}
