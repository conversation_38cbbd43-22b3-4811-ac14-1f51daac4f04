-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create sequence for DMAHKEME_KARAR_TALEP if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = '<PERSON><PERSON><PERSON><PERSON><PERSON>_KARAR_TALEP_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.DMAHKEME_KARAR_TALEP_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

-- Create DMAHKEME_KARAR_TALEP table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'DMAHKEME_KARAR_TALEP';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.DMAHKEME_KARAR_TALEP (
      <PERSON>H<PERSON><PERSON>_KARAR_ID NUMBER NOT NULL,
      <PERSON>VRAK_ID NUMBER NOT NULL,
      <PERSON>U<PERSON>AN<PERSON>I_ID NUMBER NOT NULL,
      <PERSON>AYIT_TARIHI DATE NOT NULL,
      DURUM VARCHAR2(20 BYTE),
      KARAR_TIP_DETAY VARCHAR2(20 BYTE),
      MAHKEME_ADI_DETAY VARCHAR2(250 BYTE),
      MAHKEME_KARAR_NO_DETAY VARCHAR2(50 BYTE),
      MAHKEME_ILI_DETAY VARCHAR2(6 BYTE) NOT NULL,
      SORUSTURMA_NO_DETAY VARCHAR2(50 BYTE),
      ILISKILI_MAHKEME_KARAR_ID NUMBER,
      MAHKEME_KODU_DETAY VARCHAR2(10 BYTE),
      ACIKLAMA_DETAY VARCHAR2(500 BYTE),
      ID NUMBER
    )';
    

  END IF;
END;
/

-- Insert sample data if table is empty
DECLARE
  row_count NUMBER;
BEGIN
  SELECT COUNT(*) INTO row_count FROM iym.DMAHKEME_KARAR_TALEP;
  IF row_count = 0 THEN
    -- Make sure we have mahkeme_karar_talep records
    DECLARE
      mahkeme_count NUMBER;
    BEGIN
      SELECT COUNT(*) INTO mahkeme_count FROM iym.MAHKEME_KARAR_TALEP;
      
      IF mahkeme_count > 0 THEN
        -- Get the IDs of the mahkeme_karar_talep records
        FOR mahkeme_rec IN (
          SELECT m.ID as mahkeme_id, m.EVRAK_ID, m.KULLANICI_ID, m.MAHKEME_ILI
          FROM iym.MAHKEME_KARAR_TALEP m
        ) LOOP
          -- Sample data - Detay kaydı
          INSERT INTO iym.DMAHKEME_KARAR_TALEP (
            ID, MAHKEME_KARAR_ID, EVRAK_ID, KULLANICI_ID, KAYIT_TARIHI,
            DURUM, KARAR_TIP_DETAY, MAHKEME_ADI_DETAY, MAHKEME_KARAR_NO_DETAY,
            MAHKEME_ILI_DETAY, SORUSTURMA_NO_DETAY, MAHKEME_KODU_DETAY, ACIKLAMA_DETAY
          ) VALUES (
            iym.DMAHKEME_KARAR_TALEP_SEQ.NEXTVAL, mahkeme_rec.mahkeme_id, mahkeme_rec.EVRAK_ID, 
            mahkeme_rec.KULLANICI_ID, SYSDATE,
            'AKTIF', 'DETAY', 'DETAY MAHKEME ADI', 'DETAY-' || mahkeme_rec.mahkeme_id,
            mahkeme_rec.MAHKEME_ILI, 'DETAY-SOR-' || mahkeme_rec.mahkeme_id, 'DETAY-KOD', 
            'Detay açıklama kaydı'
          );
        END LOOP;
      END IF;
    END;
  END IF;
END;
/

COMMIT;
