# Docker Environment Configuration for Backend
# PostgreSQL Database configuration for Docker containers
spring.datasource.url=**************************************
spring.datasource.username=iym
spring.datasource.password=iym
spring.datasource.driverClassName=org.postgresql.Driver
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect

# Oracle Database configuration for Makos module (via Docker)
makos.datasource.url=********************************
makos.datasource.username=iym
makos.datasource.password=iym

# JPA configuration for Docker environment
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.properties.hibernate.hbm2ddl.auto=validate
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Flyway configuration for Docker
spring.flyway.enabled=true
spring.flyway.locations=classpath:db/migration
spring.flyway.baseline-on-migrate=true

# Docker specific logging
logging.level.root=INFO
logging.level.iym=DEBUG
logging.level.org.hibernate=ERROR
logging.level.org.flywaydb=INFO

# Connection pool configuration for Docker
spring.datasource.hikari.connectionTimeout=20000
spring.datasource.hikari.maximumPoolSize=5

# Application specific properties for Docker
app.init-db=false

# CORS Configuration for Docker
cors.allowed.origins=http://localhost:4200

# Makos API configuration
makos.api.base-url=http://makos:8080/makosapi
makos.api.username=iym
makos.api.password=iym