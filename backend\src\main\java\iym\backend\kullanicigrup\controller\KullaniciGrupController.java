package iym.backend.kullanicigrup.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import iym.backend.kullanicigrup.dto.KullaniciGrupDto;
import iym.backend.kullanicigrup.service.KullaniciGrupService;

import java.util.List;

@RestController
@RequestMapping("/api/kullanici-gruplar")
@RequiredArgsConstructor
public class KullaniciGrupController {

    private final KullaniciGrupService service;

    @GetMapping
    public ResponseEntity<List<KullaniciGrupDto>> getAll() {
        return ResponseEntity.ok(service.findAll());
    }

    @GetMapping("/{id}")
    public ResponseEntity<KullaniciGrupDto> getById(@PathVariable Long id) {
        return ResponseEntity.ok(service.findById(id));
    }

    @PostMapping
    public ResponseEntity<KullaniciGrupDto> create(@RequestBody KullaniciGrupDto dto) {
        return ResponseEntity.ok(service.save(dto));
    }

    @PutMapping("/{id}")
    public ResponseEntity<KullaniciGrupDto> update(@PathVariable Long id, @RequestBody KullaniciGrupDto dto) {
        dto.setId(id);
        return ResponseEntity.ok(service.save(dto));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        service.delete(id);
        return ResponseEntity.noContent().build();
    }
}

