-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create sequence for HEDEFLER_AIDIYAT_SEQ if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = 'HEDEFLER_AIDIYAT_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.HEDEFLER_AIDIYAT_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

-- Create HEDEFLER_AIDIYAT table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'HEDEFLER_AIDIYAT';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.HEDEFLER_AIDIYAT (
      ID NUMBER NOT NULL,
      HEDEF_ID NUMBER NOT NULL,
      AIDIYAT_KOD VARCHAR2(15) NOT NULL,
      TARIH DATE NOT NULL,
      <PERSON><PERSON><PERSON><PERSON><PERSON>I_ID NUMBER NOT NULL,
      CONSTRAINT HEDEF_AIDIYAT_ID_IDX PRIMARY KEY (ID) ENABLE
    )';
  END IF;
END;
/


COMMIT;
