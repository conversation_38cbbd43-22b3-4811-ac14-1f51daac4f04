package iym.db.jpa.service.impl;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.TespitTurleri;
import iym.common.service.db.DbTespitTurleriService;
import iym.db.jpa.dao.TespitTurleriRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;


@Service
public class DbTespitTurleriServiceImpl extends GenericDbServiceImpl<TespitTurleri, String> implements DbTespitTurleriService {

    private final TespitTurleriRepo tespitTurleriRepo;

    @Autowired
    public DbTespitTurleriServiceImpl(TespitTurleriRepo repository) {
        super(repository);
        this.tespitTurleriRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<TespitTurleri> findByTespitTuru(Long tespitTuru){
        return tespitTurleriRepo.findByTespitTuru(tespitTuru);
    }


}
