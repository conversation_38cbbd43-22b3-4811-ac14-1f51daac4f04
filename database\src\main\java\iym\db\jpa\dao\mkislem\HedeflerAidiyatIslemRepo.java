package iym.db.jpa.dao.mkislem;

import iym.common.model.entity.iym.mkislem.HedeflerAidiyatIslem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * Repository interface for HedeflerAidiyatIslem entity
 */
@Repository
public interface HedeflerAidiyatIslemRepo extends JpaRepository<HedeflerAidiyatIslem, Long> {

//    List<HedeflerAidiyatIslem> findByHedefTalepId(Long hedefTalepId);

}
