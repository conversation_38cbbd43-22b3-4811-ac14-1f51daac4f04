package iym.common.service.db.mk;

import iym.common.model.entity.iym.mk.MahkemeAidiyat;
import iym.common.service.db.GenericDbService;

import java.util.List;
import java.util.Optional;


public interface DbMahkemeAidiyatService extends GenericDbService<MahkemeAidiyat, Long> {

    List<MahkemeAidiyat> findByMahkemeKararId(Long mahkemeKararId);

    Optional<MahkemeAidiyat> findById(Long id);

    Optional<MahkemeAidiyat> findByMahkemeKararIdAndAidiyatKod(Long mahkemeKararId, String aidiyatKodu);

}
