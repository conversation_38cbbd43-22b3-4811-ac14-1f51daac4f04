package iym.backend.kullanici.service;

import iym.backend.authentication.dto.ChangePasswordRequest;
import iym.backend.authentication.dto.JwtResponse;
import iym.backend.kullanici.dto.KullaniciDto;
import iym.backend.kullanici.entity.Kullanici;
import iym.backend.kullanici.enums.enumKullaniciStatus;
import iym.backend.kullanici.mapper.KullaniciMapper;
import iym.backend.kullanici.repository.KullaniciRepository;
import iym.backend.kullanicigrup.entity.KullaniciGrup;
import iym.backend.kullanicigrup.repository.KullaniciGrupRepository;
import iym.backend.shared.service.BaseServiceImpl;
import iym.common.util.ExceptionUtils;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class KullaniciServiceImpl extends BaseServiceImpl<Kullanici, KullaniciDto, Long>
        implements KullaniciService {

    private final KullaniciRepository kullaniciRepository;
    private final KullaniciMapper kullaniciMapper;
    private final PasswordEncoder passwordEncoder;
    private final KullaniciGrupRepository kullaniciGrupRepository;

    public KullaniciServiceImpl(
            KullaniciRepository kullaniciRepository,
            KullaniciMapper kullaniciMapper,
            PasswordEncoder passwordEncoder,
            KullaniciGrupRepository kullaniciGrupRepository
    ) {
        super(kullaniciRepository, kullaniciMapper);
        this.kullaniciRepository = kullaniciRepository;
        this.kullaniciMapper = kullaniciMapper;
        this.passwordEncoder = passwordEncoder;
        this.kullaniciGrupRepository = kullaniciGrupRepository;
    }

    @Override
    public KullaniciDto save(KullaniciDto dto) {
        List<KullaniciGrup> gruplar = kullaniciGrupRepository.findAllById(dto.getKullaniciGrupIdList());

        Kullanici entity = kullaniciMapper.toEntity(dto);
        kullaniciMapper.mapKullaniciGruplar(dto, entity, gruplar);

        // Set status from DTO if provided, otherwise use default
        if (dto.getStatus() != null && !dto.getStatus().isBlank()) {
            entity.setStatus(enumKullaniciStatus.valueOf(dto.getStatus()));
        } else {
            entity.setStatus(enumKullaniciStatus.SIFRE_DEGISTIRMELI);
        }

        entity.setParola(passwordEncoder.encode(entity.getParola()));

        return kullaniciMapper.toDto(kullaniciRepository.save(entity));
    }

    public JwtResponse changePassword(ChangePasswordRequest request) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String username = (String) authentication.getPrincipal(); // sadece username ve password içerir

        Kullanici kullanici = kullaniciRepository.findByKullaniciAdi(username)
                .orElseThrow(() -> ExceptionUtils.USER_NOT_FOUND);

        // Mevcut parola kontrolü
        if (!passwordEncoder.matches(request.getCurrentPassword(), kullanici.getParola())) {
            throw ExceptionUtils.INVALID_CURRENT_PASSWORD;
        }

        // Yeni parolalar eşleşiyor mu?
        if (!request.getNewPassword().equals(request.getNewPassword2())) {
            throw ExceptionUtils.PASSWORD_MISMATCH;
        }

        // Parolayı güncelle

        kullanici.setStatus(enumKullaniciStatus.AKTIF);
        kullanici.setParola(passwordEncoder.encode(request.getNewPassword()));
        kullaniciRepository.save(kullanici);

        return new JwtResponse("Parola başarıyla güncellendi", enumKullaniciStatus.AKTIF.name());
    }

    @Override
    public Optional<Kullanici> findByKullaniciAdi(String kullaniciAdi) {
        return kullaniciRepository.findByKullaniciAdi(kullaniciAdi);
    }
}

