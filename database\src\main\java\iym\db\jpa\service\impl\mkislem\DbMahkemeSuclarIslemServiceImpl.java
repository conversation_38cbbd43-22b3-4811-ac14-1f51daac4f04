package iym.db.jpa.service.impl.mkislem;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.mkislem.MahkemeSuclarIslem;
import iym.common.service.db.mkislem.DbMahkemeSuclarIslemService;
import iym.db.jpa.dao.mkislem.MahkemeSuclarIslemRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class DbMahkemeSuclarIslemServiceImpl extends GenericDbServiceImpl<MahkemeSuclarIslem, Long> implements DbMahkemeSuclarIslemService {

    private final MahkemeSuclarIslemRepo mahkemeSuclarIslemRepo;

    @Autowired
    public DbMahkemeSuclarIslemServiceImpl(MahkemeSuclarIslemRepo repository) {
        super(repository);
        this.mahkemeSuclarIslemRepo = repository;
    }

    @Override
    public List<MahkemeSuclarIslem> findByMahkemeKararIslemId(Long mahkemeKararIslemöId){
        return mahkemeSuclarIslemRepo.findByMahkemeKararIslemId(mahkemeKararIslemöId);
    }

    @Override
    public Optional<MahkemeSuclarIslem> findByMahkemeKararIdAndSucTipKodu(Long mahkemeKararId, String sucTipKodu){
        return mahkemeSuclarIslemRepo.findByMahkemeKararIslemIdAndSucTipKodu(mahkemeKararId, sucTipKodu);
    }

}
