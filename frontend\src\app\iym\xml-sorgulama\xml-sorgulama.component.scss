// XML Sorgulama Component Stilleri

.p-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  
  .p-card-header {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    color: white;
    font-weight: 600;
    border-radius: 8px 8px 0 0;
  }
}

// XML Editör stilleri
.xml-editor-container {
  position: relative;
  
  textarea {
    background: #1e1e1e;
    color: #d4d4d4;
    border: 1px solid #404040;
    border-radius: 6px;
    padding: 1rem;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.5;
    resize: vertical;
    
    &:focus {
      border-color: #8b5cf6;
      box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
      outline: none;
    }
    
    &::placeholder {
      color: #6b7280;
    }
  }
}

// XML Önizleme stilleri
.xml-preview-container {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  max-height: 400px;
  overflow: auto;
  
  .xml-content {
    margin: 0;
    padding: 1rem;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
    color: #495057;
    white-space: pre-wrap;
    word-wrap: break-word;
    
    &.formatted {
      background: #1e1e1e;
      color: #d4d4d4;
      
      // XML syntax highlighting (basit)
      .xml-tag {
        color: #569cd6;
      }
      
      .xml-attribute {
        color: #9cdcfe;
      }
      
      .xml-value {
        color: #ce9178;
      }
    }
  }
}

// XML Ağaç görünümü
.xml-tree-container {
  min-height: 300px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 1rem;
  
  .tree-node {
    margin-left: 1rem;
    border-left: 1px dashed #dee2e6;
    padding-left: 1rem;
    
    .node-label {
      font-weight: 600;
      color: #495057;
      
      &.element {
        color: #0d6efd;
      }
      
      &.attribute {
        color: #6f42c1;
      }
      
      &.text {
        color: #198754;
      }
    }
  }
}

// Toolbar stilleri
.p-toolbar {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 0.75rem;
  
  .p-toolbar-group-start,
  .p-toolbar-group-end {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
}

// Tab stilleri
.p-tabview {
  .p-tabview-nav {
    background: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    
    .p-tabview-nav-link {
      background: transparent;
      border: none;
      color: #6c757d;
      font-weight: 500;
      padding: 0.75rem 1rem;
      
      &:hover {
        background: #e9ecef;
        color: #495057;
      }
      
      &.p-highlight {
        background: #8b5cf6;
        color: white;
        border-radius: 6px 6px 0 0;
      }
    }
  }
  
  .p-tabview-panels {
    background: white;
    border: none;
    padding: 1rem;
  }
}

// Validasyon sonuç stilleri
.validation-status {
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  
  &.success {
    background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
    border: 1px solid #10b981;
    
    .status-icon {
      color: #059669;
    }
    
    .status-text {
      color: #065f46;
    }
  }
  
  &.error {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    border: 1px solid #ef4444;
    
    .status-icon {
      color: #dc2626;
    }
    
    .status-text {
      color: #991b1b;
    }
  }
}

// İstatistik kartları
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  
  .stats-item {
    text-align: center;
    padding: 1rem;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    transition: all 0.3s ease;
    
    &:hover {
      background: #e9ecef;
      transform: translateY(-2px);
    }
    
    .stats-icon {
      font-size: 1.5rem;
      margin-bottom: 0.5rem;
    }
    
    .stats-label {
      font-size: 0.875rem;
      color: #6c757d;
      margin-bottom: 0.25rem;
    }
    
    .stats-value {
      font-size: 1.25rem;
      font-weight: 700;
      color: #495057;
    }
  }
}

// Dialog stilleri
.p-dialog {
  .p-dialog-header {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    color: white;
    border-radius: 8px 8px 0 0;
    
    .p-dialog-header-icon {
      color: white;
    }
  }
  
  .p-dialog-content {
    padding: 1.5rem;
    max-height: 70vh;
    overflow-y: auto;
  }
  
  .p-dialog-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e5e7eb;
    background: #f8f9fa;
  }
}

// Hata ve uyarı mesajları
.error-item,
.warning-item {
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 0.75rem;
  position: relative;
  
  .message-header {
    display: flex;
    justify-content: between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
    
    .message-text {
      flex: 1;
      font-weight: 600;
    }
    
    .message-meta {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-left: 1rem;
    }
  }
  
  .message-location {
    font-size: 0.875rem;
    opacity: 0.8;
    margin-bottom: 0.5rem;
    
    .location-icon {
      margin-right: 0.25rem;
    }
  }
  
  .message-suggestion {
    background: rgba(255, 255, 255, 0.5);
    border-radius: 4px;
    padding: 0.75rem;
    margin-top: 0.5rem;
    font-size: 0.875rem;
    
    .suggestion-icon {
      margin-right: 0.25rem;
    }
  }
}

.error-item {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #991b1b;
  
  .message-suggestion {
    background: rgba(254, 202, 202, 0.3);
  }
}

.warning-item {
  background: #fffbeb;
  border: 1px solid #fed7aa;
  color: #92400e;
  
  .message-suggestion {
    background: rgba(254, 215, 170, 0.3);
  }
}

// Şablon seçici
.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
  
  .template-card {
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
    
    &:hover {
      border-color: #8b5cf6;
      background: #f8faff;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(139, 92, 246, 0.15);
    }
    
    .template-header {
      display: flex;
      align-items: center;
      margin-bottom: 1rem;
      
      .template-icon {
        font-size: 1.5rem;
        color: #8b5cf6;
        margin-right: 0.75rem;
      }
      
      .template-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: #1f2937;
      }
    }
    
    .template-description {
      color: #6b7280;
      margin-bottom: 1rem;
      line-height: 1.5;
    }
    
    .template-meta {
      display: flex;
      align-items: center;
      font-size: 0.875rem;
      color: #9ca3af;
      
      .meta-icon {
        margin-right: 0.25rem;
      }
    }
  }
}

// Buton stilleri
.p-button {
  border-radius: 6px;
  font-weight: 600;
  transition: all 0.3s ease;
  
  &.p-button-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
  }
  
  &:hover {
    transform: translateY(-1px);
  }
  
  &.p-button-success {
    background: #10b981;
    border-color: #10b981;
    
    &:hover {
      background: #059669;
      border-color: #059669;
    }
  }
  
  &.p-button-info {
    background: #3b82f6;
    border-color: #3b82f6;
    
    &:hover {
      background: #1d4ed8;
      border-color: #1d4ed8;
    }
  }
  
  &.p-button-secondary {
    background: #6b7280;
    border-color: #6b7280;
    
    &:hover {
      background: #4b5563;
      border-color: #4b5563;
    }
  }
}

// Dropdown stilleri
.p-select {
  border-radius: 6px;
  
  .p-select-label {
    padding: 0.75rem;
  }
  
  &:focus {
    border-color: #8b5cf6;
    box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.2);
  }
}

// Responsive tasarım
@media (max-width: 1024px) {
  .grid {
    grid-template-columns: 1fr;
  }
  
  .xml-editor-container textarea {
    font-size: 12px;
  }
  
  .template-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .p-toolbar {
    .p-toolbar-group-start,
    .p-toolbar-group-end {
      flex-direction: column;
      align-items: stretch;
      gap: 0.5rem;
    }
  }
  
  .p-dialog {
    width: 95vw !important;
    margin: 1rem;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

// Animasyonlar
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

// Scrollbar stilleri
.xml-preview-container::-webkit-scrollbar,
.p-dialog-content::-webkit-scrollbar {
  width: 8px;
}

.xml-preview-container::-webkit-scrollbar-track,
.p-dialog-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.xml-preview-container::-webkit-scrollbar-thumb,
.p-dialog-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.xml-preview-container::-webkit-scrollbar-thumb:hover,
.p-dialog-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
