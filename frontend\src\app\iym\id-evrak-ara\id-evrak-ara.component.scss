// Component specific styles
:host {
  display: block;
  width: 100%;
}

// Card styling
::ng-deep .p-card {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  border-radius: 0.5rem;
  
  .p-card-header {
    background-color: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    padding: 1rem 1.5rem;
    
    h3 {
      font-weight: 600;
      color: #1e293b;
      margin: 0;
    }
  }
  
  .p-card-body {
    padding: 1.5rem;
  }
}

// Table styling
::ng-deep .p-datatable {
  .p-datatable-header {
    background-color: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
  }
  
  .p-datatable-thead > tr > th {
    background-color: #f1f5f9;
    border-bottom: 1px solid #e2e8f0;
    font-weight: 600;
    color: #475569;
    padding: 0.75rem;
    
    &:first-child {
      border-top-left-radius: 0.375rem;
    }
    
    &:last-child {
      border-top-right-radius: 0.375rem;
    }
  }
  
  .p-datatable-tbody > tr {
    border-bottom: 1px solid #f1f5f9;
    
    &:hover {
      background-color: #f8fafc;
    }
    
    > td {
      padding: 0.75rem;
      vertical-align: middle;
    }
  }
  
  .p-datatable-tbody > tr:nth-child(even) {
    background-color: #fafafa;
  }
}

// Form styling
::ng-deep .p-inputtext {
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  
  &:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    outline: none;
  }
  
  &::placeholder {
    color: #9ca3af;
  }
}

::ng-deep .p-dropdown {
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  
  &:not(.p-disabled):hover {
    border-color: #9ca3af;
  }
  
  &:not(.p-disabled).p-focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
  
  .p-dropdown-label {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
  }
}

::ng-deep .p-calendar {
  .p-inputtext {
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    
    &:focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
  }
  
  .p-datepicker-trigger {
    border: 1px solid #d1d5db;
    border-left: none;
    border-radius: 0 0.375rem 0.375rem 0;
    background-color: #f9fafb;
    
    &:hover {
      background-color: #f3f4f6;
    }
  }
}

// Button styling
::ng-deep .p-button {
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.15s ease-in-out;
  
  &.p-button-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
  }
  
  &:not(.p-disabled):hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }
}

// Tag styling
::ng-deep .p-tag {
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  
  &.p-tag-success {
    background-color: #dcfce7;
    color: #166534;
  }
  
  &.p-tag-warn {
    background-color: #fef3c7;
    color: #92400e;
  }
  
  &.p-tag-danger {
    background-color: #fee2e2;
    color: #991b1b;
  }
  
  &.p-tag-secondary {
    background-color: #f3f4f6;
    color: #374151;
  }
  
  &.p-tag-info {
    background-color: #dbeafe;
    color: #1e40af;
  }
}

// Dialog styling
::ng-deep .p-dialog {
  border-radius: 0.5rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  
  .p-dialog-header {
    background-color: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
    border-radius: 0.5rem 0.5rem 0 0;
    padding: 1rem 1.5rem;
    
    .p-dialog-title {
      font-weight: 600;
      color: #1e293b;
    }
  }
  
  .p-dialog-content {
    padding: 1.5rem;
  }
  
  .p-dialog-footer {
    background-color: #f8fafc;
    border-top: 1px solid #e2e8f0;
    border-radius: 0 0 0.5rem 0.5rem;
    padding: 1rem 1.5rem;
  }
}

// Toast styling
::ng-deep .p-toast {
  .p-toast-message {
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    
    &.p-toast-message-success {
      background-color: #dcfce7;
      border-color: #22c55e;
      color: #166534;
    }
    
    &.p-toast-message-error {
      background-color: #fee2e2;
      border-color: #ef4444;
      color: #991b1b;
    }
    
    &.p-toast-message-info {
      background-color: #dbeafe;
      border-color: #3b82f6;
      color: #1e40af;
    }
  }
}

// Loading spinner styling
::ng-deep .p-progressspinner {
  .p-progressspinner-circle {
    stroke: #3b82f6;
  }
}

// Responsive design
@media (max-width: 768px) {
  .grid {
    grid-template-columns: 1fr;
  }
  
  ::ng-deep .p-datatable {
    .p-datatable-thead > tr > th,
    .p-datatable-tbody > tr > td {
      padding: 0.5rem;
      font-size: 0.875rem;
    }
  }
}

@media (max-width: 1024px) {
  .grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

// Utility classes
.font-mono {
  font-family: 'Courier New', Courier, monospace;
}

.text-gray-400 {
  color: #9ca3af;
}

.text-gray-500 {
  color: #6b7280;
}

.text-gray-700 {
  color: #374151;
}

.text-gray-800 {
  color: #1f2937;
}

.bg-gray-400 {
  background-color: #9ca3af;
}

.bg-gray-500 {
  background-color: #6b7280;
}

.bg-gray-700 {
  background-color: #374151;
}

.bg-gray-800 {
  background-color: #1f2937;
}
