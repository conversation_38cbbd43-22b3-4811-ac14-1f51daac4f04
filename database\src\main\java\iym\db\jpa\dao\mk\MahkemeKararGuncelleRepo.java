package iym.db.jpa.dao.mk;

import iym.common.model.entity.iym.mk.MahkemeKararGuncelleme;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface MahkemeKararGuncelleRepo extends JpaRepository<MahkemeKararGuncelleme, Long> {

    Optional<MahkemeKararGuncelleme> findByDetayMahkemeKararId(Long detayMahkemeKararId);

}
