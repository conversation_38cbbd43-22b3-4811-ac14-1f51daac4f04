package iym.db.jpa.dao.mk;

import iym.common.model.entity.iym.mk.HedeflerDetay;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;


@Repository
public interface HedeflerDetayRepo extends JpaRepository<HedeflerDetay, Long> {

    List<HedeflerDetay> findByMahkemeKararId(Long mahkemeKararId);

    Optional<HedeflerDetay> findByDetayMahkemeKararId(Long detayMahkemeKararId);

    Optional<HedeflerDetay> findByMahkemeKararIdAndHedefNoAndHedefTipi(Long mahkemeKararId, String hedefNo, Integer hedefTipi);


}
