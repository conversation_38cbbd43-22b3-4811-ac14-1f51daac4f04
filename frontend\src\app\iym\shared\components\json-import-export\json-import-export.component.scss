// JSON Import/Export Component Styles
.json-dialog {
  .p-dialog-content {
    padding: 0;
  }
}

.json-content {
  .p-tabview {
    .p-tabview-panels {
      padding: 1.5rem;
    }
  }
}

.export-section,
.import-section {
  h4 {
    color: #374151;
    font-weight: 600;
  }
  
  textarea {
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    line-height: 1.4;
    resize: vertical;
    
    &:focus {
      outline: none;
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    
    &.border-red-300 {
      border-color: #fca5a5;
      background-color: #fef2f2;
    }
    
    &.border-green-300 {
      border-color: #86efac;
      background-color: #f0fdf4;
    }
  }
}

// Info boxes
.bg-blue-50 {
  background-color: #eff6ff;
  border: 1px solid #dbeafe;
}

.bg-yellow-50 {
  background-color: #fefce8;
  border: 1px solid #fef3c7;
}

// Button spacing
.flex.gap-3 {
  gap: 0.75rem;
}

// Validation messages
.text-red-600 {
  color: #dc2626;
}

.text-yellow-600 {
  color: #d97706;
}

.text-green-600 {
  color: #16a34a;
}

.text-blue-800 {
  color: #1e40af;
}

.text-yellow-800 {
  color: #92400e;
}

// File upload styling
.p-fileupload {
  .p-fileupload-choose {
    width: 100%;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .json-content {
    .p-tabview-panels {
      padding: 1rem;
    }
  }
  
  .flex.gap-3 {
    flex-direction: column;
    gap: 0.5rem;
    
    .p-button {
      width: 100%;
    }
  }
}
