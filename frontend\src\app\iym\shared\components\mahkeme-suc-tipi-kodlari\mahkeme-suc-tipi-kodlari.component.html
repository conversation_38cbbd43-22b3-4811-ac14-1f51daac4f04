<div class="flex flex-col">
  <label class="text-sm font-medium text-gray-700 mb-2">
    {{ label }}
    <span *ngIf="required" class="text-red-500">*</span>
  </label>
  
  <p-multiSelect
    [ngModel]="value"
    (ngModelChange)="onValueChange($event)"
    [options]="options"
    [showClear]="true"
    [filter]="false"
    [showToggleAll]="false"
    [disabled]="disabled"
    display="chip"
    [placeholder]="placeholder"
    class="w-full"
    [dropdownIcon]="'pi pi-chevron-down'"
    [style]="{ maxHeight: '200px' }"
    panelStyle="{maxHeight: '200px', overflow: 'auto'}"
    appendTo="body"
    [maxSelectedLabels]="10"
  >
    <ng-template pTemplate="header">
      <div class="p-2 border-b">
        <div class="flex gap-2">
          <input
            type="text"
            pInputText
            placeholder="Yeni suç tipi kodu ekle..."
            [(ngModel)]="newSucTipiKodu"
            (keyup.enter)="addNewSucTipiKodu(); $event.preventDefault(); $event.stopPropagation()"
            (click)="$event.stopPropagation()"
            class="flex-1"
          />
          <p-button 
            icon="pi pi-plus" 
            size="small" 
            [disabled]="!newSucTipiKodu.trim()" 
            (onClick)="addNewSucTipiKodu()"
          >
          </p-button>
        </div>
      </div>
    </ng-template>
  </p-multiSelect>
</div>
