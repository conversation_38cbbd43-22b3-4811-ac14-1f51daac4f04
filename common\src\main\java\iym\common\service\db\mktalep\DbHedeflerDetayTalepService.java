package iym.common.service.db.mktalep;

import iym.common.model.entity.iym.talep.HedeflerDetayTalep;
import iym.common.service.db.GenericDbService;

import java.util.List;
import java.util.Optional;


public interface DbHedeflerDetayTalepService extends GenericDbService<HedeflerDetayTalep, Long> {

    List<HedeflerDetayTalep> findByMahkemeKararTalepId(Long mahkemeKararTalepId);

    Optional<HedeflerDetayTalep> findByDetayMahkemeKararTalepId(Long detayMahkemeKararTalepId);

    Optional<HedeflerDetayTalep> findByMahkemeKararTalepIdAndHedefNoAndHedefTipi(Long mahkemeKararTalepId, String hedefNo, Integer hedefTipi);

    Optional<HedeflerDetayTalep> findHedeflerDetayIslem(Long mahkemeKararIslemId, String hedefNo, Integer hedefTipi);


}
