package iym.common.service.db;

import iym.common.model.entity.iym.HedefTipleri;

import java.util.List;
import java.util.Optional;

/**
 * Service interface for HedefTipleri entity
 */
public interface DbHedefTipleriService extends GenericDbService<HedefTipleri, Long> {

    Optional<HedefTipleri> findByHedefKodu(Long hedefKodu);
    
    List<HedefTipleri> findByHedefTipi(String hedefTipi);
    
    List<HedefTipleri> findBySonlandirmami(String sonlandirmami);
    
    List<HedefTipleri> findByDurum(String durum);
    
    List<HedefTipleri> findByHedefTanim(String hedefTanim);
    
    List<HedefTipleri> findByAktifmi(Long aktifmi);
    
    List<HedefTipleri> findByHitapaGonderilecekmi(Long hitapaGonderilecekmi);
    
    List<HedefTipleri> findByHedefTipiContainingIgnoreCase(String hedefTipi);
    
    List<HedefTipleri> findAllByOrderBySnoAsc();
}
