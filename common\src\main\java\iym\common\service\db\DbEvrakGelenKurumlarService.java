package iym.common.service.db;

import iym.common.model.entity.iym.EvrakGelenKurumlar;

import java.util.List;
import java.util.Optional;

/**
 * Service interface for EvrakGelenKurumlar entity
 */
public interface DbEvrakGelenKurumlarService extends GenericDbService<EvrakGelenKurumlar, Long> {

    Optional<EvrakGelenKurumlar> findByKurumKod(String kurumKod);
    
    List<EvrakGelenKurumlar> findByKurumAdi(String kurumAdi);
    
    List<EvrakGelenKurumlar> findByKurum(String kurum);
    
    List<EvrakGelenKurumlar> findByIdx(Long idx);
    
    List<EvrakGelenKurumlar> findByKurumAdiContainingIgnoreCase(String kurumAdi);
    
    List<EvrakGelenKurumlar> findByKurumContainingIgnoreCase(String kurum);
    
    List<EvrakGelenKurumlar> findByKurumKodContaining(String kurumKod);
    
    List<EvrakGelenKurumlar> findAllByOrderByIdxAsc();
    
    boolean existsByKurumKod(String kurumKod);
    
    boolean existsByKurumAdi(String kurumAdi);
}
