-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;


-- Create sequence for MAHKEME_ADI if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = 'MAH<PERSON>ME_ADI_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.MAHKEME_ADI_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

-- Create MAHKEME_ADI table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'MAHKEME_ADI';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE '
            CREATE TABLE IYM.MAHKEME_ADI
            (
              MAHKEME_KODU VARCHAR2(10 BYTE) NOT NULL,
              MAHKEME_IL_ILCE VARCHAR2(10 BYTE),
              MAHKEME_TURU VARCHAR2(2 BYTE),
              MAHKEME_SAYI VARCHAR2(2 BYTE),
              <PERSON><PERSON>KEME_ADI VARCHAR2(250 BYTE),
              EKLEME_TARIHI DATE,
              EKLEYEN_ID NUMBER,
              SILINDI VARCHAR2(1 BYTE),
             CONSTRAINT MAHKEME_ADI_KOD_INDX PRIMARY KEY
              (MAHKEME_KODU)  ENABLE
            )
    ';

  END IF;
END;
/

-- Insert sample data if table is empty
DECLARE
  row_count NUMBER;
BEGIN
  SELECT COUNT(*) INTO row_count FROM iym.MAHKEME_ADI;
  IF row_count = 0 THEN
    -- Make sure we have evrak and users in the respective tables
    DECLARE
      evrak_count NUMBER;
      user_count NUMBER;
    BEGIN
          INSERT INTO MAHKEME_ADI(MAHKEME_KODU, MAHKEME_IL_ILCE, MAHKEME_TURU ,MAHKEME_SAYI ,MAHKEME_ADI, EKLEME_TARIHI,  EKLEYEN_ID , SILINDI) VALUES ('08030100', '0803', '01', '00', 'ARTVİN BORÇKA CBS', sysdate, 1, 'H') ;
          INSERT INTO MAHKEME_ADI(MAHKEME_KODU, MAHKEME_IL_ILCE, MAHKEME_TURU ,MAHKEME_SAYI ,MAHKEME_ADI, EKLEME_TARIHI,  EKLEYEN_ID , SILINDI) VALUES ('08030300', '0803', '03', '00', 'ARTVİN BORÇKA ASLİYE CM', sysdate, 1, 'H') ;
          INSERT INTO MAHKEME_ADI(MAHKEME_KODU, MAHKEME_IL_ILCE, MAHKEME_TURU ,MAHKEME_SAYI ,MAHKEME_ADI, EKLEME_TARIHI,  EKLEYEN_ID , SILINDI) VALUES ('08000100', '0800', '01', '00', 'ARTVİN CBS', sysdate, 1, 'H') ;
          INSERT INTO MAHKEME_ADI(MAHKEME_KODU, MAHKEME_IL_ILCE, MAHKEME_TURU ,MAHKEME_SAYI ,MAHKEME_ADI, EKLEME_TARIHI,  EKLEYEN_ID , SILINDI) VALUES ('06000101', '0600', '01', '00', 'ANKARA MERKEZ 1. AĞIR', sysdate, 1, 'H') ;
          INSERT INTO MAHKEME_ADI(MAHKEME_KODU, MAHKEME_IL_ILCE, MAHKEME_TURU ,MAHKEME_SAYI ,MAHKEME_ADI, EKLEME_TARIHI,  EKLEYEN_ID , SILINDI) VALUES ('06000102', '0600', '02', '00', 'ANKARA MERKEZ 2. AĞIR', sysdate, 1, 'H') ;

    END;
  END IF;
END;
/




COMMIT;
