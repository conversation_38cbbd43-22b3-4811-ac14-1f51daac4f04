-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create sequence for HEDEFLER_ISLEM_SEQ if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = 'HEDEFLER_ISLEM_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.HEDEFLER_ISLEM_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

-- Create MAHKEME_KARAR table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'HEDEFLER_ISLEM';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.HEDEFLER_ISLEM (
          ID NUMBER
        , BIRIM_KOD NUMBER
        , KULLANICI_ID NUMBER
        , TEK_MASA_KUL_ID NUMBER
        , HEDEF_NO VARCHAR2(100) NOT NULL
        , HEDEF_TIPI VARCHAR2(20) NOT NULL
        , HEDEF_ADI VARCHAR2(100)
        , HEDEF_SOYADI VARCHAR2(100)
        , BASLAMA_TARIHI DATE
        , SURESI NUMBER
        , SURE_TIPI NUMBER
        , UZATMA_SAYISI NUMBER
        , DURUMU VARCHAR2(100)
        , ACIKLAMA VARCHAR2(250)
        , MAHKEME_KARAR_ID NUMBER NOT NULL
        , HEDEF_AIDIYAT_ID NUMBER
        , GRUP_KOD NUMBER
        , AIDIYAT_KOD VARCHAR2(35)
        , UNIQ_KOD NUMBER
        , KAYIT_TARIHI DATE
        , TANIMLAMA_TARIHI DATE
        , KAPATMA_KARAR_ID NUMBER
        , KAPATMA_TARIHI DATE
        , IMHA VARCHAR2(20)
        , IMHA_TARIHI DATE
        , UZATMA_ID NUMBER
        , DEGISME_DURUMU VARCHAR2(500)
        , OPERATOR VARCHAR2(30)
        , ACILMI  VARCHAR2(1)
        , HEDEF_118_ADI VARCHAR2(128)
        , HEDEF_118_SOYADI VARCHAR2(128)
        , HEDEF_118_ADRES VARCHAR2(512)
        , CANAK_NO VARCHAR2(100)
        , TCKN VARCHAR2(11)
        , CONSTRAINT HEDEFLER_ISLEM_PKX PRIMARY KEY(ID)ENABLE

  )';

  END IF;
END;
/

COMMIT;
