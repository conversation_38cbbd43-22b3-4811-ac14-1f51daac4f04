package iym.common.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "<PERSON>leti<PERSON>im denetlenmesi karar türü", type = "string", allowableValues = {
        "YENI",
        "UZATMA",
        "SONLANDIRMA"
})
public enum IletisimDenetlenmesiKararTuru {
	YENI(0),
	UZATMA(1),
	SONLANDIRMA(2);

	private final int kararTuru;

	IletisimDenetlenmesiKararTuru(int kararTuru){
		this.kararTuru = kararTuru;
	}

	@JsonValue
	public int getKararTuru(){
		return this.kararTuru;
	}

	@JsonCreator
	public static IletisimDenetlenmesiKararTuru fromName(String name) {
		for (IletisimDenetlenmesiKararTuru kararTuru : IletisimDenetlenmesiKararTuru.values()) {
			if (kararTuru.name().equals(name)) {
				return kararTuru;
			}
		}
		throw new IllegalArgumentException("Gecersiz kararTuru: '" + name + "'");
	}

	//@JsonCreator
	public static IletisimDenetlenmesiKararTuru fromValue(int value) {
		for (IletisimDenetlenmesiKararTuru evrakTuru : IletisimDenetlenmesiKararTuru.values()) {
			if (evrakTuru.kararTuru == value) {
				return evrakTuru;
			}
		}
		throw new IllegalArgumentException("Gecersiz kararTuru: '" + value + "'");
	}
}
