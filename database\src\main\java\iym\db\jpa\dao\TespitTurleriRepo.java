package iym.db.jpa.dao;

import iym.common.model.entity.iym.TespitTurleri;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Repository interface for TespitTuru entity
 */
@Repository
public interface TespitTurleriRepo extends JpaRepository<TespitTurleri, String> {

    Optional<TespitTurleri> findByTespitTuru(Long tespitTuru);

}
