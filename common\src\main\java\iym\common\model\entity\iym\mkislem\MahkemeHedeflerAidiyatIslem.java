package iym.common.model.entity.iym.mkislem;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Entity class for MAHKEME_AIDIYAT_TALEP table
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
//talep icin
//bim aidiyat HEDEFLER_AIDIYAT_TALEP   normal aidiyat MAHKEME_HEDEFLER_AIDIYAT_TALEP

@Entity(name = "MahkemeHedeflerAidiyatIslem")
@Table(name = "MAHKEME_HEDEFLER_AIDIYAT_ISLEM")
public class MahkemeHedeflerAidiyatIslem implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "Mk_HDF_AIDIYAT_ISLEM_SEQ")
    @SequenceGenerator(name = "Mk_HDF_AIDIYAT_ISLEM_SEQ", sequenceName = "Mk_HDF_AIDIYAT_ISLEM_SEQ", allocationSize = 1)
    private Long id;

    @Column(name = "HEDEF_ID", nullable = false)
    @NotNull
    private Long hedefId;

    @Column(name = "AIDIYAT_KOD", nullable = false, length = 15)
    @NotNull
    @Size(max = 15)
    private String aidiyatKod;

    @Column(name = "TARIH", nullable = false)
    @NotNull
    private LocalDateTime tarih;

    @Column(name = "MAHKEME_KARAR_ID", nullable = false)
    @NotNull
    private Long mahkemeKararId;

    @Column(name = "DURUMU", length = 10)
    @Size(max = 10)
    private String durumu;

    @Column(name = "KULLANICI_ID")
    private Long kullaniciId;

}
