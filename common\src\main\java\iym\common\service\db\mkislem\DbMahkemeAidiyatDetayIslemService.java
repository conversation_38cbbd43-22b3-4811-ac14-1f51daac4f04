package iym.common.service.db.mkislem;

import iym.common.model.entity.iym.mkislem.MahkemeAidiyatDetayIslem;
import iym.common.service.db.GenericDbService;

import java.util.List;

/**
 * Service interface for MahkemeAidiyatDetayIslem entity
 */
public interface DbMahkemeAidiyatDetayIslemService extends GenericDbService<MahkemeAidiyatDetayIslem, Long> {

    List<MahkemeAidiyatDetayIslem> findByMahkemeKararIslemId(Long mahkemeKararIslemId);

    List<MahkemeAidiyatDetayIslem> findByMahkemeKararDetayIslemId(Long mahkemeKararDetayIslemId);
}
