package iym.db.jpa.dao;

import iym.common.enums.KullaniciKurum;
import iym.common.enums.MakosUserRoleType;
import iym.common.enums.UserStatusType;
import iym.common.model.entity.makos.MakosUser;
import iym.common.testcontainer.AbstractOracleTestContainerForDataJpa;
import iym.spring.db.loader.DbLoader;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Oracle Testcontainer integration tests for MakosUserRepo.
 * <p>
 * This test class uses Oracle Testcontainer to test Oracle-specific features:
 * - Oracle SEQUENCE generator (MAKOS_USER_SEQ)
 * - Oracle constraint behavior
 * - Oracle-specific SQL queries
 * - Entity persistence with Oracle database
 *
 * <AUTHOR> Team
 */

@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
@DataJpaTest
@ContextConfiguration(classes = {DbLoader.class})
@ActiveProfiles("oracle-test")
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@Import({AbstractOracleTestContainerForDataJpa.OracleTestContainerConfiguration.class})
@Transactional
@Execution(ExecutionMode.SAME_THREAD) // Temporarily disabled parallel execution for debugging
@DisplayName("MakosUserRepo Oracle Integration Tests")
class MakosUserRepoOracleIntegrationTest extends AbstractOracleTestContainerForDataJpa {

    @Autowired
    private TestEntityManager entityManager;

    @Autowired
    private MakosUserRepo makosUserRepo;

    private MakosUser testUser;

    @BeforeEach
    void setUp() {
        // Ensure clean container state for parallel execution
        ensureCleanContainerState();

        // Create test user for Oracle sequence testing
        testUser = new MakosUser();
        testUser.setUsername("oracle_test_user");
        testUser.setPassword("$2a$10$testpasswordhash");
        testUser.setStatus(UserStatusType.ACTIVE);
        testUser.setRole(MakosUserRoleType.ROLE_KURUM_KULLANICI);
        testUser.setKurum(KullaniciKurum.ADLI);
    }

    @AfterEach
    void tearDown() {
        // Clean up after each test to prevent interference in parallel execution
        try {
            if (entityManager != null) {
                entityManager.clear();
            }
        } catch (Exception e) {
            // Log but don't fail the test
            System.out.println("⚠️ Warning: Error during test cleanup: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("Should save MakosUser with Oracle SEQUENCE generator")
    void shouldSaveMakosUser_withOracleSequenceGenerator() {
        // When - Save entity using Oracle SEQUENCE
        MakosUser savedUser = makosUserRepo.save(testUser);
        entityManager.flush();

        // Then - Verify Oracle SEQUENCE generated ID
        assertThat(savedUser.getId()).isNotNull();
        assertThat(savedUser.getId()).isGreaterThan(0L);
        assertThat(savedUser.getUsername()).isEqualTo("oracle_test_user");
    }

    @Test
    @DisplayName("Should generate sequential IDs using Oracle SEQUENCE")
    void shouldGenerateSequentialIds_usingOracleSequence() {
        // Given - Create multiple users
        MakosUser user1 = new MakosUser();
        user1.setUsername("seq_test_user1");
        user1.setPassword("$2a$10$testpass1");
        user1.setStatus(UserStatusType.ACTIVE);
        user1.setRole(MakosUserRoleType.ROLE_KURUM_KULLANICI);
        user1.setKurum(KullaniciKurum.ADLI);

        MakosUser user2 = new MakosUser();
        user2.setUsername("seq_test_user2");
        user2.setPassword("$2a$10$testpass2");
        user2.setStatus(UserStatusType.ACTIVE);
        user2.setRole(MakosUserRoleType.ROLE_ADMIN);
        user2.setKurum(KullaniciKurum.EMNIYET);

        // When - Save both users
        MakosUser savedUser1 = makosUserRepo.save(user1);
        MakosUser savedUser2 = makosUserRepo.save(user2);
        entityManager.flush();

        // Then - Verify Oracle SEQUENCE behavior
        assertThat(savedUser1.getId()).isNotNull();
        assertThat(savedUser2.getId()).isNotNull();
        assertThat(savedUser2.getId()).isGreaterThan(savedUser1.getId());
    }

    @Test
    @DisplayName("Should find MakosUser by username using Oracle database")
    void shouldFindMakosUserByUsername_usingOracle() {
        // Given - Save test user
        makosUserRepo.save(testUser);
        entityManager.flush();

        // When - Find by username
        Optional<MakosUser> foundUser = makosUserRepo.findByUsername("oracle_test_user");

        // Then - Verify Oracle query result
        assertThat(foundUser).isPresent();
        assertThat(foundUser.get().getUsername()).isEqualTo("oracle_test_user");
        assertThat(foundUser.get().getStatus()).isEqualTo(UserStatusType.ACTIVE);
        assertThat(foundUser.get().getRole()).isEqualTo(MakosUserRoleType.ROLE_KURUM_KULLANICI);
    }

    @Test
    @DisplayName("Should find MakosUsers by status using Oracle database")
    void shouldFindMakosUsersByStatus_usingOracle() {
        // Given - Save users with different statuses
        testUser.setStatus(UserStatusType.ACTIVE);
        makosUserRepo.save(testUser);

        // Create additional active user to test multiple results
        MakosUser activeUser2 = new MakosUser();
        activeUser2.setUsername("active_oracle_user2");
        activeUser2.setPassword("$2a$10$activepass2");
        activeUser2.setStatus(UserStatusType.ACTIVE);
        activeUser2.setRole(MakosUserRoleType.ROLE_ADMIN);
        activeUser2.setKurum(KullaniciKurum.MIT);
        makosUserRepo.save(activeUser2);

        MakosUser inactiveUser = new MakosUser();
        inactiveUser.setUsername("inactive_oracle_user");
        inactiveUser.setPassword("$2a$10$inactivepass");
        inactiveUser.setStatus(UserStatusType.PASSIVE);
        inactiveUser.setRole(MakosUserRoleType.ROLE_KURUM_KULLANICI);
        inactiveUser.setKurum(KullaniciKurum.EMNIYET);
        makosUserRepo.save(inactiveUser);

        entityManager.flush();

        // When - Find by status
        List<MakosUser> activeUsers = makosUserRepo.findByStatus(UserStatusType.ACTIVE);
        List<MakosUser> inactiveUsers = makosUserRepo.findByStatus(UserStatusType.PASSIVE);

        // Then - Verify Oracle query results
        assertThat(activeUsers).hasSize(2); // Two active users created in test
        assertThat(inactiveUsers).hasSize(1);

        boolean foundTestUser = activeUsers.stream()
                .anyMatch(user -> "oracle_test_user".equals(user.getUsername()));
        assertThat(foundTestUser).isTrue();

        boolean foundActiveUser2 = activeUsers.stream()
                .anyMatch(user -> "active_oracle_user2".equals(user.getUsername()));
        assertThat(foundActiveUser2).isTrue();

        assertThat(inactiveUsers.get(0).getUsername()).isEqualTo("inactive_oracle_user");
    }

    @Test
    @DisplayName("Should find MakosUsers by role ordered by username using Oracle")
    void shouldFindMakosUsersByRoleOrderedByUsername_usingOracle() {
        // Given - Save users with different roles
        testUser.setRole(MakosUserRoleType.ROLE_ADMIN);
        testUser.setUsername("zebra_admin");
        makosUserRepo.save(testUser);

        MakosUser alphaAdmin = new MakosUser();
        alphaAdmin.setUsername("alpha_admin");
        alphaAdmin.setPassword("$2a$10$alphapass");
        alphaAdmin.setStatus(UserStatusType.ACTIVE);
        alphaAdmin.setRole(MakosUserRoleType.ROLE_ADMIN);
        alphaAdmin.setKurum(KullaniciKurum.MIT);
        makosUserRepo.save(alphaAdmin);

        MakosUser regularUser = new MakosUser();
        regularUser.setUsername("regular_user");
        regularUser.setPassword("$2a$10$regularpass");
        regularUser.setStatus(UserStatusType.ACTIVE);
        regularUser.setRole(MakosUserRoleType.ROLE_KURUM_KULLANICI);
        regularUser.setKurum(KullaniciKurum.JANDARMA);
        makosUserRepo.save(regularUser);

        entityManager.flush();

        // When - Find admins ordered by username
        List<MakosUser> adminUsers = makosUserRepo.findByRoleOrderByUsernameAsc(MakosUserRoleType.ROLE_ADMIN);

        // Then - Verify Oracle ordering
        assertThat(adminUsers).hasSizeGreaterThanOrEqualTo(2);

        // Verify ordering
        for (int i = 1; i < adminUsers.size(); i++) {
            String current = adminUsers.get(i).getUsername();
            String previous = adminUsers.get(i - 1).getUsername();
            assertThat(current.compareTo(previous)).isGreaterThanOrEqualTo(0);
        }
    }

    @Test
    @DisplayName("Should retrieve all MakosUsers ordered by username using Oracle")
    void shouldRetrieveAllMakosUsersOrderedByUsername_usingOracle() {
        // Given - Save multiple users
        testUser.setUsername("zebra_user");
        makosUserRepo.save(testUser);

        MakosUser alphaUser = new MakosUser();
        alphaUser.setUsername("alpha_user");
        alphaUser.setPassword("$2a$10$alphapass");
        alphaUser.setStatus(UserStatusType.ACTIVE);
        alphaUser.setRole(MakosUserRoleType.ROLE_KURUM_KULLANICI);
        alphaUser.setKurum(KullaniciKurum.BTK);
        makosUserRepo.save(alphaUser);

        // Create third user to test ordering with multiple records
        MakosUser middleUser = new MakosUser();
        middleUser.setUsername("middle_user");
        middleUser.setPassword("$2a$10$middlepass");
        middleUser.setStatus(UserStatusType.ACTIVE);
        middleUser.setRole(MakosUserRoleType.ROLE_ADMIN);
        middleUser.setKurum(KullaniciKurum.JANDARMA);
        makosUserRepo.save(middleUser);

        entityManager.flush();

        // When - Find all ordered by username
        List<MakosUser> allUsers = makosUserRepo.findAllByOrderByUsernameAsc();

        // Then - Verify Oracle ordering behavior
        assertThat(allUsers).isNotEmpty();
        assertThat(allUsers.size()).isEqualTo(3); // Three users created in test

        // Verify ordering
        for (int i = 1; i < allUsers.size(); i++) {
            String current = allUsers.get(i).getUsername();
            String previous = allUsers.get(i - 1).getUsername();
            assertThat(current.compareTo(previous)).isGreaterThanOrEqualTo(0);
        }

        // Verify specific ordering of our test data
        assertThat(allUsers.get(0).getUsername()).isEqualTo("alpha_user");
        assertThat(allUsers.get(1).getUsername()).isEqualTo("middle_user");
        assertThat(allUsers.get(2).getUsername()).isEqualTo("zebra_user");
    }

    @Test
    @DisplayName("Should enforce Oracle unique constraint on username")
    void shouldEnforceOracleUniqueConstraint_onUsername() {
        // Given - Save first user
        makosUserRepo.save(testUser);
        entityManager.flush();

        // When - Try to save another user with same username
        MakosUser duplicateUser = new MakosUser();
        duplicateUser.setUsername("oracle_test_user"); // Same username
        duplicateUser.setPassword("$2a$10$differentpass");
        duplicateUser.setStatus(UserStatusType.ACTIVE);
        duplicateUser.setRole(MakosUserRoleType.ROLE_ADMIN);
        duplicateUser.setKurum(KullaniciKurum.EMNIYET_SIBER);

        // Then - Should throw constraint violation exception
        org.junit.jupiter.api.Assertions.assertThrows(Exception.class, () -> {
            makosUserRepo.save(duplicateUser);
            entityManager.flush();
        });
    }
}
