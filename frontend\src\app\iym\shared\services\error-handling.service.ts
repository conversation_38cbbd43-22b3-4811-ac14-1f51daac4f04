import { Injectable } from '@angular/core';
import { MessageService } from 'primeng/api';

@Injectable({
  providedIn: 'root'
})
export class ErrorHandlingService {

  constructor(private messageService: MessageService) {}

  /**
   * Ortak error handling metodu
   * @param err HTTP error response
   * @param loading loading state'i false yapmak için callback (opsiyonel)
   */
  handleError(err: any, setLoadingFalse?: () => void): void {
    // Loading state'i false yap
    if (setLoadingFalse) {
      setLoadingFalse();
    }

    console.log('❌ Hata:', err);

    // Hata mesajını ayıkla - handle new Response wrapper format
    let errorMessage = 'İşlem sırasında bir hata oluştu.';
    let errorId = err.error?.errorId || 'N/A';

    // MakosResponseException için özel kontrol
    if (err.error?.resultDetails) {
        errorMessage = err.error.resultDetails;
    } else if (err.error?.responseMessage) {
        // MakosResponseException veya diğer responseMessage içeren hatalar için
        errorMessage = err.error.responseMessage;
        console.log('MakosResponseException yakalandı:', errorMessage);
    } else if (err.error?.response?.responseMessage) {
        // Backend'den gelen hata mesajı (eski format)
        errorMessage = err.error.response.responseMessage;
    } else if (err.error?.message) {
        // GlobalExceptionHandler'den gelen hata mesajı (yeni format)
        errorMessage = err.error.message;
    } else if (err.error?.errors) {
        // ValidationErrorResponse'den gelen çoklu hata mesajları
        const errors = err.error.errors;
        if (Array.isArray(errors) && errors.length > 0) {
            errorMessage = errors.map((e: any) => e.message || e).join('\n');
        } else if (typeof errors === 'string') {
            errorMessage = errors;
        }
    } else if (err.message) {
        // HTTP hata mesajı
        errorMessage = err.message;
    }

    // HTTP status code'a göre summary ve severity belirle
    const statusCode = err.status;
    let summary: string;
    let severity: 'error' | 'warn' | 'info';

    if (statusCode === 401) {
        // 401 Unauthorized için özel mesaj
        summary = 'Oturum Geçersiz';
        severity = 'warn';
        errorMessage = 'Oturumunuzun süresi dolmuş. Lütfen tekrar giriş yapın.';
    } else if (statusCode >= 400 && statusCode < 500) {
        // Diğer 4xx hatalar için
        summary = `Uyarı (${statusCode})`;
        severity = 'warn';
    } else {
        // 5xx ve diğer hatalar için
        summary = `Hata (${statusCode})`;
        severity = 'error';
    }

    // Toast mesajını göster
    this.messageService.add({
        severity: severity,
        summary: summary,
        detail: statusCode === 401 ? errorMessage : `${errorMessage}\nHata ID: ${errorId}`,
        sticky: true
    });
  }

  /**
   * Basit error handling - sadece mesaj gösterir
   * @param message Gösterilecek hata mesajı
   * @param severity Mesaj türü (default: 'error')
   */
  showError(message: string, severity: 'error' | 'warn' | 'info' = 'error'): void {
    this.messageService.add({
      severity: severity,
      summary: severity === 'error' ? 'Hata' : severity === 'warn' ? 'Uyarı' : 'Bilgi',
      detail: message,
      sticky: severity === 'error'
    });
  }

  /**
   * Başarı mesajı göster
   * @param message Gösterilecek başarı mesajı
   * @param summary Başlık (default: 'Başarılı')
   */
  showSuccess(message: string, summary: string = 'Başarılı'): void {
    this.messageService.add({
      severity: 'success',
      summary: summary,
      detail: message
    });
  }

  /**
   * Dropdown loading hatası için özel metod
   * @param dropdownName Dropdown adı
   * @param setLoadingFalse Loading state'i false yapmak için callback
   */
  handleDropdownError(dropdownName: string, setLoadingFalse: () => void): void {
    setLoadingFalse();
    this.showError(`${dropdownName} yüklenirken bir hata oluştu.`);
  }
}
