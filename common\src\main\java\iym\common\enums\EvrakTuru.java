package iym.common.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Evrak türü", type = "string", allowableValues = {
        "ILETISIMIN_TESPITI",
        "ILETISIMIN_DENETLENMESI",
        "GENEL_EVRAK"
})
public enum EvrakTuru {
	ILETISIMIN_TESPITI(0),
	ILETISIMIN_DENETLENMESI(1),
	GENEL_EVRAK(2);
	
	private final int evrakTuru;
	
	EvrakTuru(int evrakTuru){
		this.evrakTuru = evrakTuru;
	}

	public int getEvrakTuru(){
		return this.evrakTuru;
	}

	@Override
	@JsonValue
	public String toString() {
		return this.name();
	}

	@JsonCreator
	public static EvrakTuru fromName(String name) {
		for (EvrakTuru evrakTuru : EvrakTuru.values()) {
			if (evrakTuru.name().equals(name)) {
				return evrakTuru;
			}
		}
		throw new IllegalArgumentException("Gecersiz evrakTuru: '" + name + "'");
	}

	//@JsonCreator
	public static EvrakTuru fromValue(int value) {
		for (EvrakTuru evrakTuru : EvrakTuru.values()) {
			if (evrakTuru.evrakTuru == value) {
				return evrakTuru;
			}
		}
		throw new IllegalArgumentException("Gecersiz evrakTuru: '" + value + "'");
	}
}
