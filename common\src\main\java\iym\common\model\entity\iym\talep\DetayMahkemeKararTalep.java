package iym.common.model.entity.iym.talep;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Entity class for MAHKEME_KARAR_TALEP table
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "DMahkemeKararTalep")
@Table(name = "DMAHKEME_KARAR_TALEP")
public class DetayMahkemeKararTalep implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "DMAHKEME_KARAR_TALEP_SEQ")
    @SequenceGenerator(name = "DMAHKEME_KARAR_TALEP_SEQ", sequenceName = "DMAHKEME_KARAR_TALEP_SEQ", allocationSize = 1)
    private Long id;

    @Column(name = "MAHKEME_KARAR_ID", nullable = false)
    @NotNull
    private Long mahkemeKararTalepId;

    @Column(name = "EVRAK_ID", nullable = false)
    @NotNull
    private Long evrakId;

    @Column(name = "KULLANICI_ID", nullable = false)
    @NotNull
    private Long kullaniciId;

    @Column(name = "KAYIT_TARIHI")
    private LocalDateTime kayitTarihi;

    @Column(name = "DURUM", length = 20)
    @Size(max = 20)
    private String durum;

    @Column(name = "ILISKILI_MAHKEME_KARAR_ID", nullable = false)
    @NotNull
    private Long iliskiliMahkemeKararId;

    @Column(name = "KARAR_TIP_DETAY", length = 20)
    @Size(max = 20)
    private String kararTipDetay;

    @Column(name = "MAHKEME_ADI_DETAY", length = 250)
    @Size(max = 250)
    private String mahkemeAdi;

    @Column(name = "MAHKEME_KARAR_NO_DETAY", length = 50)
    @Size(max = 50)
    private String mahkemeKararNo;

    @Column(name = "SORUSTURMA_NO_DETAY", length = 20)
    @Size(max = 20)
    private String sorusturmaNo;

    @Column(name = "MAHKEME_ILI_DETAY", length = 6)
    @Size(max = 6)
    private String mahkemeIlIlceKodu;

    @Column(name = "MAHKEME_KODU_DETAY", length = 20)
    @Size(max = 20)
    private String mahkemeKodu;

    @Column(name = "ACIKLAMA_DETAY", length = 500)
    @Size(max = 500)
    private String aciklama;

}
