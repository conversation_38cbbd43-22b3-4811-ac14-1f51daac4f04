package iym.common.config;

import lombok.Getter;

@Getter
public class SystemParameters {

    private static String localDbDriverClass;
    private static String localDbAddress;
    private static String localDbUsername;
    private static String localDbPassword;

    static {
        loadConfig();
    }

    public static void loadConfig(){
        PropertyReader reader = PropertyReader.getInstance();

        localDbDriverClass = reader.getRequiredProperty("LOCAL_DB_DRIVER_CLASS");
        localDbAddress = reader.getRequiredProperty("LOCAL_DB_ADDRESS");
        localDbUsername = reader.getRequiredProperty("LOCAL_DB_USERNAME");
        localDbPassword = reader.getRequiredProperty("LOCAL_DB_PASSWORD");
    }

}
