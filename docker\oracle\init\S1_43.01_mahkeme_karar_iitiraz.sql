-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

----------KULLANILIYOR MU?

-- Create sequence for MAHKEME_KARAR_ITIRAZ_SEQ if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = 'MAHKEME_KARAR_ITIRAZ_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.MAHKEME_KARAR_ITIRAZ_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

--TODO : vharchar daki byte lar kald<PERSON>k
-- Create MAHKEME_KARAR_ISLEM table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'MAHKEME_KARAR_ITIRAZ';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.MAHKEME_KARAR_ITIRAZ (
       ID NUMBER NOT NULL
     , TALEP_KISI_ID NUMBER NOT NULL
     , TALEP_TARIHI DATE NOT NULL
     , TALEP_ACIKLAMA VARCHAR2(1000)
     , ITIRAZ_EDILEN_EVRAK_ID NUMBER NOT NULL
     , ITIRAZ_EDILEN_KURUM VARCHAR2(10) NOT NULL
     , DURUM VARCHAR2(100) NOT NULL
     , ITIRAZ_NEDENI VARCHAR2(10) NOT NULL
     , ISLEYEN_KISI_ID NUMBER
     , ISLEME_TARIHI DATE
     , ITIRAZ_YAZI_ID NUMBER
     , ISLEYEN_ACIKLAMA VARCHAR2(1000)
     , ITIRAZA_CEVAP_EVRAK_ID NUMBER
     , BEKLEMEDE VARCHAR2(1)
     , ARSIV VARCHAR2(1)
     , ITIRAZ_SONUC VARCHAR2(20)
     , ITIRAZ_SONUC_EVRAK_ID NUMBER
     , ITIRAZ_SONUC_TARIH DATE
     , ITIRAZ_SONUC_KAYIT_KISI_ID NUMBER
     , SILINME_NEDENI VARCHAR2(1000)
     , SILINME_TARIHI DATE
     , SILEN_KISI_ID NUMBER
     , CONSTRAINT PK_MAHKEME_KARAR_ITIRAZ PRIMARY KEY (ID) ENABLE

    )';

  END IF;
END;
/


COMMIT;
