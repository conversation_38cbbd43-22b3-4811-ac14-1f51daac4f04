<div class="p-4">
  <!-- Başlık -->
  <div class="flex justify-between items-center mb-4">
    <h2 class="text-2xl font-bold text-gray-800">
      <i class="pi pi-user-edit mr-2"></i>
      Hedef Ad Soyad Güncelle
    </h2>
    <div class="flex gap-2">
      <p-button
        icon="pi pi-refresh"
        label="Sıfırla"
        severity="secondary"
        size="small"
        (onClick)="onReset()"
        [disabled]="yukleniyor">
      </p-button>
    </div>
  </div>

  <form [formGroup]="talepForm" (ngSubmit)="onSubmit()">

    <!-- Evrak <PERSON> -->
    <p-card header="Evrak Detayları" class="mb-4">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">

        <!-- Evrak No -->
        <div class="flex flex-col">
          <label for="evrakNo" class="text-sm font-medium text-gray-700 mb-1">
            Evrak No <span class="text-red-500">*</span>
          </label>
          <input
            pInputText
            formControlName="evrakNo"
            placeholder="Evrak numarası giriniz"
            [class]="isFieldInvalid('evrakNo') ? 'ng-invalid ng-dirty' : ''"
            class="w-full">
          <small *ngIf="isFieldInvalid('evrakNo')" class="text-red-500">
            {{ getFieldError('evrakNo') }}
          </small>
        </div>

        <!-- Evrak Tarihi -->
        <div class="flex flex-col">
          <label for="evrakTarihi" class="text-sm font-medium text-gray-700 mb-1">
            Evrak Tarihi <span class="text-red-500">*</span>
          </label>
          <p-datepicker
            formControlName="evrakTarihi"
            placeholder="Evrak tarihi seçiniz"
            icon="pi pi-calendar" iconDisplay="input"
            dateFormat="dd/mm/yy"
            [class]="isFieldInvalid('evrakTarihi') ? 'ng-invalid ng-dirty' : ''"
            class="w-full">
          </p-datepicker>
          <small *ngIf="isFieldInvalid('evrakTarihi')" class="text-red-500">
            {{ getFieldError('evrakTarihi') }}
          </small>
        </div>

        <!-- Evrak Kurum Kodu -->
        <div class="flex flex-col">
          <label for="evrakKurumKodu" class="text-sm font-medium text-gray-700 mb-1">
            Evrak Kurum Kodu <span class="text-red-500">*</span>
          </label>
          <input
            pInputText
            formControlName="evrakKurumKodu"
            placeholder="Kurum kodu giriniz"
            [class]="isFieldInvalid('evrakKurumKodu') ? 'ng-invalid ng-dirty' : ''"
            class="w-full">
          <small *ngIf="isFieldInvalid('evrakKurumKodu')" class="text-red-500">
            {{ getFieldError('evrakKurumKodu') }}
          </small>
        </div>

        <!-- Evrak Türü -->
        <div class="flex flex-col">
          <label for="evrakTuru" class="text-sm font-medium text-gray-700 mb-1">
            Evrak Türü <span class="text-red-500">*</span>
          </label>
          <p-select
            formControlName="evrakTuru"
            [options]="evrakTuruOptions"
            optionLabel="label"
            optionValue="value"
            placeholder="Evrak türü seçiniz"
            inputId="evrakTuru"
            [class]="isFieldInvalid('evrakTuru') ? 'ng-invalid ng-dirty' : ''"
            class="w-full"
            [disabled]="true">
          </p-select>
          <small *ngIf="isFieldInvalid('evrakTuru')" class="text-red-500">
            {{ getFieldError('evrakTuru') }}
          </small>
        </div>

        <!-- Havale Birimi -->
        <div class="flex flex-col">
          <label for="havaleBirimi" class="text-sm font-medium text-gray-700 mb-1">
            Havale Birimi
          </label>
          <input
            pInputText
            formControlName="havaleBirimi"
            placeholder="Havale birimi giriniz"
            class="w-full">
        </div>

        <!-- Geldiği İl İlçe Kodu -->
        <div class="flex flex-col">
          <label for="geldigiIlIlceKodu" class="text-sm font-medium text-gray-700 mb-1">
            Geldiği İl İlçe Kodu <span class="text-red-500">*</span>
          </label>
          <input
            pInputText
            formControlName="geldigiIlIlceKodu"
            placeholder="İl ilçe kodu giriniz"
            [class]="isFieldInvalid('geldigiIlIlceKodu') ? 'ng-invalid ng-dirty' : ''"
            class="w-full">
          <small *ngIf="isFieldInvalid('geldigiIlIlceKodu')" class="text-red-500">
            {{ getFieldError('geldigiIlIlceKodu') }}
          </small>
        </div>

        <!-- Evrak Konusu -->
        <div class="flex flex-col md:col-span-2">
          <label for="evrakKonusu" class="text-sm font-medium text-gray-700 mb-1">
            Evrak Konusu
          </label>
          <input
            pInputText
            formControlName="evrakKonusu"
            placeholder="Evrak konusu giriniz"
            class="w-full">
        </div>

        <!-- Acil Mi -->
        <div class="flex flex-col">
          <label class="text-sm font-medium text-gray-700 mb-1">
            Durum
          </label>
          <div class="flex items-center">
            <p-checkbox
              formControlName="acilmi"
              binary="true"
              inputId="acilmi">
            </p-checkbox>
            <label for="acilmi" class="ml-2 text-sm text-gray-700">Acil</label>
          </div>
        </div>

      </div>

      <!-- Evrak Açıklama -->
      <div class="mt-4">
        <label for="evrakAciklama" class="text-sm font-medium text-gray-700 mb-1 block">
          Evrak Açıklama
        </label>
        <textarea
          pInputTextarea
          formControlName="evrakAciklama"
          placeholder="Evrak hakkında açıklama giriniz"
          rows="3"
          class="w-full">
        </textarea>
      </div>
    </p-card>

    <!-- Mahkeme Karar Detayları -->
    <p-card header="Mahkeme Karar Detayları" class="mb-4">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">

        <!-- Mahkeme Karar Tipi -->
        <div class="flex flex-col">
          <label for="mahkemeKararTipi" class="text-sm font-medium text-gray-700 mb-1">
            Mahkeme Karar Tipi <span class="text-red-500">*</span>
          </label>
          <p-select
            formControlName="mahkemeKararTipi"
            [options]="mahkemeKararTipOptions"
            optionLabel="label"
            optionValue="value"
            placeholder="Mahkeme karar tipi seçiniz"
            inputId="mahkemeKararTipi"
            [class]="isFieldInvalid('mahkemeKararTipi') ? 'ng-invalid ng-dirty' : ''"
            class="w-full"
            [disabled]="true">
          </p-select>
          <small *ngIf="isFieldInvalid('mahkemeKararTipi')" class="text-red-500">
            {{ getFieldError('mahkemeKararTipi') }}
          </small>
        </div>

        <!-- Mahkeme Kodu -->
        <div class="flex flex-col">
          <label for="mahkemeKodu" class="text-sm font-medium text-gray-700 mb-1">
            Mahkeme Kodu <span class="text-red-500">*</span>
          </label>
          <input
            pInputText
            formControlName="mahkemeKodu"
            placeholder="Mahkeme kodu giriniz"
            [class]="isFieldInvalid('mahkemeKodu') ? 'ng-invalid ng-dirty' : ''"
            class="w-full">
          <small *ngIf="isFieldInvalid('mahkemeKodu')" class="text-red-500">
            {{ getFieldError('mahkemeKodu') }}
          </small>
        </div>

        <!-- Mahkeme Karar No -->
        <div class="flex flex-col">
          <label for="mahkemeKararNo" class="text-sm font-medium text-gray-700 mb-1">
            Mahkeme Karar No <span class="text-red-500">*</span>
          </label>
          <input
            pInputText
            formControlName="mahkemeKararNo"
            placeholder="Karar numarası giriniz"
            [class]="isFieldInvalid('mahkemeKararNo') ? 'ng-invalid ng-dirty' : ''"
            class="w-full">
          <small *ngIf="isFieldInvalid('mahkemeKararNo')" class="text-red-500">
            {{ getFieldError('mahkemeKararNo') }}
          </small>
        </div>

        <!-- Mahkeme İl İlçe Kodu -->
        <div class="flex flex-col">
          <label for="mahkemeIlIlceKodu" class="text-sm font-medium text-gray-700 mb-1">
            Mahkeme İl İlçe Kodu <span class="text-red-500">*</span>
          </label>
          <input
            pInputText
            formControlName="mahkemeIlIlceKodu"
            placeholder="Mahkeme il ilçe kodu giriniz"
            [class]="isFieldInvalid('mahkemeIlIlceKodu') ? 'ng-invalid ng-dirty' : ''"
            class="w-full">
          <small *ngIf="isFieldInvalid('mahkemeIlIlceKodu')" class="text-red-500">
            {{ getFieldError('mahkemeIlIlceKodu') }}
          </small>
        </div>

        <!-- Soruşturma No -->
        <div class="flex flex-col">
          <label for="sorusturmaNo" class="text-sm font-medium text-gray-700 mb-1">
            Soruşturma No
          </label>
          <input
            pInputText
            formControlName="sorusturmaNo"
            placeholder="Soruşturma numarası giriniz"
            class="w-full">
        </div>

      </div>

      <!-- Mahkeme Açıklama -->
      <div class="mt-4">
        <label for="mahkemeAciklama" class="text-sm font-medium text-gray-700 mb-1 block">
          Mahkeme Açıklama
        </label>
        <textarea
          pInputTextarea
          formControlName="mahkemeAciklama"
          placeholder="Mahkeme kararı hakkında açıklama giriniz"
          rows="3"
          class="w-full">
        </textarea>
      </div>
    </p-card>

    <!-- Ad Soyad Güncelleme Detayları -->
    <p-card header="Hedef Ad Soyad Güncelleme Detayları" class="mb-4">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">

        <!-- Hedef No -->
        <div class="flex flex-col md:col-span-2">
          <label for="hedefNo" class="text-sm font-medium text-gray-700 mb-1">
            Hedef No <span class="text-red-500">*</span>
          </label>
          <input
            pInputText
            formControlName="hedefNo"
            placeholder="Hedef numarası giriniz"
            [class]="isFieldInvalid('hedefNo') ? 'ng-invalid ng-dirty' : ''"
            class="w-full">
          <small *ngIf="isFieldInvalid('hedefNo')" class="text-red-500">
            {{ getFieldError('hedefNo') }}
          </small>
        </div>

        <!-- Eski Ad -->
        <div class="flex flex-col">
          <label for="eskiAd" class="text-sm font-medium text-gray-700 mb-1">
            Eski Ad
          </label>
          <input
            pInputText
            formControlName="eskiAd"
            placeholder="Eski ad giriniz"
            class="w-full">
        </div>

        <!-- Eski Soyad -->
        <div class="flex flex-col">
          <label for="eskiSoyad" class="text-sm font-medium text-gray-700 mb-1">
            Eski Soyad
          </label>
          <input
            pInputText
            formControlName="eskiSoyad"
            placeholder="Eski soyad giriniz"
            class="w-full">
        </div>

        <!-- Yeni Ad -->
        <div class="flex flex-col">
          <label for="yeniAd" class="text-sm font-medium text-gray-700 mb-1">
            Yeni Ad <span class="text-red-500">*</span>
          </label>
          <input
            pInputText
            formControlName="yeniAd"
            placeholder="Yeni ad giriniz"
            [class]="isFieldInvalid('yeniAd') ? 'ng-invalid ng-dirty' : ''"
            class="w-full">
          <small *ngIf="isFieldInvalid('yeniAd')" class="text-red-500">
            {{ getFieldError('yeniAd') }}
          </small>
        </div>

        <!-- Yeni Soyad -->
        <div class="flex flex-col">
          <label for="yeniSoyad" class="text-sm font-medium text-gray-700 mb-1">
            Yeni Soyad <span class="text-red-500">*</span>
          </label>
          <input
            pInputText
            formControlName="yeniSoyad"
            placeholder="Yeni soyad giriniz"
            [class]="isFieldInvalid('yeniSoyad') ? 'ng-invalid ng-dirty' : ''"
            class="w-full">
          <small *ngIf="isFieldInvalid('yeniSoyad')" class="text-red-500">
            {{ getFieldError('yeniSoyad') }}
          </small>
        </div>

      </div>

      <!-- Değişiklik Gerekçesi -->
      <div class="mt-4">
        <label for="degisiklikGerekce" class="text-sm font-medium text-gray-700 mb-1 block">
          Değişiklik Gerekçesi <span class="text-red-500">*</span>
        </label>
        <textarea
          pInputTextarea
          formControlName="degisiklikGerekce"
          placeholder="Ad soyad değişikliği gerekçesini giriniz"
          rows="4"
          [class]="isFieldInvalid('degisiklikGerekce') ? 'ng-invalid ng-dirty' : ''"
          class="w-full">
        </textarea>
        <small *ngIf="isFieldInvalid('degisiklikGerekce')" class="text-red-500">
          {{ getFieldError('degisiklikGerekce') }}
        </small>
      </div>
    </p-card>

    <!-- Dosya Yükleme -->
    <p-card header="Mahkeme Karar Dosyası" class="mb-4">
      <div class="border-2 border-dashed border-gray-300 rounded-lg p-6">
        <p-fileUpload
          name="mahkemeKararDosyasi"
          [multiple]="false"
          accept=".pdf"
          [maxFileSize]="10000000"
          [auto]="false"
          [showUploadButton]="false"
          [showCancelButton]="false"
          (onSelect)="onDosyaSecildi($event)"
          chooseLabel="Mahkeme Karar Dosyası Seç"
          chooseIcon="pi pi-file"
          class="w-full">

          <ng-template pTemplate="content">
            <div class="text-center">
              <i class="pi pi-cloud-upload text-4xl text-gray-400 mb-4"></i>
              <p class="text-gray-600 mb-2">Mahkeme karar dosyasını buraya sürükleyin veya seçin</p>
              <p class="text-sm text-gray-500">
                Maksimum dosya boyutu: 10MB
              </p>
              <p class="text-sm text-gray-500">
                Kabul edilen format: .pdf
              </p>
            </div>
          </ng-template>
        </p-fileUpload>
      </div>

      <div *ngIf="seciliDosya" class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded">
        <div class="flex items-center">
          <i class="pi pi-file text-blue-500 mr-2"></i>
          <span class="font-medium text-blue-800">{{ seciliDosya.name }}</span>
          <span class="ml-auto text-sm text-blue-600">
            {{ (seciliDosya.size / 1024 / 1024).toFixed(2) }} MB
          </span>
        </div>
      </div>
    </p-card>

    <!-- Ad Soyad Güncelleme Kararı Dosyası -->
    <p-card header="Ad Soyad Güncelleme Kararı Dosyası" class="mb-4">
      <div class="border-2 border-dashed border-gray-300 rounded-lg p-6">
        <p-fileUpload
          name="mahkemeKararDosyasi"
          [multiple]="false"
          accept=".pdf,.doc,.docx"
          [maxFileSize]="10000000"
          [auto]="false"
          [showUploadButton]="false"
          [showCancelButton]="false"
          (onSelect)="onDosyaSecildi($event)"
          chooseLabel="Ad Soyad Güncelleme Kararı Dosyası Seç"
          chooseIcon="pi pi-file"
          class="w-full">

          <ng-template pTemplate="content">
            <div class="text-center">
              <i class="pi pi-cloud-upload text-4xl text-gray-400 mb-4"></i>
              <p class="text-gray-600 mb-2">Ad soyad güncelleme kararı dosyasını buraya sürükleyin veya seçin</p>
              <p class="text-sm text-gray-500">
                Maksimum dosya boyutu: 10MB
              </p>
              <p class="text-sm text-gray-500">
                Kabul edilen formatlar: .pdf, .doc, .docx
              </p>
            </div>
          </ng-template>
        </p-fileUpload>
      </div>

      <div *ngIf="seciliDosya" class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded">
        <div class="flex items-center">
          <i class="pi pi-file text-blue-500 mr-2"></i>
          <span class="font-medium text-blue-800">{{ seciliDosya.name }}</span>
          <span class="ml-auto text-sm text-blue-600">
            {{ (seciliDosya.size / 1024 / 1024).toFixed(2) }} MB
          </span>
        </div>
      </div>
    </p-card>

    <!-- Form Butonları -->
    <div class="flex justify-end gap-3">
      <p-button
        type="button"
        icon="pi pi-times"
        label="İptal"
        severity="secondary"
        (onClick)="onReset()"
        [disabled]="yukleniyor">
      </p-button>

      <p-button
        type="submit"
        icon="pi pi-user-edit"
        label="Hedef Ad Soyad Güncelle"
        [loading]="yukleniyor"
        [disabled]="talepForm.invalid || !seciliDosya">
      </p-button>
    </div>

  </form>

  <!-- Toast Mesajları -->
  <p-toast></p-toast>
</div>
