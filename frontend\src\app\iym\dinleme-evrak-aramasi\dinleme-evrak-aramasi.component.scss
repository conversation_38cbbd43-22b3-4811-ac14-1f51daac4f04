// <PERSON><PERSON>e <PERSON>ı Component Stilleri

.p-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  
  .p-card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: 600;
    border-radius: 8px 8px 0 0;
  }
}

.p-table {
  .p-datatable-header {
    background: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
  }
  
  .p-datatable-thead > tr > th {
    background: #e9ecef;
    color: #495057;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
  }
  
  .p-datatable-tbody > tr:hover {
    background: #f8f9fa;
  }
  
  .p-datatable-tbody > tr > td {
    border-bottom: 1px solid #e9ecef;
  }
}

.p-button {
  &.p-button-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
  }
}

.p-tag {
  font-size: 0.75rem;
  font-weight: 600;
  
  &.p-tag-info {
    background: #3b82f6;
  }
  
  &.p-tag-warn {
    background: #f59e0b;
  }
  
  &.p-tag-success {
    background: #10b981;
  }
  
  &.p-tag-danger {
    background: #ef4444;
  }
}

.p-dialog {
  .p-dialog-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    
    .p-dialog-header-icon {
      color: white;
    }
  }
  
  .p-dialog-content {
    padding: 1.5rem;
  }
}

.p-calendar {
  .p-inputtext {
    width: 100%;
  }
}

.p-dropdown {
  width: 100%;
}

// Responsive tasarım
@media (max-width: 768px) {
  .grid {
    grid-template-columns: 1fr;
  }
  
  .p-table {
    font-size: 0.875rem;
  }
  
  .p-dialog {
    width: 95vw !important;
    margin: 1rem;
  }
}

// Loading overlay
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

// Form elemanları için özel stiller
.form-field {
  margin-bottom: 1rem;
  
  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #374151;
  }
  
  .p-inputtext,
  .p-dropdown,
  .p-calendar {
    width: 100%;
  }
}

// Başlık stilleri
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  
  h2 {
    color: #1f2937;
    font-size: 1.875rem;
    font-weight: 700;
    
    i {
      color: #6366f1;
    }
  }
}

// Export butonları
.export-buttons {
  display: flex;
  gap: 0.5rem;
  
  .p-button {
    &.p-button-success {
      background: #10b981;
      border-color: #10b981;
      
      &:hover {
        background: #059669;
        border-color: #059669;
      }
    }
    
    &.p-button-danger {
      background: #ef4444;
      border-color: #ef4444;
      
      &:hover {
        background: #dc2626;
        border-color: #dc2626;
      }
    }
  }
}

// Detay dialog özel stilleri
.detail-dialog {
  .detail-section {
    margin-bottom: 1.5rem;
    
    h4 {
      color: #1f2937;
      font-size: 1.125rem;
      font-weight: 600;
      margin-bottom: 1rem;
    }
    
    .detail-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      
      .detail-item {
        label {
          font-size: 0.875rem;
          font-weight: 500;
          color: #6b7280;
          display: block;
          margin-bottom: 0.25rem;
        }
        
        p {
          color: #1f2937;
          font-weight: 500;
        }
      }
    }
  }
  
  .target-item {
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    padding: 1rem;
    background: #f9fafb;
    
    .target-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 0.75rem;
      font-size: 0.875rem;
      
      .target-field {
        span {
          font-weight: 600;
          color: #374151;
        }
      }
    }
  }
}

// Empty state stilleri
.empty-state {
  text-align: center;
  padding: 2rem;
  
  i {
    font-size: 3rem;
    color: #9ca3af;
    margin-bottom: 1rem;
  }
  
  p {
    color: #6b7280;
    font-size: 1rem;
  }
}
