# Testcontainers Oracle Timezone Fix

## Problem
Oracle testcontainers failing with timezone error:
```
ORA-00604: error occurred at recursive SQL level 1
ORA-01882: timezone region not found
```

## Root Cause
- GitHub Actions runners don't have proper timezone configuration
- Oracle containers need explicit timezone settings
- Java timezone settings need to be passed to containers

## Solutions Applied

### 1. CI Workflow Changes
- Added `TZ: Europe/Istanbul` environment variable
- Added `JAVA_TOOL_OPTIONS: -Duser.timezone=Europe/Istanbul`
- Separated unit tests from integration tests
- Used Testcontainers instead of Docker services

### 2. Test Execution Strategy
- **Unit Tests**: Run without Oracle dependency (`-Dtest="!*IntegrationTest"`)
- **Integration Tests**: Run with Testcontainers (`-Dtest="*IntegrationTest"`)
- **Profile**: Use `oracle-test` profile for integration tests

### 3. Alternative Solutions (if still failing)

#### Option A: Update AbstractOracleTestContainer
Add timezone environment variables to container:

```java
@Container
protected static final OracleContainer ORACLE_CONTAINER = new OracleContainer("gvenzl/oracle-xe:11.2.0.2-slim-faststart")
    .withDatabaseName("XE")
    .withUsername("iym")
    .withPassword("iym")
    .withEnv("TZ", "Europe/Istanbul")
    .withEnv("ORACLE_TIMEZONE", "Europe/Istanbul")
    .withEnv("NLS_TIMESTAMP_TZ_FORMAT", "YYYY-MM-DD HH24:MI:SS.FF TZH:TZM")
    .withReuse(true)
    .withStartupTimeout(Duration.ofMinutes(5));
```

#### Option B: Add JVM Arguments
In test configuration:

```properties
# application-oracle-test.properties
spring.jpa.properties.hibernate.jdbc.time_zone=Europe/Istanbul
oracle.jdbc.timezoneAsRegion=false
```

#### Option C: Skip Integration Tests in CI
If timezone issues persist, temporarily skip integration tests in CI:

```yaml
- name: Run tests (skip integration tests in CI)
  run: mvn clean test -Dspring.profiles.active=test -Dtest="!*IntegrationTest"
```

## Verification
1. Check Actions tab for workflow status
2. Look for successful test execution
3. Verify artifacts are generated
4. Check logs for timezone-related errors

## Next Steps
1. Monitor first workflow run
2. Apply additional fixes if needed
3. Consider using H2 for CI if Oracle issues persist
4. Add integration test environment for staging
