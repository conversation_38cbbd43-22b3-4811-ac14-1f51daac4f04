#!/bin/bash
# GitLab Runner Setup Script for Linux
# This script helps register a GitLab Runner with the GitLab instance

# Check if Dock<PERSON> is running
if ! docker info > /dev/null 2>&1; then
  echo "Error: Docker is not running. Please start Docker and try again."
  exit 1
fi

# Check if GitLab containers are running
WEB_CONTAINER=$(docker ps --filter "name=docker-gitlab-cicd-web-1" --format "{{.Names}}")
RUNNER_CONTAINER=$(docker ps --filter "name=docker-gitlab-cicd-runner-1" --format "{{.Names}}")

if [ -z "$WEB_CONTAINER" ]; then
  echo "Error: GitLab web container is not running. Please start it with 'docker-compose up -d'."
  exit 1
fi

if [ -z "$RUNNER_CONTAINER" ]; then
  echo "Error: GitLab runner container is not running. Please start it with 'docker-compose up -d'."
  exit 1
fi

# Get registration token from GitLab
echo -e "\nTo register the GitLab Runner, you need a registration token from GitLab."
echo "1. Open http://localhost:8929 in your browser"
echo "2. Login as root (get password with: docker exec -it $WEB_CONTAINER grep 'Password:' /etc/gitlab/initial_root_password)"
echo "3. Go to Admin Area > Runners"
echo "4. Copy the registration token"

read -p "\nEnter the registration token: " TOKEN

if [ -z "$TOKEN" ]; then
  echo "Error: Registration token is required."
  exit 1
fi

# Register the runner
echo -e "\nRegistering GitLab Runner..."

docker exec -it $RUNNER_CONTAINER gitlab-runner register \
  --non-interactive \
  --url "http://web/" \
  --registration-token "$TOKEN" \
  --executor "docker" \
  --docker-image "maven:3.9.6-eclipse-temurin-17" \
  --description "docker-runner" \
  --tag-list "docker,linux" \
  --run-untagged="true" \
  --locked="false" \
  --docker-privileged="true" \
  --docker-volumes "/var/run/docker.sock:/var/run/docker.sock" \
  --docker-network-mode="gitlab_network"

if [ $? -eq 0 ]; then
  echo -e "\nGitLab Runner registered successfully!"
  echo "You can now use GitLab CI/CD pipelines with this runner."
else
  echo -e "\nError: Failed to register GitLab Runner."
  exit 1
fi