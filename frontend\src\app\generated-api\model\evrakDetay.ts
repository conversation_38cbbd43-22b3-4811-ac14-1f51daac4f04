/**
 * IYM Backend OpenAPI definition
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface EvrakDetay { 
    evrakNo: string;
    evrakTarihi: string;
    evrakKurumKodu: string;
    evrakTuru: EvrakDetay.EvrakTuruEnum;
    havaleBirimi?: string;
    aciklama?: string;
    geldigiIlIlceKodu: string;
    acilmi?: boolean;
    evrakKonusu?: string;
}
export namespace EvrakDetay {
    export const EvrakTuruEnum = {
        IletisiminTespiti: 'ILETISIMIN_TESPITI',
        IletisiminDenetlenmesi: 'ILETISIMIN_DENETLENMESI',
        GenelEvrak: 'GENEL_EVRAK'
    } as const;
    export type EvrakTuruEnum = typeof EvrakTuruEnum[keyof typeof EvrakTuruEnum];
}


