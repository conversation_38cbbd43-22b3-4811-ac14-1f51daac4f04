# File Storage Configuration

## Overview
The application now supports external configuration for the base directory where evrak (document) files are stored. This replaces the previously hardcoded path `c:\iym300\iym`.

## Configuration Properties

### Main Property
- **Property Name**: `app.evrak.files.base-dir`
- **Default Value**: `c:\iym300\iym`
- **Environment Variable**: `EVRAK_FILES_BASE_DIR`

### Configuration Files

#### Development (application.properties)
The property is configured in `backend/src/main/resources/application.properties`:
```properties
app.evrak.files.base-dir=${EVRAK_FILES_BASE_DIR:c:\iym300\iym}
```

#### Testing (application-test.properties)
For testing purposes, a separate directory is used:
```properties
app.evrak.files.base-dir=target/test-files
```

## Usage Examples

### Setting via Environment Variable
```bash
# Windows
set EVRAK_FILES_BASE_DIR=D:\myapp\evrak-files

# Linux/Mac
export EVRAK_FILES_BASE_DIR=/home/<USER>/evrak-files
```

### Setting via application.properties
```properties
# Direct path
app.evrak.files.base-dir=D:\myapp\evrak-files

# With environment variable fallback
app.evrak.files.base-dir=${EVRAK_FILES_BASE_DIR:c:\iym300\iym}
```

### Programmatic Access
The directory path can be accessed through:
```java
String baseDir = ApplicationConstants.evrakFilesBaseDir();
```

## Migration Notes
- The default value remains `c:\iym300\iym` for backward compatibility
- Existing installations will continue to work without changes
- New installations can override the path via environment variable or property file
- The directory will be created automatically if it doesn't exist

## Troubleshooting
If you encounter file permission issues:
1. Ensure the configured directory exists
2. Verify the application has write permissions to the directory
3. Check that the path format matches your operating system (Windows: backslashes, Linux/Mac: forward slashes)
4. Consider using environment variables for environment-specific paths