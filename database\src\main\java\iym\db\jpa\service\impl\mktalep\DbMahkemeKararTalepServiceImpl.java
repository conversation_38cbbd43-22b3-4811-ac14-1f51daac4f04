package iym.db.jpa.service.impl.mktalep;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.mk.sorgu.MahkemeKararTalepSorguInfo;
import iym.common.model.entity.iym.mk.sorgu.MahkemeKararTalepSorguParam;
import iym.common.model.entity.iym.talep.MahkemeKararTalep;
import iym.common.service.db.mktalep.DbMahkemeKararTalepService;
import iym.db.jpa.dao.mktalep.MahkemeKararTalepRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Service implementation for MahkemeKararTalep entity
 */
@Service
public class DbMahkemeKararTalepServiceImpl extends GenericDbServiceImpl<MahkemeKararTalep, Long> implements DbMahkemeKararTalepService {

    private final MahkemeKararTalepRepo mahkemeKararTalepRepo;

    @Autowired
    public DbMahkemeKararTalepServiceImpl(MahkemeKararTalepRepo repository) {
        super(repository);
        this.mahkemeKararTalepRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public List<MahkemeKararTalep> findByEvrakId(Long evrakId) {
        return mahkemeKararTalepRepo.findByEvrakId(evrakId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MahkemeKararTalepSorguInfo> islenecekMahkemeKararTalepListesi(String kurumKodu){
        return mahkemeKararTalepRepo.islenecekMahkemeKararTalepListesi(kurumKodu);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MahkemeKararTalepSorguInfo> mahkemeKararTalepSorgu(String kurumKodu, MahkemeKararTalepSorguParam sorguParam){
        return mahkemeKararTalepRepo.mahkemeKararTalepSorgu(kurumKodu, sorguParam);
    }

}
