package iym.makos.domain.mktaleprequest.dbhandler;

import iym.common.enums.EvrakTuru;
import iym.common.enums.KararTuru;
import iym.common.model.entity.iym.EvrakKayit;
import iym.common.model.entity.iym.talep.HtsMahkemeKararTalep;
import iym.common.model.entity.iym.talep.MahkemeKararTalep;
import iym.common.service.db.DbEvrakKayitService;
import iym.common.service.db.DbMahkemeBilgiService;
import iym.common.service.db.mktalep.DbHtsMahkemeKararTalepService;
import iym.common.service.db.mktalep.DbMahkemeKararTalepService;
import iym.makos.domain.base.BaseDomainUnitTest;
import iym.makos.domain.mktalep.requestprocessor.dbhandler.MahkemeKararRequestCommonDbSaver;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.errors.MakosResponseException;
import iym.makos.mapper.KararRequestMapper;
import iym.makos.model.api.EvrakDetay;
import iym.makos.model.api.MahkemeKararBilgisi;
import iym.makos.model.dto.mktalep.request.MkTalepRequest;
import iym.makos.utils.UtilService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.time.LocalDateTime;

import static iym.makos.domain.utils.TestAssertions.assertValidEvrakId;
import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.*;

@Slf4j
@DisplayName("MahkemeKararRequestCommonDbSaverTest Unit Tests")
class MahkemeKararRequestCommonDbSaverTest extends BaseDomainUnitTest {

    @Mock
    private UtilService utilService;

    @Mock
    private KararRequestMapper kararRequestMapper;

    @Mock
    private DbEvrakKayitService dbEvrakKayitService;

    @Mock
    private DbMahkemeBilgiService dbMahkemeBilgiService;

    @Mock
    private DbMahkemeKararTalepService dbMahkemeKararTalepService;

    @Mock
    private DbHtsMahkemeKararTalepService dbHtsMahkemeKararTalepService;

    @InjectMocks
    private MahkemeKararRequestCommonDbSaver dbSaver;

    @Mock
    private MkTalepRequest testRequest;

    private LocalDateTime testDate;
    private Long testUserId;

    @BeforeEach
    void setUp() {
        /*
        testRequest = DummyMKTalepRequest.builder()
                .evrakDetay(createValidEvrakDetay())
                .mahkemeKararBilgisi(createValidMahkemeKararBilgisi())
                .build();
         */

        testDate = createTestDate();
        testUserId = TEST_USER_ID;

    }

    @Test
    void handleDbSave_ShouldThrowException_WhenRequestIsNull() {
        assertThatThrownBy(() -> dbSaver.handleDbSave(null, testDate, testUserId))
                .isInstanceOf(MakosResponseException.class)
                .hasMessageContaining("kararRequest cannot be null");
    }

    @Test
    void handleDbSave_ShouldThrowException_WhenKayitTarihiNull() {
        assertThatThrownBy(() -> dbSaver.handleDbSave(testRequest, null, testUserId))
                .isInstanceOf(MakosResponseException.class)
                .hasMessageContaining("kayitTarihi cannot be null");
    }

    @Test
    void handleDbSave_ShouldThrowException_WhenKullaniciIdNull() {
        assertThatThrownBy(() -> dbSaver.handleDbSave(testRequest, testDate, null))
                .isInstanceOf(MakosResponseException.class)
                .hasMessageContaining("kullaniciId cannot be null");
    }

    @Test
    void handleDbSave_ShouldThrowException_WhenEvrakSiraNoNullOrEmpty() {

        when(utilService.getEvrakSiraNumarasi(anyString(), anyString())).thenReturn(null);

        when(testRequest.getEvrakDetay()).thenReturn(createValidEvrakDetay());
        when(testRequest.getMahkemeKararBilgisi()).thenReturn(createValidMahkemeKararBilgisi());

        assertThatThrownBy(() -> dbSaver.handleDbSave(testRequest, testDate, testUserId))
                .isInstanceOf(MakosResponseException.class)
                .hasMessageContaining(MakosResponseErrorCodes.EVRAK_SIRANO_ALINAMADI);

        // Verify getEvrakSiraNumarasi was called with any request
        verify(utilService).getEvrakSiraNumarasi(any(), any());
        verify(testRequest, times(1)).getEvrakDetay(); // should be called 1 time
        verify(testRequest).getMahkemeKararBilgisi();

    }

    @Test
    void handleDbSave_ShouldSaveHtsTalep_WhenEvrakTuruIsILETISIMIN_TESPITI() {

        //GIVEN
        //talep = ILETISIMIN_TESPITI

        HtsMahkemeKararTalep requestedMahkemeKararTalep = new HtsMahkemeKararTalep();

        Long expectedMahkemeKararTalepId = 4321L;
        HtsMahkemeKararTalep expectedMahkemeKararTalep = new HtsMahkemeKararTalep();
        expectedMahkemeKararTalep.setId(expectedMahkemeKararTalepId);

        String expectedEvrakSiraNo = "EVRAK-01";

        when(utilService.getEvrakSiraNumarasi(anyString(), anyString())).thenReturn(expectedEvrakSiraNo);
        EvrakKayit mockEvrakKayit = new EvrakKayit();
        mockEvrakKayit.setId(1234L);
        when(kararRequestMapper.toEvrakKayit(any(), any(), any(), any(), any())).thenReturn(mockEvrakKayit);
        when(dbEvrakKayitService.save(any())).thenReturn(mockEvrakKayit);
        when(kararRequestMapper.toHTSMahkemeKararTalep(any(), any(), any(), any())).thenReturn(requestedMahkemeKararTalep);
        when(dbHtsMahkemeKararTalepService.save(requestedMahkemeKararTalep)).thenReturn(expectedMahkemeKararTalep);

        // Set up evrak türü for the test
        EvrakDetay mockEvrakDetay = createValidEvrakDetay();
        mockEvrakDetay.setEvrakTuru(EvrakTuru.ILETISIMIN_TESPITI);
        when(testRequest.getEvrakDetay()).thenReturn(mockEvrakDetay);
        when(testRequest.getMahkemeKararBilgisi()).thenReturn(createValidMahkemeKararBilgisi());
        when(testRequest.getKararTuru()).thenReturn(KararTuru.ILETISIMIN_TESPITI);

        //WHEN
        Long result = dbSaver.handleDbSave(testRequest, testDate, testUserId);

        //THEN
        assertValidEvrakId(result);
        assertThat(result)
                .isNotNull()
                .isEqualTo(expectedMahkemeKararTalep.getId())
                .as("MahkemeKararTalep ID doğru dönmeli");

        //VERIFY
        verify(utilService).getEvrakSiraNumarasi(anyString(), anyString());
        verify(kararRequestMapper).toEvrakKayit(any(), any(), any(), any(), any());
        verify(dbEvrakKayitService).save(any(EvrakKayit.class));
        verify(kararRequestMapper).toHTSMahkemeKararTalep(any(), any(), any(), any());
        verify(dbHtsMahkemeKararTalepService).save(requestedMahkemeKararTalep);

        //sadece iletisimini tespitinin cagrildigini verify et.
        verify(kararRequestMapper, never()).toMahkemeKararTalep(any(), anyLong(), anyLong(), any());
        verify(dbMahkemeKararTalepService, never()).save(any());

    }


    @ParameterizedTest
    @EnumSource(value = EvrakTuru.class, names = {"ILETISIMIN_DENETLENMESI", "GENEL_EVRAK"})
    void handleDbSave_ShouldSaveMahkemeTalep_WhenEvrakTuruIsILETISIMIN_DENETLENMESI_YENI_KARAR(EvrakTuru evrakTuru) {
        //GIVEN
        //talep = ILETISIMIN_DENETLENMESI
        //testRequest.getEvrakDetay().setEvrakTuru(EvrakTuru.ILETISIMIN_DENETLENMESI);

        MahkemeKararTalep requestedMahkemeKararTalep = new MahkemeKararTalep();

        Long expectedMahkemeKararTalepId = 4321L;
        MahkemeKararTalep expectedMahkemeKararTalep = new MahkemeKararTalep();
        expectedMahkemeKararTalep.setId(expectedMahkemeKararTalepId);

        String expectedEvrakSiraNo = "EVRAK-01";

        when(utilService.getEvrakSiraNumarasi(anyString(), anyString())).thenReturn(expectedEvrakSiraNo);
        EvrakKayit mockEvrakKayit = new EvrakKayit();
        mockEvrakKayit.setId(1234L);
        when(kararRequestMapper.toEvrakKayit(any(), any(), any(), any(), any())).thenReturn(mockEvrakKayit);
        when(dbEvrakKayitService.save(any())).thenReturn(mockEvrakKayit);
        when(kararRequestMapper.toMahkemeKararTalep(any(), any(), any(), any())).thenReturn(requestedMahkemeKararTalep);
        when(dbMahkemeKararTalepService.save(requestedMahkemeKararTalep)).thenReturn(expectedMahkemeKararTalep);

        // Set up evrak türü for the test
        EvrakDetay mockEvrakDetay = createValidEvrakDetay();
        mockEvrakDetay.setEvrakTuru(evrakTuru);
        when(testRequest.getEvrakDetay()).thenReturn(mockEvrakDetay);
        when(testRequest.getMahkemeKararBilgisi()).thenReturn(createValidMahkemeKararBilgisi());
        when(testRequest.getKararTuru()).thenReturn(KararTuru.ILETISIMIN_DENETLENMESI_YENI_KARAR);

        //WHEN
        Long result = dbSaver.handleDbSave(testRequest, testDate, testUserId);

        //THEN
        assertValidEvrakId(result);
        assertThat(result)
                .isNotNull()
                .isEqualTo(expectedMahkemeKararTalep.getId())
                .as("MahkemeKararTalep ID doğru dönmeli");


        //VERIFY
        verify(utilService).getEvrakSiraNumarasi(anyString(), anyString());
        verify(kararRequestMapper).toEvrakKayit(any(), any(), any(), any(), any());
        verify(dbEvrakKayitService).save(any());
        verify(kararRequestMapper).toMahkemeKararTalep(any(), any(), any(), any());
        verify(dbMahkemeKararTalepService).save(requestedMahkemeKararTalep);

        //iletisimin tespiti'nin cagrilmadigini verify et
        verify(kararRequestMapper, never()).toHTSMahkemeKararTalep(any(), any(), any(), any());
        verify(dbHtsMahkemeKararTalepService, never()).save(any());

        /*TODO:
         dbMahkemeKararTalepService.save(...) gerçekten çağrılmış mı ve doğru nesneyle çağrılmış mı?
        ArgumentCaptor<MahkemeKararTalep> captor = ArgumentCaptor.forClass(MahkemeKararTalep.class);
        verify(dbMahkemeKararTalepService).save(captor.capture());
        assertThat(captor.getValue()).isSameAs(requestedMahkemeKararTalep);
        * */
    }

    private EvrakDetay createValidEvrakDetay() {
        return EvrakDetay.builder()
                .evrakNo("TEST-EVRAK-001")
                .evrakTarihi(LocalDateTime.now())
                .evrakKurumKodu("TEST-KURUM")
                .evrakTuru(iym.common.enums.EvrakTuru.ILETISIMIN_DENETLENMESI)
                .geldigiIlIlceKodu("0601")
                .acilmi(false)
                .build();
    }

    private MahkemeKararBilgisi createValidMahkemeKararBilgisi() {
        return MahkemeKararBilgisi.builder()
                .mahkemeKararTipi(iym.common.enums.MahkemeKararTip.ADLI_HAKIM_KARARI)
                .mahkemeKararDetay(createValidMahkemeKararDetay())
                .build();
    }

    private iym.makos.model.api.MahkemeKararDetay createValidMahkemeKararDetay() {
        return iym.makos.model.api.MahkemeKararDetay.builder()
                .mahkemeKodu("TEST-MAHKEME")
                .mahkemeKararNo("TEST-KARAR-001")
                .mahkemeIlIlceKodu("0601")
                .sorusturmaNo("TEST-SORUSTURMA")
                .build();
    }

}

