package iym.common.service.db.mk;

import iym.common.model.entity.iym.mk.HedeflerAidiyat;
import iym.common.model.entity.iym.talep.HedeflerAidiyatTalep;
import iym.common.service.db.GenericDbService;

import java.util.List;
import java.util.Optional;

/**
 * Service interface for HedeflerAidiyat entity
 */
public interface DbHedeflerAidiyatService extends GenericDbService<HedeflerAidiyat, Long> {

    Optional<HedeflerAidiyat> findById(Long id);

   List<HedeflerAidiyat> findByHedeflerId(Long hedefId);


}
