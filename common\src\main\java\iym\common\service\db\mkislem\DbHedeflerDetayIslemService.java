package iym.common.service.db.mkislem;

import iym.common.model.entity.iym.mkislem.HedeflerDetayIslem;
import iym.common.service.db.GenericDbService;

import java.util.List;
import java.util.Optional;


public interface DbHedeflerDetayIslemService extends GenericDbService<HedeflerDetayIslem, Long> {
    //
    List<HedeflerDetayIslem> findByMahkemeKararIslemId(Long mahkemeKararIslemId);

    List<HedeflerDetayIslem> findByDetayMahkemeKararIslemId(Long detayMahkemeKararIslemId);

    Optional<HedeflerDetayIslem> findHedeflerDetayIslem(Long mahkemeKararIslemId, String hedefNo, Integer hedefTipi);

}
