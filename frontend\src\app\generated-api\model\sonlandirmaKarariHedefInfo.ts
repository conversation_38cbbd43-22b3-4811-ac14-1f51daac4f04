/**
 * IYM Backend OpenAPI definition
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { IDHedefAidiyatInfo } from './iDHedefAidiyatInfo';
import { DetayMahkemeKararInfo } from './detayMahkemeKararInfo';
import { IDHedefInfo } from './iDHedefInfo';


export interface SonlandirmaKarariHedefInfo { 
    hedefBilgisi?: IDHedefInfo;
    iliskiliMahkemeKararInfo?: DetayMahkemeKararInfo;
    kapatmaTarihi?: string;
    kapatmaMahkemeKararId?: number;
    baslamaTarihi?: string;
    hedefAidiyatListesi?: Array<IDHedefAidiyatInfo>;
}

