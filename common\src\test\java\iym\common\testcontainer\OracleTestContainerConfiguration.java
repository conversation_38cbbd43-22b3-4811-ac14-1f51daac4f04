package iym.common.testcontainer;

import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.h2.H2ConsoleAutoConfiguration;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Profile;

/**
 * Test configuration for Oracle TestContainer integration tests.
 * <p>
 * This configuration ensures that Spring Boot uses Oracle TestContainer
 * instead of embedded databases (H2, HSQLDB, Derby) for tests.
 * <p>
 * Usage:
 * - Import this configuration in tests extending AbstractOracleTestContainer
 * - Use @ActiveProfiles("testcontainers-oracle") to activate Oracle test profile
 * <p>
 * Features:
 * - Excludes DataSource auto-configuration to prevent H2/Oracle conflicts
 * - Allows Oracle TestContainer configuration to take precedence via @DynamicPropertySource
 * - Prevents embedded database detection conflicts
 * <p>
 * Example usage:
 * <pre>
 * {@code
 * @SpringBootTest
 * @Import(OracleTestContainerConfiguration.class)
 * @ActiveProfiles("testcontainers-oracle")
 * public class MyOracleTest extends AbstractOracleTestContainer {
 *     // Test implementation
 * }
 * }
 * </pre>
 * <p>
 * Alternative approach: Use properties-based exclusion in application-testcontainers-oracle.properties:
 * <pre>
 * spring.datasource.embedded-database-connection=none
 * spring.test.database.replace=none
 * </pre>
 *
 * <AUTHOR> Team
 */
@TestConfiguration
@Profile("testcontainers-oracle")
@EnableAutoConfiguration(exclude = {H2ConsoleAutoConfiguration.class})
public class OracleTestContainerConfiguration {

}
