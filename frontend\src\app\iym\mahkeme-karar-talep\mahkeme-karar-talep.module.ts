import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';

// PrimeNG Modules
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { CheckboxModule } from 'primeng/checkbox';
import { DatePickerModule } from 'primeng/datepicker';
import { FileUploadModule } from 'primeng/fileupload';
import { InputTextModule } from 'primeng/inputtext';
import { MultiSelectModule } from 'primeng/multiselect';
import { SelectModule } from 'primeng/select';
import { TabViewModule } from 'primeng/tabview';
import { TextareaModule } from 'primeng/textarea';
import { ToastModule } from 'primeng/toast';

// Component
import { MahkemeKararTalepComponent } from './mahkeme-karar-talep.component';

@NgModule({
  declarations: [
    MahkemeKararTalepComponent
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    
    // PrimeNG Modules
    ButtonModule,
    CardModule,
    CheckboxModule,
    DatePickerModule,
    FileUploadModule,
    InputTextModule,
    MultiSelectModule,
    SelectModule,
    TabViewModule,
    TextareaModule,
    ToastModule
  ],
  exports: [
    MahkemeKararTalepComponent
  ]
})
export class MahkemeKararTalepModule { }