-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create sequence for HEDEFLER_AIDIYAT_TALEP if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = 'HED_AID_TALEP_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.HED_AID_TALEP_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

--todo : byte -> null
-- Create HEDEFLER_AIDIYAT_TALEP table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'HEDEFLER_AIDIYAT_TALEP';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.HEDEFLER_AIDIYAT_TALEP (
      ID NUMBER NOT NULL,
      HEDEF_ID NUMBER NOT NULL,
      AIDIYAT_KOD VARCHAR2(15) NOT NULL,
      TARIH DATE NOT NULL,
      KULLANICI_ID NUMBER NOT NULL,
      DURUMU VARCHAR2(15),
      CONSTRAINT HEDEFLER_AIDIYAT_TALEP_ID_IDX PRIMARY KEY (ID) ENABLE
    )';
  END IF;
END;
/

-- Add unique constraint if it doesn't exist
DECLARE
  constraint_count NUMBER;
BEGIN
  SELECT COUNT(*) INTO constraint_count FROM user_constraints
  WHERE constraint_name = 'HED_AID_TALEP_U_IDX' AND table_name = 'HEDEFLER_AIDIYAT_TALEP';

  IF constraint_count = 0 THEN
    EXECUTE IMMEDIATE 'ALTER TABLE iym.HEDEFLER_AIDIYAT_TALEP
    ADD CONSTRAINT HED_AID_TALEP_U_IDX UNIQUE (HEDEF_ID, AIDIYAT_KOD) ENABLE';
  END IF;
END;
/

-- Insert sample data if table is empty
DECLARE
  row_count NUMBER;
BEGIN
  SELECT COUNT(*) INTO row_count FROM iym.HEDEFLER_AIDIYAT_TALEP;
  IF row_count = 0 THEN
    -- Make sure we have hedefler_talep records
    DECLARE
      hedef_count NUMBER;
    BEGIN
      SELECT COUNT(*) INTO hedef_count FROM iym.HEDEFLER_TALEP;

      IF hedef_count > 0 THEN
        -- Get the hedefler_talep records
        FOR hedef_rec IN (
          SELECT h.ID as hedef_id, h.KULLANICI_ID
          FROM iym.HEDEFLER_TALEP h
        ) LOOP
          -- Sample data - Hedef aidiyat kaydı
          INSERT INTO iym.HEDEFLER_AIDIYAT_TALEP (
            ID, HEDEF_ID, AIDIYAT_KOD, TARIH, KULLANICI_ID, DURUMU
          ) VALUES (
            iym.HED_AID_TALEP_SEQ.NEXTVAL, hedef_rec.hedef_id, 'AIDIYAT1',
            SYSDATE, hedef_rec.KULLANICI_ID, 'AKTIF'
          );
        END LOOP;
      END IF;
    END;
  END IF;
END;
/

COMMIT;
