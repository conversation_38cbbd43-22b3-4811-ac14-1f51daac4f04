package iym.common.model.entity.iym;

import jakarta.persistence.*;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Entity class for EVRAK_KAYIT table
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "MahkemeBilgi")
@Table(name = "MAHKEME_ADI")
public class MahkemeBilgi implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "MAHKEME_KODU")
    private String mahkemeKodu;

    @Column(name = "MAHKEME_IL_ILCE")
    private String ilIlceKodu;

    @Column(name = "MAHKEME_TURU")
    private String mahkemeTuruKodu;

    @Column(name = "MAHKEME_SAYI")
    private String mahkemeSayi;

    @Column(name = "MAHKEME_ADI")
    private String mahkemeAdi;

    @Column(name = "EKLEME_TARIHI")
    private LocalDateTime eklemeTarihi;


    @Column(name = "EKLEYEN_ID")
    private Long ekleyenKullaniciId;

    @Column(name = "SILINDI")
    private String silindi;

}
