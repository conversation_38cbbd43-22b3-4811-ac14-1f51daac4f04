package iym.db.jpa.dao.mk;

import iym.common.model.entity.iym.mk.DetayMahkemeKarar;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Repository interface for DMahkemeKarar entity
 */
@Repository
public interface MahkemeKararDetayRepo extends JpaRepository<DetayMahkemeKarar, Long> {

    List<DetayMahkemeKarar> findByEvrakId(Long evrakId);

    List<DetayMahkemeKarar> findByMahkemeKararId(Long mahkemeKararId);


}
