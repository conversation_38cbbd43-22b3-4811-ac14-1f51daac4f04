package iym.db.jpa.service.impl.mkislem;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.mkislem.MahkemeKararGuncellemeIslem;
import iym.common.service.db.mkislem.DbMahkemeKararGuncelleIslemService;
import iym.db.jpa.dao.mkislem.MahkemeKararGuncelleIslemRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;


@Service
public class DbMahkemeKararGuncelleIslemServiceImpl extends GenericDbServiceImpl<MahkemeKararGuncellemeIslem, Long> implements DbMahkemeKararGuncelleIslemService {

    private final MahkemeKararGuncelleIslemRepo mahkemeKararGuncelleIslemRepo;

    @Autowired
    public DbMahkemeKararGuncelleIslemServiceImpl(MahkemeKararGuncelleIslemRepo repository) {
        super(repository);
        this.mahkemeKararGuncelleIslemRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<MahkemeKararGuncellemeIslem> findByDetayMahkemeKararIslemId(Long detayMahkemeKararIslemId) {
        return mahkemeKararGuncelleIslemRepo.findByDetayMahkemeKararIslemId(detayMahkemeKararIslemId);
    }

}
