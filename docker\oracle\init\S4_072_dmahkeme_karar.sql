-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create sequence for DMAHKEME_KARAR_SEQ if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = 'DMAHKEME_KARAR_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.DMAHKEME_KARAR_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

-- Create DMAHKEME_KARAR table if it doesn't exist

--TODO : canliya Primary key eklenecek - PK_MAHKEME_KARAR_ATAMA
--TODO : varchar daki byte lar kaldirilacak

DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'DMAH<PERSON>ME_KARAR';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.DMAHKEME_KARAR (
          MAHKEME_KARAR_ID NUMBER NOT NULL
        , EVRAK_ID NUMBER NOT NULL
        , KULLANICI_ID NUMBER NOT NULL
        , KAYIT_TARIHI DATE NOT NULL
        , DURUM VARCHAR2(20 )
        , KARAR_TIP_DETAY VARCHAR2(20)
        , MAHKEME_KODU_DETAY VARCHAR2(10)
        , MAHKEME_ADI_DETAY VARCHAR2(250)
        , MAHKEME_KARAR_NO_DETAY VARCHAR2(50)
        , MAHKEME_ILI_DETAY VARCHAR2(6)
        , SORUSTURMA_NO_DETAY VARCHAR2(50)
        , ILISKILI_MAHKEME_KARAR_ID NUMBER
        , ACIKLAMA_DETAY VARCHAR2(500)
        , ID NUMBER
        , CONSTRAINT PK_DMAHKEME_KARAR PRIMARY KEY (ID) ENABLE
    )';
    

  END IF;
END;
/



COMMIT;
