package iym.db.jpa.service.impl.mk;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.mk.DetayMahkemeKarar;
import iym.common.service.db.mk.DbDetayMahkemeKararService;
import iym.db.jpa.dao.mk.DetayMahkemeKararRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class DbDetayMahkemeKararServiceImp extends GenericDbServiceImpl<DetayMahkemeKarar, Long> implements DbDetayMahkemeKararService {

    private final DetayMahkemeKararRepo detayMahkemeKararRepo;

    @Autowired
    public DbDetayMahkemeKararServiceImp(DetayMahkemeKararRepo repository) {
        super(repository);
        this.detayMahkemeKararRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public List<DetayMahkemeKarar> findByEvrakId(Long evrakId) {
        return detayMahkemeKararRepo.findByEvrakId(evrakId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<DetayMahkemeKarar> findByMahkemeKararId(Long mahkemeKararTalepId) {
        return detayMahkemeKararRepo.findByMahkemeKararId(mahkemeKararTalepId);
    }

}
