package iym.db.jpa.dao.mkislem;

import iym.common.model.entity.iym.mkislem.MahkemeAidiyatDetayIslem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface MahkemeAidiyatDetayIslemRepo extends JpaRepository<MahkemeAidiyatDetayIslem, Long> {

    List<MahkemeAidiyatDetayIslem> findByMahkemeKararIslemId(Long mahkemeKararIslemId);

    List<MahkemeAidiyatDetayIslem> findByDetayMahkemeKararIslemId(Long detayMahkemeKararIslemId);


}
