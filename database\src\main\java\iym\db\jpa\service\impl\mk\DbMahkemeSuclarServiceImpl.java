package iym.db.jpa.service.impl.mk;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.mk.MahkemeSuclar;
import iym.common.service.db.mk.DbMahkemeSuclarService;
import iym.db.jpa.dao.mk.MahkemeSuclarRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * Service implementation for MahkemeKararSuc entity
 */
@Service
public class DbMahkemeSuclarServiceImpl extends GenericDbServiceImpl<MahkemeSuclar, Long> implements DbMahkemeSuclarService {

    private final MahkemeSuclarRepo mahkemeSuclarRepo;

    @Autowired
    public DbMahkemeSuclarServiceImpl(MahkemeSuclarRepo repository) {
        super(repository);
        this.mahkemeSuclarRepo = repository;
    }

    @Override
    public List<MahkemeSuclar> findByMahkemeKararId(Long mahkemeKararId){
        return mahkemeSuclarRepo.findByMahkemeKararId(mahkemeKararId);
    }

    @Override
    public Optional<MahkemeSuclar> findByMahkemeKararIdAndSucTipKodu(Long mahkemeKararId, String sucTipKodu){
        return mahkemeSuclarRepo.findByMahkemeKararIdAndSucTipKodu(mahkemeKararId, sucTipKodu);
    }

}
