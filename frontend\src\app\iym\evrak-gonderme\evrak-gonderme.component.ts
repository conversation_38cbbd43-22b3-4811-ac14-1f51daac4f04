import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

// PrimeNG Imports
import { FileUploadModule } from 'primeng/fileupload';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { ProgressBarModule } from 'primeng/progressbar';
import { ToastModule } from 'primeng/toast';
import { MessagesModule } from 'primeng/messages';
import { MessageModule } from 'primeng/message';
import { DividerModule } from 'primeng/divider';
import { TagModule } from 'primeng/tag';
import { DialogModule } from 'primeng/dialog';
import { TableModule } from 'primeng/table';
import { SelectModule } from 'primeng/select';
import { InputTextarea } from 'primeng/inputtextarea';

import { MessageService } from 'primeng/api';

// IYM Imports
import { IymService } from '../shared/services/iym.service';
import { DosyaYuklemeResponse, XmlValidasyonSonucu } from '../shared/models/iym.models';

interface YuklenenDosya {
  dosya: File;
  durum: 'yukleniyor' | 'basarili' | 'hata' | 'validasyon-bekliyor';
  ilerleme: number;
  sonuc?: DosyaYuklemeResponse;
  hataMesaji?: string;
  validasyonSonucu?: XmlValidasyonSonucu;
}

@Component({
  selector: 'app-evrak-gonderme',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    FileUploadModule,
    ButtonModule,
    CardModule,
    ProgressBarModule,
    ToastModule,
    MessagesModule,
    MessageModule,
    DividerModule,
    TagModule,
    DialogModule,
    TableModule,
    SelectModule,
    InputTextarea
  ],
  providers: [MessageService],
  templateUrl: './evrak-gonderme.component.html',
  styleUrls: ['./evrak-gonderme.component.scss']
})
export class EvrakGondermeComponent implements OnInit {
  
  // Dosya yükleme durumları
  yuklenenDosyalar: YuklenenDosya[] = [];
  toplamIlerleme = 0;
  aktifYukleme = false;
  
  // Validasyon dialog
  validasyonDialogGoruntule = false;
  seciliValidasyonSonucu: XmlValidasyonSonucu | null = null;
  
  // Evrak tipi seçenekleri
  evrakTipleri = [
    { label: 'İletişimin Denetlenmesi', value: 'ILETISIMIN_DENETLENMESI' },
    { label: 'İletişimin Tespiti', value: 'ILETISIMIN_TESPITI' }
  ];
  
  seciliEvrakTipi = 'ILETISIMIN_DENETLENMESI';
  aciklama = '';
  
  // Dosya kısıtlamaları
  maksimumDosyaBoyutu = 10485760; // 10MB
  kabulEdilenDosyaTipleri = '.xml';

  constructor(
    private iymService: IymService,
    private messageService: MessageService
  ) {}

  ngOnInit() {
    // Component başlatma işlemleri
  }

  onDosyaSecildi(event: any) {
    const dosyalar: FileList = event.files;
    
    for (let i = 0; i < dosyalar.length; i++) {
      const dosya = dosyalar[i];
      
      // Dosya boyutu kontrolü
      if (dosya.size > this.maksimumDosyaBoyutu) {
        this.messageService.add({
          severity: 'error',
          summary: 'Hata',
          detail: `${dosya.name} dosyası çok büyük. Maksimum ${this.maksimumDosyaBoyutu / 1024 / 1024}MB olabilir.`
        });
        continue;
      }
      
      // Dosya tipi kontrolü
      if (!dosya.name.toLowerCase().endsWith('.xml')) {
        this.messageService.add({
          severity: 'error',
          summary: 'Hata',
          detail: `${dosya.name} dosyası geçersiz. Sadece XML dosyaları kabul edilir.`
        });
        continue;
      }
      
      const yuklenenDosya: YuklenenDosya = {
        dosya: dosya,
        durum: 'yukleniyor',
        ilerleme: 0
      };
      
      this.yuklenenDosyalar.push(yuklenenDosya);
      this.dosyaYukle(yuklenenDosya);
    }
  }

  dosyaYukle(yuklenenDosya: YuklenenDosya) {
    this.aktifYukleme = true;
    
    // İlerleme simülasyonu
    const ilerlemeSim = setInterval(() => {
      yuklenenDosya.ilerleme += Math.random() * 20;
      if (yuklenenDosya.ilerleme >= 100) {
        yuklenenDosya.ilerleme = 100;
        clearInterval(ilerlemeSim);
        this.dosyaYuklemeniniBitir(yuklenenDosya);
      }
      this.toplamIlerlemeyiHesapla();
    }, 200);
  }

  dosyaYuklemeniniBitir(yuklenenDosya: YuklenenDosya) {
    this.iymService.evrakGonder(yuklenenDosya.dosya).subscribe({
      next: (sonuc) => {
        yuklenenDosya.sonuc = sonuc;
        yuklenenDosya.durum = sonuc.basarili ? 'basarili' : 'hata';
        
        if (sonuc.validasyonSonucu) {
          yuklenenDosya.validasyonSonucu = sonuc.validasyonSonucu;
          if (!sonuc.validasyonSonucu.gecerliMi) {
            yuklenenDosya.durum = 'hata';
          }
        }
        
        this.messageService.add({
          severity: sonuc.basarili ? 'success' : 'error',
          summary: sonuc.basarili ? 'Başarılı' : 'Hata',
          detail: sonuc.basarili ? 
            `${yuklenenDosya.dosya.name} başarıyla yüklendi` : 
            sonuc.hataMesaji || 'Dosya yükleme başarısız'
        });
        
        this.aktifYuklemeKontrol();
      },
      error: (hata) => {
        yuklenenDosya.durum = 'hata';
        yuklenenDosya.hataMesaji = 'Dosya yükleme sırasında hata oluştu';
        
        this.messageService.add({
          severity: 'error',
          summary: 'Hata',
          detail: `${yuklenenDosya.dosya.name} yüklenemedi`
        });
        
        this.aktifYuklemeKontrol();
      }
    });
  }

  aktifYuklemeKontrol() {
    const yuklenenVarMi = this.yuklenenDosyalar.some(d => d.durum === 'yukleniyor');
    this.aktifYukleme = yuklenenVarMi;
    
    if (!yuklenenVarMi) {
      this.toplamIlerleme = 0;
    }
  }

  toplamIlerlemeyiHesapla() {
    if (this.yuklenenDosyalar.length === 0) {
      this.toplamIlerleme = 0;
      return;
    }
    
    const toplamIlerleme = this.yuklenenDosyalar.reduce((toplam, dosya) => toplam + dosya.ilerleme, 0);
    this.toplamIlerleme = Math.round(toplamIlerleme / this.yuklenenDosyalar.length);
  }

  dosyaSil(index: number) {
    this.yuklenenDosyalar.splice(index, 1);
    this.toplamIlerlemeyiHesapla();
    this.aktifYuklemeKontrol();
  }

  tumDosyalariSil() {
    this.yuklenenDosyalar = [];
    this.toplamIlerleme = 0;
    this.aktifYukleme = false;
  }

  validasyonDetayGoster(validasyonSonucu: XmlValidasyonSonucu) {
    this.seciliValidasyonSonucu = validasyonSonucu;
    this.validasyonDialogGoruntule = true;
  }

  validasyonDialogKapat() {
    this.validasyonDialogGoruntule = false;
    this.seciliValidasyonSonucu = null;
  }

  durumSeviyesiGetir(durum: string): 'success' | 'info' | 'warn' | 'danger' | 'secondary' | 'contrast' {
    switch (durum) {
      case 'basarili':
        return 'success';
      case 'hata':
        return 'danger';
      case 'yukleniyor':
        return 'info';
      default:
        return 'info';
    }
  }

  // Getter methods for template expressions
  get basariliDosyaSayisi(): number {
    return this.yuklenenDosyalar.filter(d => d.durum === 'basarili').length;
  }

  get hataliDosyaSayisi(): number {
    return this.yuklenenDosyalar.filter(d => d.durum === 'hata').length;
  }

  get yuklenenDosyaSayisi(): number {
    return this.yuklenenDosyalar.filter(d => d.durum === 'yukleniyor').length;
  }

  durumMetniGetir(durum: string): string {
    switch (durum) {
      case 'basarili':
        return 'Başarılı';
      case 'hata':
        return 'Hatalı';
      case 'yukleniyor':
        return 'Yükleniyor';
      default:
        return 'Bilinmiyor';
    }
  }

  dosyaBoyutuFormat(boyut: number): string {
    if (boyut < 1024) {
      return boyut + ' B';
    } else if (boyut < 1024 * 1024) {
      return Math.round(boyut / 1024) + ' KB';
    } else {
      return Math.round(boyut / (1024 * 1024)) + ' MB';
    }
  }

  yenidenDene(yuklenenDosya: YuklenenDosya) {
    yuklenenDosya.durum = 'yukleniyor';
    yuklenenDosya.ilerleme = 0;
    yuklenenDosya.hataMesaji = undefined;
    yuklenenDosya.sonuc = undefined;
    yuklenenDosya.validasyonSonucu = undefined;
    
    this.dosyaYukle(yuklenenDosya);
  }

  xmlOnizleme(dosya: File) {
    const reader = new FileReader();
    reader.onload = (e) => {
      const xmlIcerik = e.target?.result as string;

      // XML önizleme dialog'u açılabilir
      this.messageService.add({
        severity: 'info',
        summary: 'Bilgi',
        detail: 'XML önizleme özelliği yakında eklenecek'
      });
    };
    reader.readAsText(dosya);
  }

  // Toplu işlemler
  basariliDosyalariSil() {
    this.yuklenenDosyalar = this.yuklenenDosyalar.filter(d => d.durum !== 'basarili');
    this.toplamIlerlemeyiHesapla();
  }

  hataliDosyalariSil() {
    this.yuklenenDosyalar = this.yuklenenDosyalar.filter(d => d.durum !== 'hata');
    this.toplamIlerlemeyiHesapla();
  }

  get basariliDosyaYok(): boolean {
    return this.yuklenenDosyalar.filter(d => d.durum === 'basarili').length === 0;
  }

  get hataliDosyaYok(): boolean {
    return this.yuklenenDosyalar.filter(d => d.durum === 'hata').length === 0;
  }

  tumHatalariYenidenDene() {
    const hatalıDosyalar = this.yuklenenDosyalar.filter(d => d.durum === 'hata');
    hatalıDosyalar.forEach(dosya => this.yenidenDene(dosya));
  }
}
