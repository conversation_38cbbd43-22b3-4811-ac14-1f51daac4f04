package iym.db.jpa.dao.mkislem;

import iym.common.model.entity.iym.mkislem.MahkemeSucTipiDetayIslem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface MahkemeSucTipiDetayIslemRepo extends JpaRepository<MahkemeSucTipiDetayIslem, Long> {

    List<MahkemeSucTipiDetayIslem> findByMahkemeKararDetayIslemId(Long mahkemeKararDetayIslemId);

    

}
