import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { MahkemeKararDetaylariComponent } from './mahkeme-karar-detaylari.component';

describe('MahkemeKararDetaylariComponent', () => {
  let component: MahkemeKararDetaylariComponent;
  let fixture: ComponentFixture<MahkemeKararDetaylariComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        MahkemeKararDetaylariComponent,
        ReactiveFormsModule
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(MahkemeKararDetaylariComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with empty values', () => {
    expect(component.form.get('mahkemeKodu')?.value).toBe('');
    expect(component.form.get('mahkemeIlIlceKodu')?.value).toBe('');
    expect(component.form.get('mahkemeKararNo')?.value).toBe('');
    expect(component.form.get('sorusturmaNo')?.value).toBe('');
    expect(component.form.get('aciklama')?.value).toBe('');
  });

  it('should validate required fields', () => {
    const mahkemeKoduControl = component.form.get('mahkemeKodu');
    const mahkemeIlIlceKoduControl = component.form.get('mahkemeIlIlceKodu');

    expect(mahkemeKoduControl?.hasError('required')).toBeTruthy();
    expect(mahkemeIlIlceKoduControl?.hasError('required')).toBeTruthy();

    mahkemeKoduControl?.setValue('TEST001');
    mahkemeIlIlceKoduControl?.setValue('0600');

    expect(mahkemeKoduControl?.hasError('required')).toBeFalsy();
    expect(mahkemeIlIlceKoduControl?.hasError('required')).toBeFalsy();
  });

  it('should write value correctly', () => {
    const testValue = {
      mahkemeKodu: 'TEST001',
      mahkemeIlIlceKodu: '0600',
      mahkemeKararNo: '2023/123',
      sorusturmaNo: '2023/456',
      aciklama: 'Test açıklama'
    };

    component.writeValue(testValue);

    expect(component.form.get('mahkemeKodu')?.value).toBe('TEST001');
    expect(component.form.get('mahkemeIlIlceKodu')?.value).toBe('0600');
    expect(component.form.get('mahkemeKararNo')?.value).toBe('2023/123');
    expect(component.form.get('sorusturmaNo')?.value).toBe('2023/456');
    expect(component.form.get('aciklama')?.value).toBe('Test açıklama');
  });

  it('should return valid value when form is valid', () => {
    component.form.patchValue({
      mahkemeKodu: 'TEST001',
      mahkemeIlIlceKodu: '0600',
      mahkemeKararNo: '2023/123',
      sorusturmaNo: '2023/456',
      aciklama: 'Test açıklama'
    });

    const value = component.getValue();
    expect(value).toBeTruthy();
    expect(value?.mahkemeKodu).toBe('TEST001');
    expect(value?.mahkemeIlIlceKodu).toBe('0600');
  });

  it('should return null when form is invalid', () => {
    // Form is invalid because required fields are empty
    const value = component.getValue();
    expect(value).toBeNull();
  });

  it('should disable form when setDisabledState is called', () => {
    component.setDisabledState(true);
    expect(component.form.disabled).toBeTruthy();

    component.setDisabledState(false);
    expect(component.form.disabled).toBeFalsy();
  });
});
