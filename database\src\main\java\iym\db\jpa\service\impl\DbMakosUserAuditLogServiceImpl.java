package iym.db.jpa.service.impl;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.enums.IletisimTespitiKararTuru;
import iym.common.enums.MakosUserAuditType;
import iym.common.model.entity.makos.MakosUserAuditLog;
import iym.common.service.db.DbMakosUserAuditLogService;
import iym.db.jpa.dao.MakosUserAuditLogRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Service implementation for MakosUserAuditLog entity
 * Provides business logic methods for MAKOS user audit logging
 */
@Service
public class DbMakosUserAuditLogServiceImpl extends GenericDbServiceImpl<MakosUserAuditLog, Long> implements DbMakosUserAuditLogService {

    private final MakosUserAuditLogRepo makosUserAuditLogRepo;

    @Autowired
    public DbMakosUserAuditLogServiceImpl(MakosUserAuditLogRepo repository) {
        super(repository);
        this.makosUserAuditLogRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public List<MakosUserAuditLog> findByUserAuditType(MakosUserAuditType userAuditType) {
        return makosUserAuditLogRepo.findByUserAuditType(userAuditType);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MakosUserAuditLog> findByUsername(String username) {
        return makosUserAuditLogRepo.findByUsername(username);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MakosUserAuditLog> findByActingUsername(String actingUsername) {
        return makosUserAuditLogRepo.findByActingUsername(actingUsername);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MakosUserAuditLog> findByAdminOperatedUsername(String adminOperatedUsername) {
        return makosUserAuditLogRepo.findByAdminOperatedUsername(adminOperatedUsername);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MakosUserAuditLog> findByUserIp(String userIp) {
        return makosUserAuditLogRepo.findByUserIp(userIp);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MakosUserAuditLog> findByRequestTimeBetween(LocalDateTime startTime, LocalDateTime endTime) {
        return makosUserAuditLogRepo.findByRequestTimeBetween(startTime, endTime);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MakosUserAuditLog> findByUsernameAndUserAuditType(String username, MakosUserAuditType userAuditType) {
        return makosUserAuditLogRepo.findByUsernameAndUserAuditType(username, userAuditType);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MakosUserAuditLog> findByUsernameAndRequestTimeBetween(String username, LocalDateTime startTime, LocalDateTime endTime) {
        return makosUserAuditLogRepo.findByUsernameAndRequestTimeBetween(username, startTime, endTime);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MakosUserAuditLog> findAllByOrderByRequestTimeDesc() {
        return makosUserAuditLogRepo.findAllByOrderByRequestTimeDesc();
    }

    @Override
    @Transactional(readOnly = true)
    public List<MakosUserAuditLog> findByUsernameOrderByRequestTimeDesc(String username) {
        return makosUserAuditLogRepo.findByUsernameOrderByRequestTimeDesc(username);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<MakosUserAuditLog> findAll(Specification<MakosUserAuditLog> spec, Pageable pageable) {
        return makosUserAuditLogRepo.findAll(spec, pageable);
    }
}
