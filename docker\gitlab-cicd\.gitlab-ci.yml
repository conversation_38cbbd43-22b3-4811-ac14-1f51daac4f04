# Example .gitlab-ci.yml for IYM project
# Place this file in the root of your repository and rename it to .gitlab-ci.yml

stages:
  - build
  - test
  - docker
  - deploy

variables:
  MAVEN_OPTS: "-Dmaven.repo.local=.m2/repository -Xmx1024m"
  JAVA_VERSION: "17"

cache:
  paths:
    - .m2/repository
    - frontend/node_modules

# Backend Build Job
build-backend:
  stage: build
  image: maven:3.9.6-eclipse-temurin-17
  script:
    - mvn clean install -DskipTests
  artifacts:
    paths:
      - backend/target/*.jar
      - makos/target/*.jar
    expire_in: 1 day

# Frontend Build Job
build-frontend:
  stage: build
  image: node:18
  script:
    - cd frontend
    - npm install
    - npm run build --prod
  artifacts:
    paths:
      - frontend/dist
    expire_in: 1 day

# Unit Tests Job
test-backend:
  stage: test
  image: maven:3.9.6-eclipse-temurin-17
  script:
    - mvn test -Dspring.profiles.active=test -Dtest="!*IntegrationTest" -DfailIfNoTests=false
  artifacts:
    paths:
      - "**/target/surefire-reports/*.xml"
    reports:
      junit: "**/target/surefire-reports/*.xml"
  dependencies:
    - build-backend

# Backend Docker Build Job
docker-build-backend:
  stage: docker
  image: docker:20
  services:
    - docker:dind
  script:
    - export VERSION=$(mvn help:evaluate -Dexpression=project.version -q -DforceStdout)
    - docker build -t localhost:5050/iym/backend:$VERSION -f backend/Dockerfile .
    - docker login -u gitlab-ci-token -p $CI_JOB_TOKEN localhost:5050
    - docker push localhost:5050/iym/backend:$VERSION
    # Tag with branch name for easier reference
    - docker tag localhost:5050/iym/backend:$VERSION localhost:5050/iym/backend:$CI_COMMIT_REF_SLUG
    - docker push localhost:5050/iym/backend:$CI_COMMIT_REF_SLUG
  dependencies:
    - build-backend
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
    - if: $CI_COMMIT_BRANCH == "develop"
    - if: $CI_COMMIT_TAG

# Makos Docker Build Job
docker-build-makos:
  stage: docker
  image: docker:20
  services:
    - docker:dind
  script:
    - export VERSION=$(mvn help:evaluate -Dexpression=project.version -q -DforceStdout)
    - docker build -t localhost:5050/iym/makos:$VERSION -f makos/Dockerfile .
    - docker login -u gitlab-ci-token -p $CI_JOB_TOKEN localhost:5050
    - docker push localhost:5050/iym/makos:$VERSION
    # Tag with branch name for easier reference
    - docker tag localhost:5050/iym/makos:$VERSION localhost:5050/iym/makos:$CI_COMMIT_REF_SLUG
    - docker push localhost:5050/iym/makos:$CI_COMMIT_REF_SLUG
  dependencies:
    - build-backend
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
    - if: $CI_COMMIT_BRANCH == "develop"
    - if: $CI_COMMIT_TAG

# Frontend Docker Build Job
docker-build-frontend:
  stage: docker
  image: docker:20
  services:
    - docker:dind
  script:
    - export VERSION=$(mvn help:evaluate -Dexpression=project.version -q -DforceStdout)
    - docker build -t localhost:5050/iym/frontend:$VERSION -f frontend/Dockerfile frontend/
    - docker login -u gitlab-ci-token -p $CI_JOB_TOKEN localhost:5050
    - docker push localhost:5050/iym/frontend:$VERSION
    # Tag with branch name for easier reference
    - docker tag localhost:5050/iym/frontend:$VERSION localhost:5050/iym/frontend:$CI_COMMIT_REF_SLUG
    - docker push localhost:5050/iym/frontend:$CI_COMMIT_REF_SLUG
  dependencies:
    - build-frontend
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
    - if: $CI_COMMIT_BRANCH == "develop"
    - if: $CI_COMMIT_TAG

# Deploy to Development Environment
deploy-dev:
  stage: deploy
  image: docker:20
  services:
    - docker:dind
  script:
    - echo "Deploying to development environment..."
    - docker-compose -f docker/docker-compose.yml pull
    - docker-compose -f docker/docker-compose.yml up -d
  environment:
    name: development
    url: http://localhost:8080
  rules:
    - if: $CI_COMMIT_BRANCH == "develop"
      when: manual

# Deploy to Production Environment
deploy-prod:
  stage: deploy
  image: docker:20
  services:
    - docker:dind
  script:
    - echo "Deploying to production environment..."
    - docker-compose -f docker/docker-compose.yml pull
    - docker-compose -f docker/docker-compose.yml up -d
  environment:
    name: production
    url: http://localhost:8080
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
      when: manual
    - if: $CI_COMMIT_TAG
      when: manual