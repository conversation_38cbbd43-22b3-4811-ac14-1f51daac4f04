package iym.db.jpa.service.impl.mk;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.mk.MahkemeAidiyat;
import iym.common.service.db.mk.DbMahkemeAidiyatService;
import iym.db.jpa.dao.mk.MahkemeAidiyatRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Service implementation for MahkemeAidiyatTalep entity
 */
@Service
public class DbMahkemeAidiyatServiceImpl extends GenericDbServiceImpl<MahkemeAidiyat, Long> implements DbMahkemeAidiyatService {

    private final MahkemeAidiyatRepo mahkemeAidiyatRepo;

    @Autowired
    public DbMahkemeAidiyatServiceImpl(MahkemeAidiyatRepo repository) {
        super(repository);
        this.mahkemeAidiyatRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public List<MahkemeAidiyat> findByMahkemeKararId(Long mahkemeKararId) {
        return mahkemeAidiyatRepo.findByMahkemeKararId(mahkemeKararId);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<MahkemeAidiyat> findById(Long id){
        return mahkemeAidiyatRepo.findById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<MahkemeAidiyat> findByMahkemeKararIdAndAidiyatKod(Long mahkemeKararTalepId, String aidiyatKodu){
        return mahkemeAidiyatRepo.findByMahkemeKararIdAndAidiyatKod(mahkemeKararTalepId, aidiyatKodu);
    }

}
