# PrimeNG v19 Migration Guide: Replacing Deprecated Components

PrimeNG v19 introduces new component names to align with modern UI standards. Several older components are now **deprecated** but still functional. This guide will help you migrate deprecated elements to their new replacements.

---

## 🔄 Deprecated → New Component Mapping

| Deprecated Component (Old) | New Component (Replacement) |
|-----------------------------|-----------------------------|
| `<p-dropdown>` / **Dropdown** | `<p-select>` / **Select** |
| `<p-calendar>` / **Calendar** | `<p-datepicker>` / **DatePicker** |
| `<p-inputswitch>` / **InputSwitch** | `<p-toggleswitch>` / **ToggleSwitch** |
| `<p-inputmask>` / **InputMask** | `<p-mask>` / **Mask** |
| `<p-chips>` / **Chips** | `<p-multiselectchips>` / **MultiSelectChips** |
| `<p-listbox>` / **Listbox** | `<p-selectlist>` / **SelectList** |
| `<p-orderlist>` / **OrderList** | `<p-transfer>` / **Transfer** |

---

## 📝 Example Replacements

### 1. Dropdown → Select
**Before (deprecated):**
```html
<p-dropdown [options]="cities" [(ngModel)]="selectedCity"></p-dropdown>
```

**After (new):**
```html
<p-select [options]="cities" [(ngModel)]="selectedCity"></p-select>
```

---

### 2. Calendar → DatePicker
**Before (deprecated):**
```html
<p-calendar [(ngModel)]="date"></p-calendar>
```

**After (new):**
```html
<p-datepicker [(ngModel)]="date"></p-datepicker>
```

---

### 3. InputSwitch → ToggleSwitch
**Before (deprecated):**
```html
<p-inputswitch [(ngModel)]="checked"></p-inputswitch>
```

**After (new):**
```html
<p-toggleswitch [(ngModel)]="checked"></p-toggleswitch>
```

---

### 4. InputMask → Mask
**Before (deprecated):**
```html
<p-inputmask mask="99/99/9999" [(ngModel)]="value"></p-inputmask>
```

**After (new):**
```html
<p-mask mask="99/99/9999" [(ngModel)]="value"></p-mask>
```

---

## ✅ Migration Tips
- All **deprecated components still work** in PrimeNG 19, but support may be dropped in future versions.
- Update imports in your Angular modules to use the new component modules.
- Check for renamed properties (e.g., `autoDisplayFirst` in Dropdown has been removed earlier).
- Use the [PrimeNG Migration Guide](https://primeng.org/migration/v19) for detailed breaking changes.

---

## 📌 Recommendation
Migrate gradually: replace deprecated components as you update features. This ensures your project stays **future-proof** for upcoming PrimeNG versions.

---

Would you like me to also create a **side-by-side cheat sheet PDF** you can share with your team for quick reference?

