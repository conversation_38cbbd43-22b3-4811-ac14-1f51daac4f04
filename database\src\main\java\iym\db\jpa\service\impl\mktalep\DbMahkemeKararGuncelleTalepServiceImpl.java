package iym.db.jpa.service.impl.mktalep;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.talep.MahkemeKararGuncellemeTalep;
import iym.common.service.db.mktalep.DbMahkemeKararGuncelleTalepService;
import iym.db.jpa.dao.mktalep.MahkemeKararGuncelleTalepRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * Service implementation for MahkemeAidiyatTalep entity
 */
@Service
public class DbMahkemeKararGuncelleTalepServiceImpl extends GenericDbServiceImpl<MahkemeKararGuncellemeTalep, Long> implements DbMahkemeKararGuncelleTalepService {

    private final MahkemeKararGuncelleTalepRepo mahkemeKararGuncelleTalepRepo;

    @Autowired
    public DbMahkemeKararGuncelleTalepServiceImpl(MahkemeKararGuncelleTalepRepo repository) {
        super(repository);
        this.mahkemeKararGuncelleTalepRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<MahkemeKararGuncellemeTalep> findByDetayMahkemeKararTalepId(Long detayMahkemeKararTalepId) {
        return mahkemeKararGuncelleTalepRepo.findByDetayMahkemeKararTalepId(detayMahkemeKararTalepId);
    }


}
