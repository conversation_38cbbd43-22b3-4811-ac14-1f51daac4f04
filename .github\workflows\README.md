# GitHub Actions Workflows

Bu dizin IYM projesi için GitHub Actions workflow dosyalarını içerir.

## 📋 Mevcut Workflow'lar

### 1. CI Pipeline (`ci.yml`)
**Tetiklenme:** Push (main, develop, feature/github-actions-setup) ve Pull Request (main, develop)

**İşlevler:**
- ✅ **Unit Testler**: Maven ile unit testleri çalıştırır
- 🔨 **Build**: JAR dosyalarını oluşturur
- 🧪 **Integration Testler**: Oracle veritabanı ile integration testleri
- 📊 **Test Raporları**: JUnit test sonuçlarını görselleştirir
- 📦 **Artifacts**: JAR dosyalarını ve test raporlarını saklar

**Jobs:**
1. `test` - Unit testleri çalıştırır
2. `build` - Uygulamayı derler ve JAR oluşturur
3. `integration-test` - Oracle DB ile integration testleri
4. `code-quality` - Kod kalitesi analizi (sadece PR'larda)

### 2. Docker Build (`docker-build.yml`)
**Tetiklenme:** Push (main, develop), Tags (v*) ve Pull Request (main)

**İşlevler:**
- 🐳 **Docker Images**: Backend, Makos ve Frontend için Docker image'ları
- 📦 **Registry**: GitHub Container Registry'ye push
- 🏷️ **Tagging**: Otomatik tag yönetimi
- 💾 **Cache**: Build cache optimizasyonu

**Jobs:**
1. `build-backend` - Backend Docker image
2. `build-makos` - Makos Docker image  
3. `build-frontend` - Frontend Docker image (package.json varsa)

## 🚀 Kullanım

### Workflow'ları Tetikleme
```bash
# Yeni commit push'la
git add .
git commit -m "feat: new feature"
git push origin feature/github-actions-setup

# Pull Request oluştur
gh pr create --title "Add GitHub Actions" --body "CI/CD pipeline setup"
```

### Artifacts İndirme
1. GitHub repo → **Actions** sekmesi
2. İstediğiniz workflow run'ı seç
3. **Artifacts** bölümünden indir:
   - `jar-artifacts` - Backend ve Makos JAR dosyaları
   - `test-results` - Test raporları
   - `integration-test-results` - Integration test sonuçları

### Docker Images
Images GitHub Container Registry'de saklanır:
```bash
# Pull images
docker pull ghcr.io/[username]/[repo]/backend:latest
docker pull ghcr.io/[username]/[repo]/makos:latest
docker pull ghcr.io/[username]/[repo]/frontend:latest
```

## 📊 Monitoring

### Actions Sekmesinde Görebilecekleriniz:
- ✅ **Başarılı builds** - Yeşil check mark
- ❌ **Başarısız builds** - Kırmızı X işareti  
- 🟡 **Devam eden builds** - Sarı nokta
- 📈 **Build süreleri** - Performance metrikleri
- 📋 **Test sonuçları** - Geçen/kalan test sayıları

### Bildirimler
- Email bildirimleri (GitHub ayarlarından)
- Slack/Teams entegrasyonu (opsiyonel)
- PR status checks

## 🔧 Konfigürasyon

### Environment Variables
Workflow'larda kullanılan değişkenler:
- `JAVA_VERSION: '17'` - Java sürümü
- `MAVEN_OPTS: '-Xmx1024m'` - Maven memory ayarları
- `REGISTRY: ghcr.io` - Docker registry

### Secrets (Gerekirse)
Repository Settings → Secrets and variables → Actions:
- `SONAR_TOKEN` - SonarCloud entegrasyonu için
- `SNYK_TOKEN` - Security scanning için
- Custom deployment secrets

## 🛠️ Geliştirme

### Yeni Workflow Ekleme
1. `.github/workflows/` altında yeni `.yml` dosyası oluştur
2. Workflow syntax'ını kullan
3. Test et ve commit et

### Mevcut Workflow'ları Güncelleme
1. İlgili `.yml` dosyasını düzenle
2. Syntax'ı kontrol et
3. Test branch'ında dene
4. Main'e merge et

## 📚 Kaynaklar
- [GitHub Actions Documentation](https://docs.github.com/en/actions)
- [Workflow Syntax](https://docs.github.com/en/actions/using-workflows/workflow-syntax-for-github-actions)
- [Maven Actions](https://github.com/actions/setup-java)
- [Docker Actions](https://github.com/docker/build-push-action)
