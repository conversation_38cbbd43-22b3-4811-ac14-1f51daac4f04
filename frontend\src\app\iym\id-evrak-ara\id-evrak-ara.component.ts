import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

// PrimeNG Imports
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { DatePickerModule } from 'primeng/datepicker';
import { SelectModule } from 'primeng/select';
import { ToastModule } from 'primeng/toast';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { CardModule } from 'primeng/card';
import { DividerModule } from 'primeng/divider';
import { TagModule } from 'primeng/tag';
import { DialogModule } from 'primeng/dialog';

import { MessageService } from 'primeng/api';

// Generated API Imports
import {
  MahkemeKararTalepSorguParam,
  IDMahkemeKararTalepSorgulamaRequest,
  MahkemeKararTalepSorguView
} from '../../generated-api';

// Extended interface for additional search fields (until backend supports them)
interface ExtendedMahkemeKararTalepSorguParam extends MahkemeKararTalepSorguParam {
  mahkemeAdi?: string;
  kurumKodu?: string;
  kurumAdi?: string;
  evrakKonusu?: string;
}

// Services
import { MakosControllerService } from '../../generated-api';

@Component({
  selector: 'app-id-evrak-ara',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TableModule,
    ButtonModule,
    InputTextModule,
    DatePickerModule,
    SelectModule,
    ToastModule,
    ProgressSpinnerModule,
    CardModule,
    DividerModule,
    TagModule,
    DialogModule
  ],
  providers: [MessageService],
  templateUrl: './id-evrak-ara.component.html',
  styleUrls: ['./id-evrak-ara.component.scss']
})
export class IdEvrakAraComponent implements OnInit {

  // Arama Filtreleri (Extended interface kullanıyoruz çünkü backend henüz yeni arama alanlarını desteklemiyor)
  aramaFiltresi: ExtendedMahkemeKararTalepSorguParam = {};

  // Sonuçlar
  evraklar: MahkemeKararTalepSorguView[] = [];
  seciliEvrak: MahkemeKararTalepSorguView | null = null;

  // UI Durumları
  yukleniyor = false;
  detayDialogGoruntule = false;

  // Dropdown Seçenekleri
  durumSecenekleri = [
    { label: 'Tümü', value: null },
    { label: 'Bekliyor', value: 'BEKLIYOR' },
    { label: 'İşlendi', value: 'ISLENDI' },
    { label: 'Hatalı', value: 'HATALI' },
    { label: 'İptal Edildi', value: 'IPTAL_EDILDI' }
  ];

  constructor(
    private makosService: MakosControllerService,
    private messageService: MessageService
  ) {}

  ngOnInit() {
    this.evrakAra();
  }

  evrakAra() {
    this.yukleniyor = true;

    // Yeni arama alanları için bilgi mesajı
    const yeniAlanlarKullanildi = this.aramaFiltresi.mahkemeAdi ||
                                  this.aramaFiltresi.kurumKodu ||
                                  this.aramaFiltresi.kurumAdi ||
                                  this.aramaFiltresi.evrakKonusu;

    if (yeniAlanlarKullanildi && !this.isYeniAramaAlanlariAktif()) {
      this.messageService.add({
        severity: 'info',
        summary: 'Bilgi',
        detail: 'Yeni arama alanları henüz aktif değil. Sadece mevcut alanlar kullanılarak arama yapılıyor.',
        life: 5000
      });
    }

    // Backend henüz yeni arama alanlarını desteklemediği için sadece mevcut alanları gönderiyoruz
    // Yeni alanlar (mahkemeAdi, kurumKodu, kurumAdi, evrakKonusu) şimdilik filtreleme yapmıyor
    const backendSupportedParams: MahkemeKararTalepSorguParam = {
      sorusturmaNo: this.aramaFiltresi.sorusturmaNo,
      mahkemeKararNo: this.aramaFiltresi.mahkemeKararNo,
      mahkemeKodu: this.aramaFiltresi.mahkemeKodu,
      durum: this.aramaFiltresi.durum,
      aciklama: this.aramaFiltresi.aciklama,
      kayitTarihi: this.aramaFiltresi.kayitTarihi,
      kaydedenKullaniciId: this.aramaFiltresi.kaydedenKullaniciId,
      evrakSiraNo: this.aramaFiltresi.evrakSiraNo
    };

    const request: IDMahkemeKararTalepSorgulamaRequest = {
      id: crypto.randomUUID(),
      sorguParam: backendSupportedParams
    };

    this.makosService.mahkemeKararTalepSorgu(request).subscribe({
      next: (response: any) => {
        if (response.success && response.result?.response?.responseCode === 'SUCCESS') {
          this.evraklar = response.result.mahkemeKararTalepSorguViewListesi || [];
          this.messageService.add({
            severity: 'success',
            summary: 'Başarılı',
            detail: `${this.evraklar.length} evrak bulundu`
          });
        } else {
          this.messageService.add({
            severity: 'error',
            summary: 'Hata',
            detail: response.resultDetails || response.result?.response?.responseMessage || 'Evrak arama işlemi başarısız oldu'
          });
        }
        this.yukleniyor = false;
      },
      error: (hata) => {
        this.yukleniyor = false;
        this.messageService.add({
          severity: 'error',
          summary: 'Hata',
          detail: 'Evrak arama işlemi başarısız oldu'
        });
      }
    });
  }

  filtreleriTemizle() {
    // Tüm filtreleri temizle (hem mevcut hem yeni alanlar)
    this.aramaFiltresi = {
      sorusturmaNo: undefined,
      mahkemeKararNo: undefined,
      mahkemeKodu: undefined,
      mahkemeAdi: undefined,
      kurumKodu: undefined,
      kurumAdi: undefined,
      evrakKonusu: undefined,
      durum: undefined,
      aciklama: undefined,
      kayitTarihi: undefined,
      kaydedenKullaniciId: undefined,
      evrakSiraNo: undefined
    };
    this.evrakAra();
  }

  evrakDetayGoster(evrak: MahkemeKararTalepSorguView) {
    this.seciliEvrak = evrak;
    this.detayDialogGoruntule = true;
  }

  detayDialogKapat() {
    this.detayDialogGoruntule = false;
    this.seciliEvrak = null;
  }

  durumSeviyesiGetir(durum: string | undefined): 'success' | 'info' | 'warn' | 'danger' | 'secondary' | 'contrast' {
    if (!durum) return 'info';

    switch (durum) {
      case 'ISLENDI':
        return 'success';
      case 'BEKLIYOR':
        return 'warn';
      case 'HATALI':
        return 'danger';
      case 'IPTAL_EDILDI':
        return 'secondary';
      default:
        return 'info';
    }
  }

  durumMetniGetir(durum: string | undefined): string {
    if (!durum) return 'Bilinmiyor';

    switch (durum) {
      case 'ISLENDI':
        return 'İşlendi';
      case 'BEKLIYOR':
        return 'Bekliyor';
      case 'HATALI':
        return 'Hatalı';
      case 'IPTAL_EDILDI':
        return 'İptal Edildi';
      default:
        return durum;
    }
  }

  tarihFormatiDuzelt(tarih: Date | string | undefined): string {
    if (!tarih) return '';

    if (typeof tarih === 'string') {
      return tarih;
    }

    return tarih.toLocaleDateString('tr-TR');
  }

  excelAktar() {
    this.messageService.add({
      severity: 'info',
      summary: 'Bilgi',
      detail: 'Excel dosyası hazırlanıyor...'
    });
  }

  pdfAktar() {
    this.messageService.add({
      severity: 'info',
      summary: 'Bilgi',
      detail: 'PDF dosyası hazırlanıyor...'
    });
  }

  // Backend güncellendiğinde bu metodu true yaparak yeni alanları aktif hale getirin
  // Ayrıca evrakAra() metodundaki backendSupportedParams kısmını da güncelleyin
  private isYeniAramaAlanlariAktif(): boolean {
    return false; // Backend güncellendiğinde true yapın
  }
}
