#!/bin/bash
# Setup External Volumes for GitLab on Linux
# This script creates directories for GitLab data outside the source code repository

# Define the base directory for GitLab data
BASE_DIR="$HOME/gitlab-data"

# Create the base directory if it doesn't exist
if [ ! -d "$BASE_DIR" ]; then
    echo "Creating base directory: $BASE_DIR"
    mkdir -p "$BASE_DIR"
fi

# Create subdirectories for GitLab data
directories=("config" "logs" "data" "runner-config")

for dir in "${directories[@]}"; do
    path="$BASE_DIR/$dir"
    if [ ! -d "$path" ]; then
        echo "Creating directory: $path"
        mkdir -p "$path"
    else
        echo "Directory already exists: $path"
    fi
done

# Create a .env file with the GITLAB_HOME variable
ENV_FILE=".env"
ENV_CONTENT="GITLAB_HOME=$BASE_DIR"

echo "Creating .env file with GITLAB_HOME=$BASE_DIR"
echo "$ENV_CONTENT" > "$ENV_FILE"

echo -e "\nExternal volumes setup complete!"
echo "GitLab data will be stored in: $BASE_DIR"
echo "To start GitLab with these external volumes, run:"
echo "docker-compose -f docker-compose.linux.yml up -d"

# Make the script executable
chmod +x "$ENV_FILE"