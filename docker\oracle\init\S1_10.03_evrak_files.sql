-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create sequence for HEDEFLER if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = 'EVRAK_FILES_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.EVRAK_FILES_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

-- Create EVRAK_FILES table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'EVRAK_FILES';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.EVRAK_FILES (
      ID NUMBER NOT NULL,
      EVRAK_ID NUMBER,
      FILE_NAME VARCHAR2(400 BYTE),
      HEDEF_ADI VARCHAR2(100 BYTE),
      SIRA_NO NUMBER,
      SILINDI VARCHAR2(1 BYTE),
      CONSTRAINT EVRAK_FILES_PK PRIMARY KEY (ID) ENABLE
    )';

  END IF;
END;
/




COMMIT;
