package iym.db.jpa.dao.sorgu.internal;

import lombok.*;

import java.time.LocalDateTime;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class IdIslenecekEvrakSorguInfo {

    private Long evrakId;
    private String evrakSiraNo;
    private String evrakNo;
    private LocalDateTime evrakGirişTarihi;
    private LocalDateTime evrakTarihi;
    private String evrakIlIlceKodu;
    private String evraklIlceAdi;
    private String evrakKurumKodu;
    private String evrakKurumAdı;
    private boolean acil;
    private String aciklama;
    private Long mahkemeKararTalepId;
    private Long atayanKullaniciId;
    private String atayanAdiSoyadi;
    private Long atananKullaniciId;
    private String atananAdiSoyadi;
    private String sorusturmaNo;
    private String mahkemeKararNo;
    private String mahkemeKodu;
    private String mahkemeAdi;

}
