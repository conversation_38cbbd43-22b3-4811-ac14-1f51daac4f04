-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create sequence for MAHKEME_SUCLAR_ISLEM_SEQ if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = '<PERSON><PERSON><PERSON><PERSON>_SUCLAR_ISLEM_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.MAHKEME_SUCLAR_ISLEM_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/
--todo : byte -> null
-- Create MAHKEME_SUCLAR_ISLEM table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'MAHKEME_SUCLAR_ISLEM';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.MAHKEME_SUCLAR_ISLEM (
      ID NUMBER NOT NULL,
      <PERSON><PERSON><PERSON><PERSON>_KARAR_ID NUMBER NOT NULL,
      <PERSON><PERSON><PERSON><PERSON>_SUC_TIP_KOD VARCHAR2(10) NOT NULL
    )';
    

  END IF;
END;
/

COMMIT;
