package iym.backend.shared.security;

import com.fasterxml.jackson.annotation.JsonIgnore;
import iym.backend.kullanici.entity.Kullanici;
import iym.common.enums.KullaniciKurum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.*;

/**
 * UserDetails implementation for Kullanici entity
 * Used by Spring Security for authentication and authorization
 */
@ToString
public class KullaniciUserDetailsImpl implements UserDetails {

    @Getter
    private final Long userId;
    private final String username;

    @JsonIgnore
    @ToString.Exclude
    private final String password;

    @Getter
    @Setter
    private KullaniciKurum kurum;

    private final Set<GrantedAuthority> grantedAuthorities = new HashSet<>();

    /**
     * Constructor from Kullanici entity
     */
    public KullaniciUserDetailsImpl(<PERSON><PERSON><PERSON> kullanici) {
        this.userId = kullanici.getId();
        this.username = kullanici.getUsername();
        this.password = kullanici.getPassword();
        this.kurum = kullanici.getKurum();

        // Add authorities from user's roles (from related tables)
        List<String> roles = kullanici.getRoles();
        for (String role : roles) {
            this.grantedAuthorities.add(new SimpleGrantedAuthority(role));
        }

        // If no roles found, add a default role
        if (this.grantedAuthorities.isEmpty()) {
            this.grantedAuthorities.add(new SimpleGrantedAuthority("ROLE_USER"));
        }
    }

    /**
     * Constructor with individual parameters
     */
    public KullaniciUserDetailsImpl(Long userId, String username, String password, KullaniciKurum kurum, Collection<String> authorities) {
        this.userId = userId;
        this.username = username;
        this.password = password;
        this.kurum = kurum;

        if (authorities != null && !authorities.isEmpty()) {
            for (String authority : authorities) {
                this.grantedAuthorities.add(new SimpleGrantedAuthority(authority));
            }
        } else {
            this.grantedAuthorities.add(new SimpleGrantedAuthority("ROLE_USER"));
        }
    }

    /**
     * Check if user has a specific authority
     */
    public boolean hasAuthority(String authority) {
        return grantedAuthorities.stream()
                .anyMatch(grantedAuthority -> grantedAuthority.getAuthority().equals(authority));
    }

    /**
     * Check if user has any of the given authorities
     */
    public boolean hasAnyAuthority(String... authorities) {
        for (String authority : authorities) {
            if (hasAuthority(authority)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return grantedAuthorities;
    }

    @Override
    public String getPassword() {
        return password;
    }

    @Override
    public String getUsername() {
        return username;
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        return true;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        KullaniciUserDetailsImpl that = (KullaniciUserDetailsImpl) o;
        return Objects.equals(userId, that.userId) &&
                Objects.equals(username, that.username);
    }

    @Override
    public int hashCode() {
        return Objects.hash(userId, username);
    }
}
