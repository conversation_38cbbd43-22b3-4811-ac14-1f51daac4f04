-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create sequence for MAHKEME_SUCTIPI_DETAY_TALEP if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = 'MAH_ST_DET_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.MAH_ST_DET_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

-- Create MAHKEME_SUCTIPI_DETAY_TALEP table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'MAHKEME_SUCTIPI_DETAY';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.MAHKEME_SUCTIPI_DETAY (
      ID NUMBER NOT NULL,
      ILISKILI_MAHKEME_KARAR_ID NUMBER,
      MAH<PERSON>ME_KARAR_ID NUMBER,
      MA<PERSON><PERSON><PERSON>_SUC_TIPI_EKLE VARCHAR2(25 BYTE),
      MAHKEME_SUC_TIPI_CIKAR VARCHAR2(25 BYTE),
      TARIH DATE NOT NULL,
      DURUM VARCHAR2(15 BYTE),
      MAHKEME_KARAR_DETAY_ID NUMBER,
      CONSTRAINT MAHKEME_ST_DETAY_PK PRIMARY KEY (ID) ENABLE
    )';
  END IF;
END;
/

COMMIT;
