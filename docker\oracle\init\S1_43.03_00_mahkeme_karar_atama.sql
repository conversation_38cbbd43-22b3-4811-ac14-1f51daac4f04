-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create sequence for MAHKEME_KARAR_ATAMA_SEQ if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = '<PERSON><PERSON><PERSON><PERSON>_KARAR_ATAMA_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.MAHKEME_KARAR_ATAMA_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

--TODO : canliya Primary key eklenecek - PK_MAHKEME_KARAR_ATAMA
--TODO : varchar daki byte lar kaldiri<PERSON>k
-- Create MAHKEME_KARAR_ATAMA table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'MAHKEME_KARAR_ATAMA';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.MAHKEME_KARAR_ATAMA (
          ID NUMBER
        , EVRAK_ID NUMBER
        , KULLANICI_ID NUMBER
        , TARIH DATE
        , DURUM VARCHAR2(10)
        , SEVIYE VARCHAR2(1) DEFAULT 0
        , ACIKLAMA VARCHAR2(2000)
        , SEBEBI NUMBER
        , GONDEREN_ID NUMBER
        , GONDERILEN_ID NUMBER
        , CONSTRAINT PK_MAHKEME_KARAR_ATAMA PRIMARY KEY (ID) ENABLE
    )';

  END IF;
END;
/


COMMIT;
