/**
 * IYM Backend OpenAPI definition
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { MahkemeKararTalepStateUpdateResponse } from './mahkemeKararTalepStateUpdateResponse';
import { ResponseIDYeniKararResponseException } from './responseIDYeniKararResponseException';


export interface ResponseMahkemeKararTalepStateUpdateResponse { 
    resultCode?: ResponseMahkemeKararTalepStateUpdateResponse.ResultCodeEnum;
    resultDetails?: string;
    exception?: ResponseIDYeniKararResponseException;
    result?: MahkemeKararTalepStateUpdateResponse;
    success?: boolean;
}
export namespace ResponseMahkemeKararTalepStateUpdateResponse {
    export const ResultCodeEnum = {
        Success: 'SUCCESS',
        Failed: 'FAILED',
        Rejected: 'REJECTED'
    } as const;
    export type ResultCodeEnum = typeof ResultCodeEnum[keyof typeof ResultCodeEnum];
}


