# JUnit 5 Parallel Execution Configuration for Database Module Integration Tests
# This configuration enables parallel execution of database integration tests with Oracle TestContainers
# while maintaining proper isolation and resource management

# Enable parallel execution
junit.jupiter.execution.parallel.enabled=true

# Set parallel execution mode for classes and methods
# Use concurrent execution for both classes and methods to maximize parallelism
junit.jupiter.execution.parallel.mode.default=concurrent
junit.jupiter.execution.parallel.mode.classes.default=concurrent

# Configure thread pool for parallel execution
# Use fixed strategy for more predictable resource usage with Oracle TestContainers
# Oracle TestContainers can be resource-intensive, so we limit parallelism
junit.jupiter.execution.parallel.config.strategy=fixed
junit.jupiter.execution.parallel.config.fixed.parallelism=2

# Alternative dynamic configuration (use if fixed causes issues)
# junit.jupiter.execution.parallel.config.strategy=dynamic
# junit.jupiter.execution.parallel.config.dynamic.factor=0.3

# TestContainers and Database-specific timeout settings
# Oracle TestContainers can take longer to start up, so increase timeouts
junit.jupiter.execution.timeout.default=15m
junit.jupiter.execution.timeout.testable.method.default=10m

# Display names for better test reporting
junit.jupiter.displayname.generator.default=org.junit.jupiter.api.DisplayNameGenerator$ReplaceUnderscores

# Resource isolation settings for database tests
# These settings help prevent resource conflicts between parallel test executions
junit.jupiter.execution.parallel.config.dynamic.max-pool-size=4
junit.jupiter.execution.parallel.config.dynamic.core-pool-size=2

# TestContainers resource management
# Enable proper cleanup and resource sharing for Oracle containers
testcontainers.reuse.enable=false
