# IYM Postman Collections

This directory contains Postman collections and environments for testing the IYM (İletişimin Yönetimi) system with separate backend and makos modules.

## 📁 Directory Structure

```
docs/postman/
├── backend/                    # Backend Module (Port 4000)
│   ├── IYM_Backend_Environment.postman_environment.json
│   ├── IYM_Backend_JWT_Authentication.postman_collection.json
│   └── IYM_Quick_Test.postman_collection.json
├── makos/                      # Makos Module (Port 5000)
│   ├── IYM_Makos_Environment.postman_environment.json
│   ├── mahkemekararcontroller/
│   │   └── MahkemeKararController.postman_collection.json
│   └── test-data/              # Sample JSON data files
└── [Documentation files]
```

## 🚀 Quick Start

### Backend Module Testing (Port 4000)
1. Import `backend/IYM_Backend_Environment.postman_environment.json`
2. Import collections from `backend/` directory
3. Select "IYM Backend Environment" in Postman
4. Test JWT authentication and backend endpoints

### Makos Module Testing (Port 5000)
1. Import `makos/IYM_Makos_Environment.postman_environment.json`
2. Import collections from `makos/mahkemekararcontroller/` directory
3. Select "IYM Makos Environment" in Postman
4. Test makos endpoints with Basic Auth

## 🔐 Authentication

### Backend Authentication
- **Method**: JWT Bearer Token
- **Login**: `POST /api/auth/login`
- **Credentials**: `admin:1`
- **Usage**: Automatic token management in collections

### Makos Authentication
- **Method**: Basic Auth
- **Credentials**: `makos_admin:123456`
- **Usage**: Pre-configured in environment variables

## 🌐 Base URLs & Ports

| Module  | Port | Base URL | Context Path |
|---------|------|----------|--------------|
| Backend | 4000 | http://localhost:4000 | / |
| Makos   | 5000 | http://localhost:5000 | /makosapi |

## 📋 Available Collections

### Backend Collections (`backend/`)
- **IYM_Backend_JWT_Authentication.postman_collection.json**
  - Complete JWT authentication flow
  - Login, change password, protected endpoints
  - Health check and API documentation access

- **IYM_Quick_Test.postman_collection.json**
  - Quick authentication test
  - Simplified testing workflow

### Makos Collections (`makos/`)
- **mahkemekararcontroller/MahkemeKararController.postman_collection.json**
  - Court decision management endpoints
  - File upload with multipart form data
  - All CRUD operations for court decisions

### Test Data (`makos/test-data/`)
Sample JSON files for testing makos endpoints:
- `00_01.id_yeni karar.json` - New ID decision
- `00_02.id_uzarma karari.json` - ID extension decision
- `00_03.id_sonlandirma karari.json` - ID termination decision
- `00_04.id_canak_guncelleme_ornek.json` - ID canak update
- `00_05.hedefAdSoyadGuncelle.json` - Target name update
- `00_06.id_mahkeme_kodu_guncelleme.json` - Court code update
- `00_07.id_mahkeme_aidiyat_guncelleme_ornek.json` - Court affiliation update
- `01_00.it_iletisimin_tespiti.json` - IT communication detection

## 🛠️ Setup Instructions

### Step 1: Import Environment Files
1. Open Postman
2. Click "Import" button
3. Import the appropriate environment file:
   - For backend testing: `backend/IYM_Backend_Environment.postman_environment.json`
   - For makos testing: `makos/IYM_Makos_Environment.postman_environment.json`

### Step 2: Import Collections
1. Import the collections you need:
   - For backend testing: Import collections from `backend/` directory
   - For makos testing: Import collections from `makos/` directory

### Step 3: Select Environment
1. In Postman, use the environment dropdown (top-right)
2. Select the appropriate environment:
   - **"IYM Backend Environment"** for backend collections
   - **"IYM Makos Environment"** for makos collections

## 🔍 Testing Workflows

### Backend Testing Workflow
1. Select "IYM Backend Environment"
2. Run "Login - Get JWT Token" to authenticate
3. JWT token is automatically stored for subsequent requests
4. Test protected endpoints with automatic authentication

### Makos Testing Workflow
1. Select "IYM Makos Environment"
2. Basic Auth is pre-configured in all requests
3. Upload files using multipart form data
4. Use sample JSON from `test-data/` directory

## 📊 Endpoint Summary

### Backend Endpoints (Port 4000)
- `POST /api/auth/login` - User authentication
- `POST /api/auth/changePassword` - Change user password
- `GET /api/makos/health` - Health check
- `GET /api-docs` - API documentation (anonymous access)

### Makos Endpoints (Port 5000)
- `POST /makosapi/mahkemeKarar/yeniKararID` - New ID decision
- `POST /makosapi/mahkemeKarar/kararGonderIT` - Send IT decision
- `POST /makosapi/mahkemeKarar/aidiyatBilgisiGuncelle` - Update affiliation info
- `POST /makosapi/mahkemeKarar/hedefBilgiGuncelle` - Update target info
- `POST /makosapi/mahkemeKarar/mahkemeBilgiGuncelle` - Update court info
- `POST /makosapi/mahkemeKarar/sucTipiGuncelle` - Update crime type

## 🚨 Troubleshooting

### Port Conflicts
- Ensure backend runs on port 4000
- Ensure makos runs on port 5000
- Check that only one instance of each module is running

### Authentication Issues
- **Backend**: Verify JWT token is obtained and stored
- **Makos**: Verify Basic Auth credentials are correct
- Check that the correct environment is selected

### File Upload Issues (Makos)
- Ensure files are properly selected in form data
- Verify JSON data is properly formatted
- Check that Content-Type is set to multipart/form-data

