import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

// PrimeNG Imports
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { DatePickerModule } from 'primeng/datepicker';
import { SelectModule } from 'primeng/select';
import { ToastModule } from 'primeng/toast';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { CardModule } from 'primeng/card';
import { DividerModule } from 'primeng/divider';
import { TagModule } from 'primeng/tag';
import { DialogModule } from 'primeng/dialog';

import { MessageService } from 'primeng/api';

// IYM Imports
import { IymService } from '../shared/services/iym.service';
import { EvrakAramaFiltresi, EvrakAramaSonucu, EvrakKayit } from '../shared/models/iym.models';

@Component({
  selector: 'app-dinleme-evrak-aramasi',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TableModule,
    ButtonModule,
    InputTextModule,
    DatePickerModule,
    SelectModule,
    ToastModule,
    ProgressSpinnerModule,
    CardModule,
    DividerModule,
    TagModule,
    DialogModule
  ],
  providers: [MessageService],
  templateUrl: './dinleme-evrak-aramasi.component.html',
  styleUrls: ['./dinleme-evrak-aramasi.component.scss']
})
export class DinlemeEvrakAramasiComponent implements OnInit {
  
  // Arama Filtreleri
  aramaFiltresi: EvrakAramaFiltresi = {};
  
  // Sonuçlar
  evraklar: EvrakAramaSonucu[] = [];
  seciliEvrak: EvrakAramaSonucu | null = null;
  
  // UI Durumları
  yukleniyor = false;
  detayDialogGoruntule = false;
  evrakDetayi: EvrakKayit | null = null;
  
  // Dropdown Seçenekleri
  mahkemeKodlari = [
    { label: 'Tümü', value: null },
    { label: '06000204 - Ankara 2. Sulh Ceza Hakimliği', value: '06000204' },
    { label: '06003405 - Ankara 34. Asliye Ceza Hakimliği', value: '06003405' },
    { label: '34000101 - İstanbul 1. Sulh Ceza Hakimliği', value: '34000101' }
  ];
  
  evrakTipleri = [
    { label: 'Tümü', value: null },
    { label: 'İletişimin Denetlenmesi', value: 'ILETISIMIN_DENETLENMESI' },
    { label: 'İletişimin Tespiti', value: 'ILETISIMIN_TESPITI' }
  ];
  
  kararTipleri = [
    { label: 'Tümü', value: null },
    { label: 'Önleyici Hakim Kararı', value: '100' },
    { label: 'Adli Hakim Kararı', value: '300' },
    { label: 'Bilgi Alma', value: '500' }
  ];

  constructor(
    private iymService: IymService,
    private messageService: MessageService
  ) {}

  ngOnInit() {
    this.evrakAra();
  }

  evrakAra() {
    this.yukleniyor = true;
    
    this.iymService.evrakAra(this.aramaFiltresi).subscribe({
      next: (sonuclar) => {
        this.evraklar = sonuclar;
        this.yukleniyor = false;
        this.messageService.add({
          severity: 'success',
          summary: 'Başarılı',
          detail: `${sonuclar.length} evrak bulundu`
        });
      },
      error: (hata) => {
        this.yukleniyor = false;
        this.messageService.add({
          severity: 'error',
          summary: 'Hata',
          detail: 'Evrak arama işlemi başarısız oldu'
        });
      }
    });
  }

  filtreleriTemizle() {
    this.aramaFiltresi = {};
    this.evrakAra();
  }

  evrakDetayGoster(evrak: EvrakAramaSonucu) {
    this.seciliEvrak = evrak;
    this.yukleniyor = true;
    
    this.iymService.evrakDetayGetir(evrak.evrakNo).subscribe({
      next: (detay) => {
        this.evrakDetayi = detay;
        this.detayDialogGoruntule = true;
        this.yukleniyor = false;
      },
      error: (hata) => {
        this.yukleniyor = false;
        this.messageService.add({
          severity: 'error',
          summary: 'Hata',
          detail: 'Evrak detayı alınamadı'
        });
      }
    });
  }

  detayDialogKapat() {
    this.detayDialogGoruntule = false;
    this.evrakDetayi = null;
    this.seciliEvrak = null;
  }

  durumSeviyesiGetir(durum: string): 'success' | 'info' | 'warn' | 'danger' | 'secondary' | 'contrast' {
    switch (durum) {
      case 'İşlendi':
        return 'success';
      case 'Bekliyor':
        return 'warn';
      case 'Hatalı':
        return 'danger';
      default:
        return 'info';
    }
  }

  evrakTipiKisaAd(tip: string): string {
    switch (tip) {
      case 'İletişimin Denetlenmesi':
        return 'İ.D.';
      case 'İletişimin Tespiti':
        return 'İ.T.';
      default:
        return tip;
    }
  }

  // Getter method for template expression
  get mahkemeKararHedefleriVar(): boolean {
    return !!(this.evrakDetayi?.mahkemeKarar?.hedefler && this.evrakDetayi.mahkemeKarar.hedefler.length > 0);
  }

  tarihFormatiDuzelt(tarih: string): string {
    // DD/MM/YYYY formatını Date objesine çevir
    if (tarih && tarih.includes('/')) {
      const parcalar = tarih.split('/');
      if (parcalar.length === 3) {
        return `${parcalar[2]}-${parcalar[1].padStart(2, '0')}-${parcalar[0].padStart(2, '0')}`;
      }
    }
    return tarih;
  }

  excelAktar() {
    // Excel export işlemi - şimdilik mockup
    this.messageService.add({
      severity: 'info',
      summary: 'Bilgi',
      detail: 'Excel dosyası hazırlanıyor...'
    });
  }

  pdfAktar() {
    // PDF export işlemi - şimdilik mockup
    this.messageService.add({
      severity: 'info',
      summary: 'Bilgi',
      detail: 'PDF dosyası hazırlanıyor...'
    });
  }
}
