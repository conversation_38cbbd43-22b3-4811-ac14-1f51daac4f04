package iym.common.model.api;

import iym.common.enums.ResponseCode;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

class FailedApiResponseTest {

    @Test
    void testFailedApiResponseInheritance() {
        // Given
        UUID errorId = UUID.randomUUID();
        ResponseCode responseCode = ResponseCode.FAILED;
        String responseMessage = "Test error message";

        // When
        FailedApiResponse failedResponse = FailedApiResponse.builder()
                .errorId(errorId)
                .responseCode(responseCode)
                .responseMessage(responseMessage)
                .build();

        // Then
        assertNotNull(failedResponse);
        assertEquals(errorId, failedResponse.getErrorId());
        assertEquals(responseCode, failedResponse.getResponseCode());
        assertEquals(responseMessage, failedResponse.getResponseMessage());
        
        // Test inheritance
        assertTrue(failedResponse instanceof ApiResponse);
    }

    @Test
    void testFailedApiResponseFields() {
        // Given
        UUID errorId = UUID.randomUUID();
        ResponseCode responseCode = ResponseCode.INVALID_REQUEST;
        String responseMessage = "Invalid request";

        // When
        FailedApiResponse failedResponse = FailedApiResponse.builder()
                .errorId(errorId)
                .responseCode(responseCode)
                .responseMessage(responseMessage)
                .build();

        // Then
        // Test that all fields from both FailedApiResponse and ApiResponse are accessible
        assertNotNull(failedResponse.getErrorId());
        assertNotNull(failedResponse.getResponseCode());
        assertNotNull(failedResponse.getResponseMessage());
        
        assertEquals(errorId, failedResponse.getErrorId());
        assertEquals(responseCode, failedResponse.getResponseCode());
        assertEquals(responseMessage, failedResponse.getResponseMessage());
    }

    @Test
    void testApiResponseCanBeUsedStandalone() {
        // Given
        ResponseCode responseCode = ResponseCode.SUCCESS;
        String responseMessage = "Success";

        // When
        ApiResponse apiResponse = ApiResponse.builder()
                .responseCode(responseCode)
                .responseMessage(responseMessage)
                .build();

        // Then
        assertNotNull(apiResponse);
        assertEquals(responseCode, apiResponse.getResponseCode());
        assertEquals(responseMessage, apiResponse.getResponseMessage());
    }
}
