# Oracle TestContainer Paralel Execution Sorunları ve Çözümleri

## Sorun Tanımı

Paralel test çalıştırma sırasında Oracle TestContainer ile ilgili aşağıdaki hatalar alınıyordu:

1. **Container Başlatma Hatası**: `IllegalStateException: Mapped port can only be obtained after the container is started`
2. **Connection Pool Çakışmaları**: `java.sql.BatchUpdateException: G/Ç Hatası`
3. **Connection Kapanma Sorunları**: `SQLRecoverableException: Kapalı Bağlantı`

## Uygulanan Çözümler

### 1. Container Başlatma Senkronizasyonu İyileştirmesi

**Dosya**: `common/src/test/java/iym/common/testcontainer/AbstractOracleTestContainerForDataJpa.java`

**Değişiklikler**:
- `waitForOracleContainerReady()` metodunda 3 aşamalı kontrol eklendi:
  1. Container'ın çalışır durumda olduğunu kontrol
  2. Port mapping'in hazır olduğunu kontrol  
  3. Database bağlantısının çalıştığını kontrol
- Her aşama için ayrı retry logic ve timeout değerleri
- Daha detaylı hata mesajları ve logging

### 2. Connection Pool İzolasyonu

**Değişiklikler**:
- Her test için benzersiz connection pool isimleri: `OracleTestPool-{ThreadName}-{Timestamp}`
- Paralel execution için optimize edilmiş HikariCP ayarları:
  - `initialization-fail-timeout: -1` (Don't fail fast)
  - `isolate-internal-queries: true`
  - `connection-test-query: SELECT 1 FROM DUAL`
  - `validation-timeout: 5000`

### 3. Container Lifecycle Yönetimi

**Yeni Metodlar**:
- `cleanupContainerResources()`: Test sınıfları arası temizlik
- `ensureCleanContainerState()`: Test öncesi container durumu temizliği

**Test Sınıflarında Kullanım**:
- `@BeforeEach` metodlarında `ensureCleanContainerState()` çağrısı
- `@AfterEach` metodlarında `entityManager.clear()` çağrısı

### 4. Paralel Test Ayarları Optimizasyonu

**Dosyalar**:
- `database/src/test/resources/junit-platform.properties`
- `makos/src/test/resources/junit-platform.properties`

**Değişiklikler**:
- Thread stratejisi: `dynamic` → `fixed` (daha öngörülebilir)
- Parallelism: `2` (Oracle container'lar için konservatif)
- Timeout değerleri artırıldı:
  - Default timeout: `10m` → `15m`
  - Method timeout: `5m` → `8m`
- Resource isolation ayarları eklendi

## Test Edilmesi Gereken Senaryolar

1. **Paralel Test Çalıştırma**:
   ```bash
   mvn test -Dtest="*OracleIntegrationTest" -Djunit.jupiter.execution.parallel.enabled=true
   ```

2. **Tek Test Sınıfı**:
   ```bash
   mvn test -Dtest="MakosUserRepoOracleIntegrationTest"
   ```

3. **Tüm Database Testleri**:
   ```bash
   mvn test -pl database -Dtest="*Oracle*"
   ```

## Beklenen Sonuçlar

- ✅ Container başlatma hatalarının ortadan kalkması
- ✅ Connection pool çakışmalarının önlenmesi
- ✅ Test sınıfları arası izolasyonun sağlanması
- ✅ Paralel execution'da stabil test sonuçları

## Monitoring ve Debugging

Test çalıştırma sırasında aşağıdaki log mesajlarını takip edin:

- `⏳ Oracle container not running, waiting for startup...`
- `✅ Oracle container port mapping ready`
- `✅ Oracle database is ready after X seconds`
- `🔄 Container state cleaned for next test`
- `🧹 Cleaning up Oracle container resources...`

## Sorun Devam Ederse

1. **Reuse ayarını kontrol edin**: `testcontainers.reuse.enable=false`
2. **Thread sayısını azaltın**: `junit.jupiter.execution.parallel.config.fixed.parallelism=1`
3. **Sequential execution'a geçin**: `junit.jupiter.execution.parallel.enabled=false`
4. **Container log'larını inceleyin**: `docker logs <container_id>`

## Performans Notları

- Fixed thread strategy ile daha stabil sonuçlar beklenir
- 2 paralel thread Oracle container'lar için optimal değer
- Timeout değerleri CI/CD ortamları için yeterli olmalı
- Connection pool ayarları test ortamı için optimize edildi
