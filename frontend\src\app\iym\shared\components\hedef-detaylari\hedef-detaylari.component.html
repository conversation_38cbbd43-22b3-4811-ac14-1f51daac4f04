<div class="w-full">
  <div class="flex justify-between items-center mb-4">
    <span class="text-sm text-gray-600">{{ label }} - Toplam {{ value.length }} hedef</span>
    <p-button 
      icon="pi pi-plus" 
      label="Hedef Ekle" 
      severity="success" 
      size="small" 
      (onClick)="hedefEkleDialog()"
      [disabled]="disabled"
    >
    </p-button>
  </div>

  <p-table [value]="value" responsiveLayout="scroll" styleClass="p-datatable-sm" *ngIf="value.length > 0">
    <ng-template pTemplate="header">
      <tr>
        <th>Hedef No</th>
        <th>Hedef Tipi</th>
        <th>Ad Soyad</th>
        <th>Başlama Tarihi</th>
        <th>Süre</th>
        <th>Hedef <PERSON>t <PERSON></th>
        <th>İşlemler</th>
      </tr>
    </ng-template>

    <ng-template pTemplate="body" let-hedef let-i="rowIndex">
      <tr>
        <td>{{ hedef.hedefNo }}</td>
        <td>{{ getHedefTipLabel(hedef.hedefTip) }}</td>
        <td>{{ hedef.hedefAd }} {{ hedef.hedefSoyad }}</td>
        <td>{{ formatTarih(hedef.baslamaTarihi) }}</td>
        <td>{{ hedef.sure }} {{ getSureTipiLabel(hedef.sureTip) }}</td>
        <td>
          <p-chip *ngFor="let kod of hedef.hedefAidiyatKodlari" [label]="kod" class="mr-1"></p-chip>
        </td>
        <td>
          <div class="flex gap-1">
            <p-button 
              icon="pi pi-pencil" 
              size="small" 
              severity="info" 
              (onClick)="hedefDuzenleDialog(i)" 
              pTooltip="Düzenle" 
              tooltipPosition="top"
              [disabled]="disabled"
            >
            </p-button>
            <p-button 
              icon="pi pi-trash" 
              size="small" 
              severity="danger" 
              (onClick)="hedefSil(i)" 
              pTooltip="Sil" 
              tooltipPosition="top"
              [disabled]="disabled"
            >
            </p-button>
          </div>
        </td>
      </tr>
    </ng-template>

    <ng-template pTemplate="emptymessage">
      <tr>
        <td colspan="7" class="text-center py-8">
          <i class="pi pi-info-circle text-4xl text-gray-400 mb-2"></i>
          <p class="text-gray-500">Henüz hedef eklenmedi.</p>
        </td>
      </tr>
    </ng-template>
  </p-table>

  <div *ngIf="value.length === 0" class="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
    <i class="pi pi-info-circle text-4xl text-gray-400 mb-2"></i>
    <p class="text-gray-500 mb-4">Henüz hedef eklenmedi.</p>
    <p-button 
      icon="pi pi-plus" 
      label="İlk Hedefi Ekle" 
      severity="success" 
      (onClick)="hedefEkleDialog()"
      [disabled]="disabled"
    >
    </p-button>
  </div>
</div>

<!-- Hedef Dialog -->
<p-dialog 
  header="Hedef Detayı" 
  [(visible)]="hedefDialogVisible" 
  [modal]="true" 
  [style]="{width: '50vw'}" 
  [draggable]="false" 
  [resizable]="false"
>
  <form [formGroup]="hedefForm" class="grid grid-cols-1 md:grid-cols-2 gap-4">
    <div class="flex flex-col">
      <label class="text-sm font-medium text-gray-700 mb-1">Hedef No <span class="text-red-500">*</span></label>
      <input pInputText formControlName="hedefNo" placeholder="Hedef numarasını giriniz" class="w-full" />
    </div>

    <div class="flex flex-col">
      <label class="text-sm font-medium text-gray-700 mb-1">Hedef Tipi <span class="text-red-500">*</span></label>
      <p-dropdown formControlName="hedefTip" [options]="[
        {label: 'GSM', value: HedefTipEnum.Gsm},
        {label: 'Sabit', value: HedefTipEnum.Sabit},
        {label: 'IMEI', value: HedefTipEnum.Imei}
      ]" placeholder="Hedef tipi seçiniz" class="w-full"></p-dropdown>
    </div>

    <div class="flex flex-col">
      <label class="text-sm font-medium text-gray-700 mb-1">Ad <span class="text-red-500">*</span></label>
      <input pInputText formControlName="hedefAd" placeholder="Adını giriniz" class="w-full" />
    </div>

    <div class="flex flex-col">
      <label class="text-sm font-medium text-gray-700 mb-1">Soyad <span class="text-red-500">*</span></label>
      <input pInputText formControlName="hedefSoyad" placeholder="Soyadını giriniz" class="w-full" />
    </div>

    <div class="flex flex-col">
      <label class="text-sm font-medium text-gray-700 mb-1">Başlama Tarihi <span class="text-red-500">*</span></label>
      <p-calendar formControlName="baslamaTarihi" [showIcon]="true" dateFormat="dd/mm/yy" placeholder="Tarih seçiniz" class="w-full"></p-calendar>
    </div>

    <div class="flex flex-col">
      <label class="text-sm font-medium text-gray-700 mb-1">Süre <span class="text-red-500">*</span></label>
      <div class="flex gap-2">
        <input pInputText formControlName="sure" placeholder="Süre" class="flex-1" type="number" />
        <p-dropdown formControlName="sureTip" [options]="[
          {label: 'Gün', value: SureTipEnum.Gun},
          {label: 'Ay', value: SureTipEnum.Ay},
          {label: 'Yıl', value: SureTipEnum.Yil}
        ]" class="w-24"></p-dropdown>
      </div>
    </div>

    <div class="flex flex-col md:col-span-2">
      <label class="text-sm font-medium text-gray-700 mb-1">Hedef Aidiyat Kodları</label>
      <p-multiSelect
        formControlName="hedefAidiyatKodlari"
        [options]="hedefAidiyatKodlariOptions"
        [showClear]="true"
        [filter]="false"
        [showToggleAll]="false"
        display="chip"
        placeholder="Aidiyat kodları seçin veya yeni ekleyin..."
        class="w-full"
        [maxSelectedLabels]="10"
      >
        <ng-template pTemplate="header">
          <div class="p-2 border-b">
            <div class="flex gap-2">
              <input
                type="text"
                pInputText
                placeholder="Yeni aidiyat kodu ekle..."
                [(ngModel)]="newHedefAidiyatKodu"
                [ngModelOptions]="{ standalone: true }"
                (keyup.enter)="addNewHedefAidiyatKodu(); $event.preventDefault(); $event.stopPropagation()"
                (click)="$event.stopPropagation()"
                class="flex-1"
              />
              <p-button 
                icon="pi pi-plus" 
                size="small" 
                [disabled]="!newHedefAidiyatKodu.trim()" 
                (onClick)="addNewHedefAidiyatKodu()"
              >
              </p-button>
            </div>
          </div>
        </ng-template>
      </p-multiSelect>
    </div>

    <div class="flex flex-col md:col-span-2">
      <label class="text-sm font-medium text-gray-700 mb-1">Çanak No</label>
      <input pInputText formControlName="canakNo" placeholder="Çanak numarasını giriniz" class="w-full" />
    </div>
  </form>

  <ng-template pTemplate="footer">
    <div class="flex justify-end gap-2">
      <p-button label="İptal" severity="secondary" (onClick)="hedefDialogVisible = false"></p-button>
      <p-button label="Kaydet" severity="success" (onClick)="hedefKaydet()" [disabled]="!hedefForm.valid"></p-button>
    </div>
  </ng-template>
</p-dialog>
