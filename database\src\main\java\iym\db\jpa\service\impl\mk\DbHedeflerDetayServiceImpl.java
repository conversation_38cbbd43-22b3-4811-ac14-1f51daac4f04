package iym.db.jpa.service.impl.mk;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.mk.HedeflerDetay;
import iym.common.service.db.mk.DbHedeflerDetayService;
import iym.db.jpa.dao.mk.HedeflerDetayRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
public class DbHedeflerDetayServiceImpl extends GenericDbServiceImpl<HedeflerDetay, Long> implements DbHedeflerDetayService {

    private final HedeflerDetayRepo hedeflerDetayRepo;

    @Autowired
    public DbHedeflerDetayServiceImpl(HedeflerDetayRepo repository) {
        super(repository);
        this.hedeflerDetayRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public List<HedeflerDetay> findByMahkemeKararId(Long mahkemeKararId){
        return hedeflerDetayRepo.findByMahkemeKararId(mahkemeKararId);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<HedeflerDetay> findByDetayMahkemeKararId(Long detayMahkemeKararId){
        return hedeflerDetayRepo.findByDetayMahkemeKararId(detayMahkemeKararId);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<HedeflerDetay> findHedeflerDetayIslem(Long mahkemeKararId, String hedefNo, Integer hedefTipi){
        return hedeflerDetayRepo.findByMahkemeKararIdAndHedefNoAndHedefTipi(mahkemeKararId, hedefNo, hedefTipi);
    }

}
