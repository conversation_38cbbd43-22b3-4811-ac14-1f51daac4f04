package iym.common.model.entity.iym.mk;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;

/**
 * Entity class for MAHKEME_AIDIYAT_TALEP table
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "MahkemeAidiyat")
@Table(name = "MAHKEME_AIDIYAT")
public class MahkemeAidiyat implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "MAHKEME_AIDIYAT_SEQ")
    @SequenceGenerator(name = "MAHKEME_AIDIYAT_SEQ", sequenceName = "MAHKEME_AIDIYAT_SEQ", allocationSize = 1)
    private Long id;

    @Column(name = "MAHKEME_ID", nullable = false)
    @NotNull
    private Long mahkemeKararId;

    @Column(name = "AIDIYAT_KOD", nullable = false, length = 25)
    @NotNull
    @Size(max = 25)
    private String aidiyatKod;


}
