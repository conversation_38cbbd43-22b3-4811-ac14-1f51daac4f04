/**
 * IYM Backend OpenAPI definition
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { PageableObject } from './pageableObject';
import { UlkeDto } from './ulkeDto';
import { SortObject } from './sortObject';


export interface PageUlkeDto { 
    totalPages?: number;
    totalElements?: number;
    size?: number;
    content?: Array<UlkeDto>;
    number?: number;
    sort?: SortObject;
    first?: boolean;
    last?: boolean;
    numberOfElements?: number;
    pageable?: PageableObject;
    empty?: boolean;
}

