/**
 * IYM Backend OpenAPI definition
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { IDHedefGuncellemeResponse } from './iDHedefGuncellemeResponse';
import { ResponseIDYeniKararResponseException } from './responseIDYeniKararResponseException';


export interface ResponseIDHedefGuncellemeResponse { 
    resultCode?: ResponseIDHedefGuncellemeResponse.ResultCodeEnum;
    resultDetails?: string;
    exception?: ResponseIDYeniKararResponseException;
    result?: IDHedefGuncellemeResponse;
    success?: boolean;
}
export namespace ResponseIDHedefGuncellemeResponse {
    export const ResultCodeEnum = {
        Success: 'SUCCESS',
        Failed: 'FAILED',
        Rejected: 'REJECTED'
    } as const;
    export type ResultCodeEnum = typeof ResultCodeEnum[keyof typeof ResultCodeEnum];
}


