package iym.db.jpa.service.impl.mkislem;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.mkislem.DetayMahkemeKararIslem;
import iym.common.model.entity.iym.talep.DetayMahkemeKararTalep;
import iym.common.service.db.mkislem.DbMahkemeKararIslemDetayService;
import iym.db.jpa.dao.mkislem.DetayMahkemeKararIslemRepo;
import iym.db.jpa.dao.mktalep.DetayMahkemeKararTalepRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class DbMahkemeKararIslemDetayServiceImpl extends GenericDbServiceImpl<DetayMahkemeKararIslem, Long> implements DbMahkemeKararIslemDetayService {

    private final DetayMahkemeKararIslemRepo detayMahkemeKararIslemRepo;

    @Autowired
    public DbMahkemeKararIslemDetayServiceImpl(DetayMahkemeKararIslemRepo repository) {
        super(repository);
        this.detayMahkemeKararIslemRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public List<DetayMahkemeKararIslem> findByEvrakId(Long evrakId){
        return  detayMahkemeKararIslemRepo.findByEvrakId(evrakId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<DetayMahkemeKararIslem> findByMahkemeKararIslemId(Long mahkemeKararIslemId){
        return  detayMahkemeKararIslemRepo.findByMahkemeKararIslemId(mahkemeKararIslemId);
    }

}
