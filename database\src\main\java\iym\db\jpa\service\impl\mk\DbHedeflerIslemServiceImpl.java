package iym.db.jpa.service.impl.mk;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.mkislem.HedeflerIslem;
import iym.common.service.db.mk.DbHedeflerIslemService;
import iym.db.jpa.dao.mkislem.HedeflerIslemRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Service implementation for Hedefler entity
 */
@Service
public class DbHedeflerIslemServiceImpl extends GenericDbServiceImpl<HedeflerIslem, Long> implements DbHedeflerIslemService {

    private final HedeflerIslemRepo hedeflerIslemRepo;

    @Autowired
    public DbHedeflerIslemServiceImpl(HedeflerIslemRepo repository) {
        super(repository);
        this.hedeflerIslemRepo = repository;
    }


    @Override
    @Transactional(readOnly = true)
    public List<HedeflerIslem> findByMahkemeKararIslemId(Long mahkemeKararIslemId) {
        return hedeflerIslemRepo.findByMahkemeKararIslemId(mahkemeKararIslemId);
    }

}
