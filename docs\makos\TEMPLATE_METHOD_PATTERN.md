# Template Method Pattern in Mahkeme Karar Talep Processors

## Overview

The mahkeme karar talep processing system uses the **Template Method design pattern** to ensure consistent processing across different karar types while allowing for type-specific customizations.

## Architecture

### Base Classes

1. **`IDMahkemeKararIsleyiciBase`** - For İletiş<PERSON><PERSON> (ID) karar types
2. **`ITMahkemeKararIsleyiciBase`** - For İletiş<PERSON>in <PERSON> (IT) karar types

### Template Method Pattern Implementation

Both base classes implement the Template Method pattern where:

- The `process()` method defines the algorithm skeleton
- Subclasses implement specific behavior via the abstract `updateRelatedTables()` method
- The core algorithm flow is protected from modification

## Core Algorithm Flow

### IDMahkemeKararIsleyiciBase Flow

```
1. Update MahkemeKararTalep record status
2. Update related EvrakKayit record status  
3. Call subclass-specific updateRelatedTables()
4. Return consolidated response
```

### Transaction Management

- The `process()` method in `IDMahkemeKararIsleyiciBase` is annotated with `@Transactional`
- This ensures all operations (core + subclass-specific) execute within a single transaction
- Guarantees atomicity - either all operations succeed or all are rolled back

## Design Intent and Historical Context

### Why `final` Was Originally Used

The `process()` method was originally marked as `final` to:

1. **Enforce Template Method Pattern** - Prevent subclasses from overriding the core algorithm
2. **Protect Transaction Boundaries** - Ensure subclasses cannot bypass `@Transactional`
3. **Maintain Data Consistency** - Guarantee the sequence of operations cannot be altered
4. **Provide Compile-time Safety** - Catch accidental overrides at compile time

### The Spring AOP Conflict

**Problem**: Spring AOP (which handles `@Transactional`) cannot create CGLIB proxies for `final` methods.

**Symptoms**: 
- CGLIB proxy warnings in application logs
- Potential transaction management issues

**Solution**: Remove `final` modifier while preserving design intent through documentation.

## Current Implementation

### What Changed

- ✅ Removed `final` modifier from `process()` method in `IDMahkemeKararIsleyiciBase`
- ✅ Added comprehensive JavaDoc documentation explaining design intent
- ✅ Added warnings about not overriding the method
- ✅ Preserved `final` on setter methods (no AOP conflicts)

### What Remains Protected

- Setter methods remain `final` to prevent dependency injection override
- Abstract method contract ensures subclasses implement required behavior
- Documentation clearly explains the design pattern and constraints

## Subclass Implementation Guidelines

### Required Implementations

All concrete processors must:

1. **Extend the appropriate base class**
   ```java
   @Component
   public class MyKararIsleyici extends IDMahkemeKararIsleyiciBase {
   ```

2. **Implement updateRelatedTables()**
   ```java
   @Override
   protected MahkemeKararTalepUpdateResponse updateRelatedTables(MahkemeKararTalepUpdateRequest request) {
       // Type-specific database operations
   }
   ```

3. **Implement getRelatedKararTuru()**
   ```java
   @Override
   public KararTuru getRelatedKararTuru() {
       return KararTuru.YOUR_KARAR_TYPE;
   }
   ```

### Critical Rules

❌ **DO NOT override the `process()` method**
- This breaks the template method pattern
- Can bypass transaction boundaries
- May cause data inconsistency

✅ **DO implement `updateRelatedTables()`**
- This is where type-specific logic belongs
- Executes within the same transaction
- Should handle type-specific database operations

## Testing Considerations

When testing processors:

1. **Test the complete flow** through the `process()` method
2. **Verify transaction behavior** - ensure rollback on failures
3. **Test subclass-specific logic** via `updateRelatedTables()`
4. **Verify error handling** and response building

## Code Review Guidelines

When reviewing processor implementations:

1. ✅ Ensure `process()` method is not overridden
2. ✅ Verify `updateRelatedTables()` is properly implemented
3. ✅ Check that no additional `@Transactional` annotations are added to `updateRelatedTables()`
4. ✅ Confirm proper error handling and response building
5. ✅ Validate that database operations are appropriate for the karar type

## Examples

### Correct Implementation
```java
@Component
@Slf4j
public class IDYeniKararIsleyici extends IDMahkemeKararIsleyiciBase {
    
    @Override
    protected MahkemeKararTalepUpdateResponse updateRelatedTables(MahkemeKararTalepUpdateRequest request) {
        try {
            // Type-specific database operations
            // ...
            return buildSuccessResponse(request);
        } catch (Exception ex) {
            log.error("Update failed", ex);
            return buildErrorResponse("Database update error");
        }
    }
    
    @Override
    public KararTuru getRelatedKararTuru() {
        return KararTuru.ILETISIMIN_DENETLENMESI_YENI_KARAR;
    }
}
```

### Incorrect Implementation ❌
```java
@Component
public class BadKararIsleyici extends IDMahkemeKararIsleyiciBase {
    
    // ❌ DO NOT DO THIS - breaks template method pattern
    @Override
    @Transactional
    public MahkemeKararTalepUpdateResponse process(MahkemeKararTalepUpdateRequest request, Long kullaniciId) {
        // Custom implementation that bypasses base class logic
    }
}
```

## Migration Notes

If you encounter CGLIB proxy warnings in other parts of the codebase:

1. Check if the method needs to be `final` for business reasons
2. If `final` is not critical, remove it and add documentation
3. If `final` is required, consider alternative solutions:
   - Move `@Transactional` to service layer
   - Use interface-based JDK proxies
   - Use programmatic transaction management
