package iym.db.jpa.service.impl;

import iym.common.model.entity.iym.talep.MahkemeAidiyatDetayTalep;
import iym.db.jpa.dao.mktalep.MahkemeAidiyatDetayTalepRepo;
import iym.db.jpa.service.impl.mktalep.DbMahkemeAidiyatDetayTalepServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class DbMahkemeAidiyatDetayTalepServiceImplTest {

    @Mock
    private MahkemeAidiyatDetayTalepRepo mahkemeAidiyatDetayTalepRepo;

    @InjectMocks
    private DbMahkemeAidiyatDetayTalepServiceImpl dbMahkemeAidiyatDetayTalepService;

    private MahkemeAidiyatDetayTalep mahkemeAidiyatDetayTalep;
    private LocalDateTime testDate;

    @BeforeEach
    void setUp() {
        testDate = LocalDateTime.now();
        mahkemeAidiyatDetayTalep = MahkemeAidiyatDetayTalep.builder()
                .id(1L)
                .iliskiliMahkemeKararId(100L)
                .mahkemeKararTalepId(200L)
                .mahkemeAidiyatKoduEkle("AIDIYAT-EKLE")
                .mahkemeAidiyatKoduCikar(null)
                .tarih(testDate)
                .durum("AKTIF")
                .mahkemeKararDetayTalepId(300L)
                .build();
    }

    @Test
    void findByMahkemeKararId_shouldReturnListOfMahkemeAidiyatDetayTalep() {
        // Given
        Long mahkemeKararId = 200L;
        List<MahkemeAidiyatDetayTalep> expectedList = Arrays.asList(mahkemeAidiyatDetayTalep);
        when(mahkemeAidiyatDetayTalepRepo.findByMahkemeKararTalepId(mahkemeKararId)).thenReturn(expectedList);

        // When
        List<MahkemeAidiyatDetayTalep> result = dbMahkemeAidiyatDetayTalepService.findByMahkemeKararId(mahkemeKararId);

        // Then
        assertThat(result).isEqualTo(expectedList);
        verify(mahkemeAidiyatDetayTalepRepo).findByMahkemeKararTalepId(mahkemeKararId);
    }





}
