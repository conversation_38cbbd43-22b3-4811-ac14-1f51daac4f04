package iym.common.service.db.mktalep;

import iym.common.model.entity.iym.mk.sorgu.MahkemeKararTalepSorguInfo;
import iym.common.model.entity.iym.mk.sorgu.MahkemeKararTalepSorguParam;
import iym.common.model.entity.iym.talep.MahkemeKararTalep;
import iym.common.service.db.GenericDbService;

import java.util.List;

/**
 * Service interface for MahkemeKararTalep entity
 */
public interface DbMahkemeKararTalepService extends GenericDbService<MahkemeKararTalep, Long> {

    List<MahkemeKararTalep> findByEvrakId(Long evrakId);

    List<MahkemeKararTalepSorguInfo> islenecekMahkemeKararTalepListesi(String kurumKodu);

    List<MahkemeKararTalepSorguInfo> mahkemeKararTalepSorgu(String kurumKodu, MahkemeKararTalepSorguParam sorguParam);


}
