package iym.db.jpa.dao;

import iym.common.model.entity.iym.Kullanicilar;
import iym.common.testcontainer.AbstractOracleTestContainerForDataJpa;
import iym.spring.db.loader.DbLoader;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Oracle Testcontainer integration tests for KullanicilarRepo.
 * <p>
 * This test class uses Oracle Testcontainer to test Oracle-specific column definitions
 * and database behaviors that cannot be properly tested with H2.
 * <p>
 * Features tested:
 * - Oracle CHAR(1) column definitions (IMZA_YETKISI, ACILMI, TEKITMI)
 * - Oracle DATE column behavior
 * - Oracle-specific SQL queries
 * - Entity persistence with Oracle-specific constraints
 *
 * <AUTHOR> Team
 */
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
@DataJpaTest
@ContextConfiguration(classes = {DbLoader.class})
@ActiveProfiles("oracle-test")
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@Import({AbstractOracleTestContainerForDataJpa.OracleTestContainerConfiguration.class})
@Transactional
@Execution(ExecutionMode.SAME_THREAD) // Temporarily disabled parallel execution for debugging
@DisplayName("KullanicilarRepo Oracle Integration Tests")
class KullanicilarRepoOracleIntegrationTest extends AbstractOracleTestContainerForDataJpa {

    @Autowired
    private TestEntityManager entityManager;

    @Autowired
    private KullanicilarRepo kullanicilarRepo;

    private Kullanicilar testKullanici;

    @BeforeEach
    void setUp() {
        // Create test user with Oracle-specific column values
        testKullanici = new Kullanicilar();
        testKullanici.setAdi("Test");
        testKullanici.setSoyadi("User");
        testKullanici.setKullaniciAdi("testuser_oracle");
        testKullanici.setSifre("testpassword123456789012345678"); // CHAR(32 BYTE)
        testKullanici.setDurumu("AKTIF");
        testKullanici.setImzaYetkisi("E"); // CHAR(1) - Oracle specific
        testKullanici.setEkstraGuvenlik("H"); // VARCHAR2(1)
        testKullanici.setTemsilEdilenKurum("01");
        testKullanici.setCid("test_cid_12345678901234567890123");
        testKullanici.setTck("12345678901");
        testKullanici.setParolaDegisimTarihi(LocalDateTime.now()); // Oracle DATE
        testKullanici.setAkademikUnvan("DR");
        testKullanici.setGrupKodu("GRP001");
    }

    @Test
    @DisplayName("Should save and retrieve Kullanici with Oracle CHAR(1) columns")
    void shouldSaveAndRetrieveKullanici_withOracleCharColumns() {
        // When - Save entity with Oracle-specific column definitions
        Kullanicilar savedKullanici = kullanicilarRepo.save(testKullanici);
        entityManager.flush();
        entityManager.clear();

        // Then - Retrieve and verify Oracle CHAR(1) column behavior
        Optional<Kullanicilar> retrievedKullanici = kullanicilarRepo.findById(savedKullanici.getId());

        assertThat(retrievedKullanici).isPresent();
        assertThat(retrievedKullanici.get().getImzaYetkisi()).isEqualTo("E"); // CHAR(1)
        assertThat(retrievedKullanici.get().getEkstraGuvenlik()).isEqualTo("H");
        assertThat(retrievedKullanici.get().getSifre()).hasSize(testKullanici.getSifre().length()); // CHAR(32 BYTE)
        assertThat(retrievedKullanici.get().getParolaDegisimTarihi()).isNotNull(); // Oracle DATE
    }

    @Test
    @DisplayName("Should find Kullanici by kullaniciAdi using Oracle database")
    void shouldFindKullaniciByKullaniciAdi_usingOracle() {
        // Given - Save test user
        kullanicilarRepo.save(testKullanici);
        entityManager.flush();

        // When - Find by kullanici adi
        Optional<Kullanicilar> foundKullanici = kullanicilarRepo.findByKullaniciAdi("testuser_oracle");

        // Then - Verify Oracle-specific behavior
        assertThat(foundKullanici).isPresent();
        assertThat(foundKullanici.get().getKullaniciAdi()).isEqualTo("testuser_oracle");
        assertThat(foundKullanici.get().getImzaYetkisi()).isEqualTo("E");
    }

    @Test
    @DisplayName("Should find Kullanicilar by durumu using Oracle database")
    void shouldFindKullanicilarByDurumu_usingOracle() {
        // Given - Save multiple test users with different durumu
        testKullanici.setDurumu("AKTIF");
        kullanicilarRepo.save(testKullanici);

        Kullanicilar inactiveUser = new Kullanicilar();
        inactiveUser.setAdi("Inactive");
        inactiveUser.setSoyadi("User");
        inactiveUser.setKullaniciAdi("inactive_user");
        inactiveUser.setSifre("inactivepassword12345678901234"); // CHAR(32 BYTE)
        inactiveUser.setDurumu("PASIF");
        inactiveUser.setImzaYetkisi("H"); // CHAR(1)
        kullanicilarRepo.save(inactiveUser);

        entityManager.flush();

        // When - Find by durumu
        List<Kullanicilar> activeUsers = kullanicilarRepo.findByDurumu("AKTIF");
        List<Kullanicilar> inactiveUsers = kullanicilarRepo.findByDurumu("PASIF");

        // Then - Verify Oracle query results
        assertThat(activeUsers).hasSize(1);
        assertThat(inactiveUsers).hasSize(1);
        assertThat(activeUsers.get(0).getDurumu()).isEqualTo("AKTIF");
        assertThat(inactiveUsers.get(0).getDurumu()).isEqualTo("PASIF");
    }

    @Test
    @DisplayName("Should handle Oracle CHAR column padding correctly")
    void shouldHandleOracleCharColumnPadding_correctly() {
        // Given - Set CHAR(1) column with single character
        testKullanici.setImzaYetkisi("E");

        // When - Save and retrieve
        Kullanicilar savedKullanici = kullanicilarRepo.save(testKullanici);
        entityManager.flush();
        entityManager.clear();

        Optional<Kullanicilar> retrievedKullanici = kullanicilarRepo.findById(savedKullanici.getId());

        // Then - Verify Oracle CHAR(1) behavior (no padding issues)
        assertThat(retrievedKullanici).isPresent();
        assertThat(retrievedKullanici.get().getImzaYetkisi()).isEqualTo("E");
        assertThat(retrievedKullanici.get().getImzaYetkisi()).hasSize(1);
    }

    @Test
    @DisplayName("Should handle Oracle DATE column correctly")
    void shouldHandleOracleDateColumn_correctly() {
        // Given - Set Oracle DATE column
        LocalDateTime testDate = LocalDateTime.now().withNano(0); // truncate nanos to be closer to Oracle precision
        testKullanici.setParolaDegisimTarihi(testDate);

        // When - Save and retrieve
        Kullanicilar savedKullanici = kullanicilarRepo.save(testKullanici);
        entityManager.flush();
        entityManager.clear();

        Optional<Kullanicilar> retrievedKullanici = kullanicilarRepo.findById(savedKullanici.getId());

        // Then - Verify Oracle DATE behavior
        assertThat(retrievedKullanici).isPresent();
        assertThat(retrievedKullanici.get().getParolaDegisimTarihi()).isNotNull();

        LocalDateTime retrievedDate = retrievedKullanici.get().getParolaDegisimTarihi();

        // Compare difference in millis
        long diffMillis = Math.abs(Duration.between(testDate, retrievedDate).toMillis());

        assertThat(diffMillis).isLessThan(2000); // tolerance of 2 seconds
    }

    @Test
    @DisplayName("Should retrieve all Kullanicilar ordered by kullaniciAdi using Oracle")
    void shouldRetrieveAllKullanicilarOrderedByKullaniciAdi_usingOracle() {
        // Given - Save multiple users
        testKullanici.setKullaniciAdi("zebra_user");
        kullanicilarRepo.save(testKullanici);

        Kullanicilar alphaUser = new Kullanicilar();
        alphaUser.setAdi("Alpha");
        alphaUser.setSoyadi("User");
        alphaUser.setKullaniciAdi("alpha_user");
        alphaUser.setSifre("alphapassword123456789012345678"); // CHAR(32 BYTE)
        alphaUser.setDurumu("AKTIF");
        alphaUser.setImzaYetkisi("E"); // CHAR(1)
        kullanicilarRepo.save(alphaUser);

        entityManager.flush();

        // When - Find all ordered by kullaniciAdi
        List<Kullanicilar> orderedUsers = kullanicilarRepo.findAllByOrderByKullaniciAdiAsc();

        // Then - Verify Oracle ordering behavior
        assertThat(orderedUsers).isNotEmpty();
        assertThat(orderedUsers.size()).isGreaterThanOrEqualTo(2);

        // Verify ordering (should include testuser from init script)
        for (int i = 1; i < orderedUsers.size(); i++) {
            String current = orderedUsers.get(i).getKullaniciAdi();
            String previous = orderedUsers.get(i - 1).getKullaniciAdi();
            assertThat(current.compareTo(previous)).isGreaterThanOrEqualTo(0);
        }
    }
}
