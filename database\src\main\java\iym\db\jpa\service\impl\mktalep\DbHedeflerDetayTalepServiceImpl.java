package iym.db.jpa.service.impl.mktalep;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.talep.HedeflerDetayTalep;
import iym.common.service.db.mktalep.DbHedeflerDetayTalepService;
import iym.db.jpa.dao.mktalep.HedeflerDetayTalepRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
public class DbHedeflerDetayTalepServiceImpl extends GenericDbServiceImpl<HedeflerDetayTalep, Long> implements DbHedeflerDetayTalepService {

    private final HedeflerDetayTalepRepo hedeflerDetayTalepRepo;

    @Autowired
    public DbHedeflerDetayTalepServiceImpl(HedeflerDetayTalepRepo repository) {
        super(repository);
        this.hedeflerDetayTalepRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public List<HedeflerDetayTalep> findByMahkemeKararTalepId(Long mahkemeKararTalepId) {
        return hedeflerDetayTalepRepo.findByMahkemeKararTalepId(mahkemeKararTalepId);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<HedeflerDetayTalep> findByDetayMahkemeKararTalepId(Long detayMahkemeKararTalepId) {
        return hedeflerDetayTalepRepo.findByDetayMahkemeKararTalepId(detayMahkemeKararTalepId);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<HedeflerDetayTalep> findByMahkemeKararTalepIdAndHedefNoAndHedefTipi(Long mahkemeKararTalepId, String hedefNo, Integer hedefTipi) {
        return hedeflerDetayTalepRepo.findByMahkemeKararTalepIdAndHedefNoAndHedefTipi(mahkemeKararTalepId, hedefNo, hedefTipi);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<HedeflerDetayTalep> findHedeflerDetayIslem(Long mahkemeKararTalepId, String hedefNo, Integer hedefTipi) {
        return hedeflerDetayTalepRepo.findByMahkemeKararTalepIdAndHedefNoAndHedefTipi(mahkemeKararTalepId, hedefNo, hedefTipi);
    }


}
