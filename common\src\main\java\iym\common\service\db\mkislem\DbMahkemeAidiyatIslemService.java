package iym.common.service.db.mkislem;

import iym.common.model.entity.iym.mkislem.MahkemeAidiyatIslem;
import iym.common.service.db.GenericDbService;

import java.util.List;

/**
 * Service interface for MahkemeKararAidiyatIslem entity
 */
public interface DbMahkemeAidiyatIslemService extends GenericDbService<MahkemeAidiyatIslem, Long> {

    List<MahkemeAidiyatIslem> findByMahkemeKararIslemId(Long mahkemeKararIslemId);

}
