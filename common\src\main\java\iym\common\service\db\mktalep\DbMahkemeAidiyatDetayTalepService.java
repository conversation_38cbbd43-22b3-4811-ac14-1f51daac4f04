package iym.common.service.db.mktalep;

import iym.common.model.entity.iym.talep.MahkemeAidiyatDetayTalep;
import iym.common.service.db.GenericDbService;

import java.util.List;

/**
 * Service interface for MahkemeAidiyatDetayTalep entity
 */
public interface DbMahkemeAidiyatDetayTalepService extends GenericDbService<MahkemeAidiyatDetayTalep, Long> {

    List<MahkemeAidiyatDetayTalep> findByMahkemeKararId(Long mahkemeKararId);
    List<MahkemeAidiyatDetayTalep> findByMahkemeKararDetayTalepId(Long mahkemeKararDetayId);
}
