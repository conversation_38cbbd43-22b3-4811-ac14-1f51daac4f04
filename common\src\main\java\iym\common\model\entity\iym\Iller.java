package iym.common.model.entity.iym;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;

/**
 * Entity class for ILLER table
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "Iller")
@Table(name = "ILLER")
public class Iller implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "IL_KOD", nullable = false, length = 4)
    @NotNull
    @Size(max = 4)
    private String ilKod;

    @Column(name = "IL_ADI", length = 50)
    @Size(max = 50)
    private String ilAdi;

    @Column(name = "ILCE_ADI", length = 50)
    @Size(max = 50)
    private String ilceAdi;
}
