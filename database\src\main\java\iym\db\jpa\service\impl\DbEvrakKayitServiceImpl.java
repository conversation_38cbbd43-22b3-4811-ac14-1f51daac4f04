package iym.db.jpa.service.impl;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.EvrakKayit;
import iym.common.service.db.DbEvrakKayitService;
import iym.db.jpa.dao.EvrakKayitRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Service implementation for EvrakKayit entity
 */
@Service
public class DbEvrakKayitServiceImpl extends GenericDbServiceImpl<EvrakKayit, Long> implements DbEvrakKayitService {

    private final EvrakKayitRepo evrakKayitRepo;

    @Autowired
    public DbEvrakKayitServiceImpl(EvrakKayitRepo repository) {
        super(repository);
        this.evrakKayitRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByEvrakSiraNo(String evrakSiraNo) {
        return evrakKayitRepo.existsByEvrakSiraNo(evrakSiraNo);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<EvrakKayit> findByEvrakNoAndEvrakGeldigiKurumKodu(String evrakNo
            , String evrakGeldigiKurumKodu){
        return evrakKayitRepo.findByEvrakNoAndEvrakGeldigiKurumKodu(evrakNo, evrakGeldigiKurumKodu);
    }

    @Override
    @Transactional(readOnly = true)
    public List<EvrakKayit> findAllByEvrakNoAndEvrakGeldigiKurumKodu(String evrakNo
            , String evrakGeldigiKurumKodu){
        return evrakKayitRepo.findAllByEvrakNoAndEvrakGeldigiKurumKodu(evrakNo, evrakGeldigiKurumKodu);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<EvrakKayit> findByEvrakNoAndGeldigiIlIlceKoduAndEvrakGeldigiKurumKodu(String evrakNo,
                                                                                   String gelIlIlceKodu,
                                                                                   String evrakGeldigiKurumKodu){
        return evrakKayitRepo.findByEvrakNoAndGeldigiIlIlceKoduAndEvrakGeldigiKurumKodu(evrakNo, gelIlIlceKodu, evrakGeldigiKurumKodu);
    }


    @Override
    @Transactional(readOnly = true)
    public List<EvrakKayit> findByEvrakTipi(String evrakTipi) {
        return evrakKayitRepo.findByEvrakTipi(evrakTipi);
    }

    @Override
    @Transactional(readOnly = true)
    public List<EvrakKayit> findByGirisTarihBetween(LocalDateTime startDate, LocalDateTime endDate) {
        return evrakKayitRepo.findByGirisTarihBetween(startDate, endDate);
    }

    @Override
    @Transactional(readOnly = true)
    public List<EvrakKayit> findByDurumu(String durumu) {
        return evrakKayitRepo.findByDurumu(durumu);
    }

    @Override
    @Transactional(readOnly = true)
    public List<EvrakKayit> findByHavaleBirim(String havaleBirim) {
        return evrakKayitRepo.findByHavaleBirim(havaleBirim);
    }



    @Override
    @Transactional(readOnly = true)
    public List<EvrakKayit> findByAcilmi(String acilmi) {
        return evrakKayitRepo.findByAcilmi(acilmi);
    }
}
