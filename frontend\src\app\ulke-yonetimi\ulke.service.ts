import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { Observable } from 'rxjs';
import { UlkeDto } from './ulke.model';
import { environment } from '../../enviroments/environment';

@Injectable({ providedIn: 'root' })
export class UlkeService {

  private apiUrl = environment.apiUrl+'/ulkeler'; 

  constructor(private http: HttpClient) {}

  getPaged(page: number, size: number): Observable<any> {
    return this.http.get(`${this.apiUrl}?page=${page}&size=${size}`);
  }

  create(dto: UlkeDto): Observable<UlkeDto> {
    return this.http.post<UlkeDto>(this.apiUrl, dto);
  }

  update(dto: UlkeDto): Observable<UlkeDto> {
    return this.http.put<UlkeDto>(`${this.apiUrl}/${dto.id}`, dto);
  }

  delete(id: number): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`);
  }
}
