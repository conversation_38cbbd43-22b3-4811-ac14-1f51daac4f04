package iym.common.model.entity.iym;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Entity class for XML_ISLEM_LOG table
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "XmlIslemLog")
@Table(name = "XML_ISLEM_LOG")
public class XmlIslemLog implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "XML_ISLEM_LOG_SEQ")
    @SequenceGenerator(name = "XML_ISLEM_LOG_SEQ", sequenceName = "XML_ISLEM_LOG_SEQ", allocationSize = 1)
    private Long id;

    @Column(name = "KULLANICI_ID")
    private Long kullaniciId;

    @Column(name = "IP", length = 100)
    @Size(max = 100, message = "IP 50 karakterden fazla olamaz")
    private String ip;

    @Column(name = "TARIH", nullable = false)
    @NotNull
    private LocalDateTime tarih;

    @Column(name = "ISLEM", length = 4000)
    @Size(max = 100, message = "İşlem 4000 karakterden fazla olamaz")
    private String islem;

    @Column(name = "ISLEM_TABLO", length = 100)
    @Size(max = 100, message = "İşlem Tablo 100 karakterden fazla olamaz")
    private String islemTablo;

    @Column(name = "ISLEM_TABLO_REF_ID")
    private Long islemTabloRefId;

    @Column(name = "ISLEM_ID")
    private Long islemId;

    @Column(name = "KULLANICI_ADI", length = 100)
    @Size(max = 100, message = "Kullanıcı Adı 100 karakterden fazla olamaz")
    private String kullaniciAdi;


}
