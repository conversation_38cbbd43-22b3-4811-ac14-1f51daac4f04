package iym.backend.kullanici.service;

import iym.backend.authentication.dto.ChangePasswordRequest;
import iym.backend.authentication.dto.JwtResponse;
import iym.backend.kullanici.dto.KullaniciDto;
import iym.backend.kullanici.entity.Kullanici;
import iym.backend.shared.service.BaseService;

import java.util.Optional;

public interface KullaniciService extends BaseService<KullaniciDto, Long> {
    JwtResponse changePassword(ChangePasswordRequest request);

    Optional<Kullanici> findByKullaniciAdi(String kullaniciAdi);
}

