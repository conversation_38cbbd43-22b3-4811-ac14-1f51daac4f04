package iym.common.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Tale<PERSON> güncelleme türü enumu", type = "string", allowableValues = {"ONAYLA", "ARSIV", "SIL"})
public enum TalepGuncellemeTuru {

    ONAYLA,
    ARSIV,
    SIL;


    @JsonCreator
    public static TalepGuncellemeTuru fromName(String name) {
        for (TalepGuncellemeTuru code : TalepGuncellemeTuru.values()) {
            if (code.name().equals(name)) {
                return code;
            }
        }
        throw new IllegalArgumentException("Gecersiz TalepGuncellemeTuru: '" + name + "'");
    }

}

