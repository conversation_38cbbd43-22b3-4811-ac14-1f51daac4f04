package iym.common.service.db;

import iym.common.model.entity.iym.talep.HedeflerTalep;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Service interface for HedeflerTalep entity
 */
public interface DbHedeflerTalepService extends GenericDbService<HedeflerTalep, Long> {

    List<HedeflerTalep> findByMahkemeKararTalepId(Long mahkemeKararTalepId);

    List<HedeflerTalep> findByHedefNo(String hedefNo);

    List<HedeflerTalep> findByBaslamaTarihiBetween(LocalDateTime startDate, LocalDateTime endDate);

    List<HedeflerTalep> findByKayitTarihiBetween(LocalDateTime startDate, LocalDateTime endDate);

    List<HedeflerTalep> findByTanimlamaTarihiBetween(LocalDateTime startDate, LocalDateTime endDate);

    List<HedeflerTalep> findByKapatmaTarihiBetween(LocalDateTime startDate, LocalDateTime endDate);

    List<HedeflerTalep> findByImhaTarihiBetween(LocalDateTime startDate, LocalDateTime endDate);


}
