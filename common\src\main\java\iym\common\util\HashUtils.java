package iym.common.util;

import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.math.BigInteger;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class HashUtils {

    public static String md5Checksum(Path filePath) throws IOException, NoSuchAlgorithmException {
        return md5Checksum(Files.readAllBytes(filePath));
    }

    public static String md5Checksum(String filePath) throws IOException, NoSuchAlgorithmException {
        return md5Checksum(Paths.get(filePath));
    }

    public static String md5Checksum(File file) throws IOException, NoSuchAlgorithmException {
        return md5Checksum(file.toPath());
    }

    public static String md5Checksum(MultipartFile file) throws IOException, NoSuchAlgorithmException {
        return md5Checksum(file.getBytes());
    }

    public static String md5Checksum(byte[] data) throws NoSuchAlgorithmException {
        byte[] hash = MessageDigest.getInstance("MD5").digest(data);
        return new BigInteger(1, hash).toString(16);
    }
}
