package iym.common.enums;


import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Mahkeme karar tipi", type = "string", allowableValues = {
        "ONLEYICI_HAKIM_KARARI",
        "SINYAL_BILGI_DEGERLENDIRME_KARARI",
        "ABONE_KUTUK_BILGILERI_KARARI",
        "ONLEYICI_YAZILI_EMIR",
        "ADLI_HAKIM_KARARI",
        "ADLI_HAKIM_HTS_KARARI",
        "ADLI_YAZILI_EMIR",
        "ADLI_KHK_YAZILI_EMIR",
        "ADLI_SAVCILIK_HTS_KARARI",
        "HEDEF_AD_SOYAD_DEGISTIRME",
        "HEDEF_BILGI_DEGISTIRME",
        "MAHKEME_KODU_DEGISTIR<PERSON>",
        "MAHKEME_KARAR_BILGI_DEGISTIRME",
        "MAHKEME_AIDIYAT_DEGISTIRME",
        "ONLEYICI_SONLANDIRMA",
        "ADLI_SONLANDIRMA",
        "ADLI_SAVCILIK_SONLANDIRMA",
        "ADLI_SAVCILIK_YER_TESPITI_SONLANDIRMA",
        "ADLI_KHK_SONLANDIRMA",
        "ADLI_ASKERI_HAKIM_KARARI",
        "ADLI_ASKERI_SONLANDIRMA",
        "ADLI_ASKERI_SAVCILIK_SONLANDIRMA",
        "CANAK_NUMARA_DEGISTIRME",
        "ADLI_ASKERI_YER_TESPITI_SONLANDIRMA",
        "MAHKEME_SUCTIPI_DEGISTIRME"
})
public enum MahkemeKararTip {

    // TODO update and finalize below values

    ONLEYICI_HAKIM_KARARI(100),
    SINYAL_BILGI_DEGERLENDIRME_KARARI(150),
    ABONE_KUTUK_BILGILERI_KARARI(151),
    ONLEYICI_YAZILI_EMIR(200),
    ADLI_HAKIM_KARARI(300),
    ADLI_HAKIM_HTS_KARARI(350),
    ADLI_YAZILI_EMIR(400),
    ADLI_KHK_YAZILI_EMIR(410),
    ADLI_SAVCILIK_HTS_KARARI(450),
    HEDEF_AD_SOYAD_DEGISTIRME(510),
    HEDEF_BILGI_DEGISTIRME(511),
    MAHKEME_KODU_DEGISTIRME(520),
    MAHKEME_KARAR_BILGI_DEGISTIRME(521),
    MAHKEME_AIDIYAT_DEGISTIRME(530),
    ONLEYICI_SONLANDIRMA(600),
    ADLI_SONLANDIRMA(700),
    ADLI_SAVCILIK_SONLANDIRMA(710),
    ADLI_SAVCILIK_YER_TESPITI_SONLANDIRMA(720),
    ADLI_KHK_SONLANDIRMA(730),
    ADLI_ASKERI_HAKIM_KARARI(800),
    ADLI_ASKERI_SONLANDIRMA(900),
    ADLI_ASKERI_SAVCILIK_SONLANDIRMA(910),
    CANAK_NUMARA_DEGISTIRME(599),
    ADLI_ASKERI_YER_TESPITI_SONLANDIRMA(920),
    MAHKEME_SUCTIPI_DEGISTIRME(930)
    ;

    private final int kararKodu;

    MahkemeKararTip(int kararKodu) {
        this.kararKodu = kararKodu;
    }

    public int getKararKodu() {
        return kararKodu;
    }

    @Override
    @JsonValue
    public String toString() {
        return this.name();
    }

    @JsonCreator
    public static MahkemeKararTip fromName(String name) {
        for (MahkemeKararTip mahkemeKararTip : MahkemeKararTip.values()) {
            if (mahkemeKararTip.name().equals(name)) {
                return mahkemeKararTip;
            }
        }
        throw new IllegalArgumentException("Gecersiz kararTipi: '" + name + "'");
    }

    //@JsonCreator
    public static MahkemeKararTip fromValue(int value) {
        for (MahkemeKararTip mahkemeKararTip : MahkemeKararTip.values()) {
            if (mahkemeKararTip.kararKodu == value) {
                return mahkemeKararTip;
            }
        }
        throw new IllegalArgumentException("Gecersiz kararTipi kodu: '" + value + "'");
    }
}
