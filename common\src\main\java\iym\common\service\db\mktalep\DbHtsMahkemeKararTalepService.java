package iym.common.service.db.mktalep;

import iym.common.model.entity.iym.talep.HtsMahkemeKararTalep;
import iym.common.service.db.GenericDbService;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Service interface for HtsMahkemeKararTalep entity
 */
public interface DbHtsMahkemeKararTalepService extends GenericDbService<HtsMahkemeKararTalep, Long> {

    List<HtsMahkemeKararTalep> findByEvrakId(Long evrakId);
    
    List<HtsMahkemeKararTalep> findByKullaniciId(Long kullaniciId);
    
    List<HtsMahkemeKararTalep> findByDurum(String durum);
    
    List<HtsMahkemeKararTalep> findByKararTip(String kararTip);
    
    List<HtsMahkemeKararTalep> findByHukukBirim(String hukukBirim);
    
    List<HtsMahkemeKararTalep> findByMahkemeIli(String mahkemeIli);
    
    List<HtsMahkemeKararTalep> findByMahkemeKodu(String mahkemeKodu);
    
    List<HtsMahkemeKararTalep> findByMahkemeAdiContainingIgnoreCase(String mahkemeAdi);
    
    Optional<HtsMahkemeKararTalep> findByMahkemeKararNo(String mahkemeKararNo);
    
    List<HtsMahkemeKararTalep> findBySorusturmaNo(String sorusturmaNo);
    
    List<HtsMahkemeKararTalep> findByKayitTarihiBetween(LocalDateTime startDate, LocalDateTime endDate);
    
    List<HtsMahkemeKararTalep> findByMahkemeIliAndMahkemeKodu(String mahkemeIli, String mahkemeKodu);
    
    List<HtsMahkemeKararTalep> findByKararTipAndDurum(String kararTip, String durum);
    
    boolean existsByMahkemeKararNo(String mahkemeKararNo);
}
