-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create sequence for MAHKEME_AIDIYAT_DETAY_TALEP if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = 'MA<PERSON>_AID_DET_TALEP_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.MAH_AID_DET_TALEP_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

-- Create MAHKEME_AIDIYAT_DETAY_TALEP table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'MAHKEME_AIDIYAT_DETAY_TALEP';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.MAHKEME_AIDIYAT_DETAY_TALEP (
      ID NUMBER NOT NULL,
      ILISKILI_MAHKEME_KARAR_ID NUMBER,
      MAHKEME_KARAR_ID NUMBER,
      MAHKEME_AIDIYAT_KODU_EKLE VARCHAR2(25 BYTE),
      MAHKEME_AIDIYAT_KODU_CIKAR VARCHAR2(25 BYTE),
      TARIH DATE NOT NULL,
      DURUM VARCHAR2(15 BYTE),
      MAHKEME_KARAR_DETAY_ID NUMBER,
      CONSTRAINT MAHKEME_AIDIYAT_DETAY_TALEP_PK PRIMARY KEY (ID) ENABLE
    )';
  END IF;
END;
/

-- Insert sample data if table is empty
DECLARE
  row_count NUMBER;
BEGIN
  SELECT COUNT(*) INTO row_count FROM iym.MAHKEME_AIDIYAT_DETAY_TALEP;
  IF row_count = 0 THEN
    -- Make sure we have mahkeme_karar_talep records
    DECLARE
      mahkeme_count NUMBER;
      dmahkeme_count NUMBER;
    BEGIN
      SELECT COUNT(*) INTO mahkeme_count FROM iym.MAHKEME_KARAR_TALEP;
      SELECT COUNT(*) INTO dmahkeme_count FROM iym.DMAHKEME_KARAR_TALEP;

      IF mahkeme_count > 0 AND dmahkeme_count > 0 THEN
        -- Get the IDs of the mahkeme_karar_talep and dmahkeme_karar_talep records
        FOR mahkeme_rec IN (
          SELECT m.ID as mahkeme_id, d.ID as detay_id
          FROM iym.MAHKEME_KARAR_TALEP m
          JOIN iym.DMAHKEME_KARAR_TALEP d ON m.ID = d.MAHKEME_KARAR_ID
        ) LOOP
          -- Sample data - Aidiyat detay kaydı (Ekle)
          INSERT INTO iym.MAHKEME_AIDIYAT_DETAY_TALEP (
            ID, ILISKILI_MAHKEME_KARAR_ID, MAHKEME_KARAR_ID,
            MAHKEME_AIDIYAT_KODU_EKLE, MAHKEME_AIDIYAT_KODU_CIKAR,
            TARIH, DURUM, MAHKEME_KARAR_DETAY_ID
          ) VALUES (
            iym.MAH_AID_DET_TALEP_SEQ.NEXTVAL, NULL, mahkeme_rec.mahkeme_id,
            'AIDIYAT-EKLE', NULL,
            SYSDATE, 'AKTIF', mahkeme_rec.detay_id
          );

          -- Sample data - Aidiyat detay kaydı (Çıkar)
          INSERT INTO iym.MAHKEME_AIDIYAT_DETAY_TALEP (
            ID, ILISKILI_MAHKEME_KARAR_ID, MAHKEME_KARAR_ID,
            MAHKEME_AIDIYAT_KODU_EKLE, MAHKEME_AIDIYAT_KODU_CIKAR,
            TARIH, DURUM, MAHKEME_KARAR_DETAY_ID
          ) VALUES (
            iym.MAH_AID_DET_TALEP_SEQ.NEXTVAL, NULL, mahkeme_rec.mahkeme_id,
            NULL, 'AIDIYAT-CIKAR',
            SYSDATE, 'AKTIF', mahkeme_rec.detay_id
          );
        END LOOP;
      END IF;
    END;
  END IF;
END;
/

COMMIT;
