# LocalDateTime Deserialization Configuration

## Overview

This document describes the project-wide configuration that enforces string-only LocalDateTime deserialization across all IYM modules. This configuration ensures that only properly formatted date-time strings are accepted, rejecting numeric timestamps that could cause parsing issues.

## Problem Statement

When clients send integer values for LocalDateTime fields (e.g., Unix timestamps), <PERSON> by default attempts to deserialize them as LocalDateTime objects. This can lead to:

1. **Incorrect date/time values** - Numeric timestamps are interpreted as milliseconds since epoch
2. **Data inconsistency** - Different clients might send different timestamp formats
3. **Validation issues** - Hard to validate and debug date/time data
4. **API contract violations** - Expected ISO format strings vs. actual numeric values

## Solution

A centralized utility class `LocalDateTimeDeserializationUtils` has been created to configure ObjectMapper instances across all modules to reject numeric values for LocalDateTime fields.

### Key Features

- **Project-wide consistency** - Same configuration applied across all modules
- **Clear error messages** - Descriptive exceptions when numeric values are provided
- **Flexible configuration** - Can be applied to any ObjectMapper, RestTemplate, or MessageConverter
- **Maintainable code** - Centralized logic prevents code duplication

## Implementation

### 1. Utility Class

**File**: `common/src/main/java/iym/common/util/LocalDateTimeDeserializationUtils.java`

This utility class provides three main methods:

- `configureObjectMapperForLocalDateTime(ObjectMapper objectMapper)` - Configures any ObjectMapper instance
- `configureMessageConverterForLocalDateTime(MappingJackson2HttpMessageConverter converter)` - Configures message converters
- `configureRestTemplateForLocalDateTime(RestTemplate restTemplate)` - Configures RestTemplate instances

### 2. Configuration Points

The utility is applied at the following key points:

#### A. Global Jackson Configuration
**File**: `common/src/main/java/iym/common/config/JacksonConfig.java`

```java
@Bean
@Primary
public ObjectMapper objectMapper(Jackson2ObjectMapperBuilder builder) {
    ObjectMapper objectMapper = builder
        // ... existing configuration ...
        .build();
    
    // Configure string-only LocalDateTime deserialization
    LocalDateTimeDeserializationUtils.configureObjectMapperForLocalDateTime(objectMapper);
    
    return objectMapper;
}
```

#### B. JsonUtils Utility
**File**: `common/src/main/java/iym/common/util/JsonUtils.java`

```java
public static ObjectMapper getMapper() {
    ObjectMapper objectMapper = new Jackson2ObjectMapperBuilder()
        // ... existing configuration ...
        .build();
    
    // Configure string-only LocalDateTime deserialization
    LocalDateTimeDeserializationUtils.configureObjectMapperForLocalDateTime(objectMapper);
    
    return objectMapper;
}
```

#### C. RestTemplate Configuration
**File**: `backend/src/main/java/iym/backend/util/RestUtils.java`

```java
public static RestTemplate getRestTemplateHttp(int connectTimeout, int readTimeout, boolean useCustomErrorHandler) {
    // ... existing configuration ...
    
    // Configure ObjectMapper for LocalDateTime timestamp handling
    LocalDateTimeDeserializationUtils.configureRestTemplateForLocalDateTime(restTemplate);
    
    return restTemplate;
}
```

#### D. OpenAPI Configuration (MAKOS)
**File**: `makos/src/main/java/iym/makos/config/openapi/OpenApiConfig.java`

```java
@Bean
public MappingJackson2HttpMessageConverter swaggerBeanConfig() {
    MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter(objectMapper);
    
    // Configure the converter to enforce string-only LocalDateTime deserialization
    LocalDateTimeDeserializationUtils.configureMessageConverterForLocalDateTime(converter);
    
    // ... rest of configuration ...
    return converter;
}
```

#### E. API Client Configuration
**File**: `backend/src/main/java/iym/backend/makosclient/config/MakosApiClientConfig.java`

```java
private void configureApiClientObjectMapper(ApiClient apiClient) {
    // ... reflection code to access RestTemplate ...
    
    // Configure the RestTemplate using the utility
    LocalDateTimeDeserializationUtils.configureRestTemplateForLocalDateTime(restTemplate);
}
```

## Behavior

### Accepted Formats

- **Valid ISO format strings**: `"2023-12-25T10:30:00"`
- **Null values**: `null`
- **Empty strings**: `""` (treated as null)

### Rejected Formats

- **Numeric timestamps**: `1703505000000`
- **Invalid format strings**: `"2023-12-25 10:30:00"`

### Error Messages

When numeric values are provided, the system throws an exception with a clear message:

```
LocalDateTime deserialization from numeric values is not allowed. 
Expected string format (ISO_LOCAL_DATE_TIME) but received numeric value: 1703505000000
```

## Testing

### Unit Tests

**File**: `common/src/test/java/iym/common/util/LocalDateTimeDeserializationUtilsTest.java`

The test suite verifies:

1. **Valid string deserialization** - ISO format strings are correctly parsed
2. **Numeric rejection** - Numeric values throw appropriate exceptions
3. **Null handling** - Null values are handled correctly
4. **Empty string handling** - Empty strings are treated as null
5. **Invalid format rejection** - Invalid format strings throw parsing exceptions

### Running Tests

```bash
cd common
mvn test -Dtest=LocalDateTimeDeserializationUtilsTest
```

## Usage Examples

### For New ObjectMapper Instances

```java
ObjectMapper objectMapper = new ObjectMapper();
LocalDateTimeDeserializationUtils.configureObjectMapperForLocalDateTime(objectMapper);
```

### For RestTemplate Instances

```java
RestTemplate restTemplate = new RestTemplate();
LocalDateTimeDeserializationUtils.configureRestTemplateForLocalDateTime(restTemplate);
```

### For Message Converters

```java
MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter(objectMapper);
LocalDateTimeDeserializationUtils.configureMessageConverterForLocalDateTime(converter);
```

## Benefits

1. **Consistency** - All modules use the same LocalDateTime deserialization logic
2. **Maintainability** - Centralized configuration reduces code duplication
3. **Reliability** - Prevents unexpected numeric timestamp parsing
4. **Debugging** - Clear error messages help identify issues quickly
5. **API Contract** - Enforces expected string format for date/time fields

## Migration Notes

- The `configureObjectMapperForLocalDateTime` method in `RestUtils.java` has been removed and replaced with the utility
- All existing ObjectMapper configurations now include the LocalDateTime validation
- No breaking changes to existing API contracts
- Existing valid string formats continue to work as expected

## Future Enhancements

1. **Additional date/time types** - Extend to support LocalDate, LocalTime, etc.
2. **Custom format support** - Allow configuration of alternative date formats
3. **Validation annotations** - Create custom validation annotations for date fields
4. **Error handling** - Provide more detailed error information for debugging
