package iym.common.model.entity.iym.mkislem;

import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Entity class for HEDEFLER_TALEP table
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

//MahkemeHedeflerAidiyatIslem ile ortak kullaniliyor.
@Entity(name = "HedeflerAidiyatIslem")
@Table(name = "HEDEFLER_AIDIYAT_ISLEM")
public class HedeflerAidiyatIslem implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "HED_AIDIYAT_ISLEM_SEQ")
    @SequenceGenerator(name = "HED_AIDIYAT_ISLEM_SEQ", sequenceName = "HED_AIDIYAT_ISLEM_SEQ", allocationSize = 1)
    private Long id;

    @Column(name = "HEDEF_ID", nullable = false)
    private Long hedeflerIslemId;

    @Column(name = "AIDIYAT_KOD", length = 15, nullable = false)
    @Size(max = 15)
    private String aidiyatKod;


    @Column(name = "TARIH", nullable = false)
    private LocalDateTime tarih;

    @Column(name = "KULLANICI_ID", nullable = false)
    private Long kullanciId;


}
