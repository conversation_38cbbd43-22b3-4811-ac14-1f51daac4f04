import { Component, forwardRef, Input } from '@angular/core';
import { ControlValueAccessor, FormBuilder, FormGroup, NG_VALUE_ACCESSOR, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextarea } from 'primeng/inputtextarea';
import { SelectModule } from 'primeng/select';

export interface MahkemeKararDetay {
    mahkemeKodu: string;
    mahkemeIlIlceKodu: string;
    mahkemeKararNo?: string;
    sorusturmaNo?: string;
    aciklama?: string;
}

@Component({
    selector: 'app-mahkeme-karar-detaylari',
    standalone: true,
    imports: [CommonModule, ReactiveFormsModule, InputTextModule, InputTextarea, SelectModule],
    templateUrl: './mahkeme-karar-detaylari.component.html',
    styleUrls: ['./mahkeme-karar-detaylari.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => MahkemeKararDetaylariComponent),
            multi: true
        }
    ]
})
export class MahkemeKararDetaylariComponent implements ControlValueAccessor {
    @Input() showLabels: boolean = true;
    @Input() disabled: boolean = false;
    @Input() required: boolean = false;
    @Input() mahkemeKodlari: any[] = [];
    @Input() iller: any[] = [];
    @Input() dropdownLoading: any = { mahkemeKodlari: false, iller: false };

    form: FormGroup;

    private onChange = (value: MahkemeKararDetay | null) => {};
    private onTouched = () => {};

    constructor(private fb: FormBuilder) {
        this.form = this.fb.group({
            mahkemeKodu: ['', [Validators.required]],
            mahkemeIlIlceKodu: ['', [Validators.required]],
            mahkemeKararNo: [''],
            sorusturmaNo: [''],
            aciklama: ['']
        });

        // Form değişikliklerini dinle
        this.form.valueChanges.subscribe((value) => {
            this.onChange(value);
            this.onTouched();
        });
    }

    // ControlValueAccessor implementation
    writeValue(value: MahkemeKararDetay | null): void {
        if (value) {
            this.form.patchValue(value, { emitEvent: false });
        } else {
            this.form.reset({ emitEvent: false });
        }
    }

    registerOnChange(fn: (value: MahkemeKararDetay | null) => void): void {
        this.onChange = fn;
    }

    registerOnTouched(fn: () => void): void {
        this.onTouched = fn;
    }

    setDisabledState(isDisabled: boolean): void {
        this.disabled = isDisabled;
        if (isDisabled) {
            this.form.disable();
        } else {
            this.form.enable();
        }
    }

    // Validation helper methods
    isFieldInvalid(fieldName: string): boolean {
        const field = this.form.get(fieldName);
        return !!(field && field.invalid && (field.dirty || field.touched));
    }

    getFieldError(fieldName: string): string {
        const field = this.form.get(fieldName);
        if (field && field.errors) {
            if (field.errors['required']) {
                return `${this.getFieldLabel(fieldName)} zorunludur`;
            }
        }
        return '';
    }

    private getFieldLabel(fieldName: string): string {
        const labels: { [key: string]: string } = {
            mahkemeKodu: 'Mahkeme Kodu',
            mahkemeIlIlceKodu: 'Mahkeme İl/İlçe Kodu',
            mahkemeKararNo: 'Mahkeme Karar No',
            sorusturmaNo: 'Soruşturma No',
            aciklama: 'Açıklama'
        };
        return labels[fieldName] || fieldName;
    }

    // Public method to check if form is valid
    isValid(): boolean {
        return this.form.valid;
    }

    // Public method to get form value
    getValue(): MahkemeKararDetay | null {
        return this.form.valid ? this.form.value : null;
    }
}
