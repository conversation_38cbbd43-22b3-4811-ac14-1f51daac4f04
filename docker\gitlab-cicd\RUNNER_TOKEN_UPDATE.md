# GitLab Runner Token System Update

## Overview

The `setup-gitlab-complete.ps1` script has been updated to use GitLab's new authentication token system instead of the deprecated registration token approach. This update addresses the error you encountered and ensures compatibility with current GitLab versions.

## What Was Changed

### 1. Updated Token Creation Process

**Before (Deprecated):**
- Used registration tokens to register runners
- Required manual token retrieval from GitLab UI
- Used `POST /runners` API with registration token

**After (Current):**
- Uses authentication tokens via `POST /user/runners` API
- Creates runners programmatically with authentication tokens
- Follows GitLab 15.10+ new runner creation workflow

### 2. Fixed API Calls

**Updated Personal Access Token Scopes:**
```powershell
# Added 'create_runner' scope for new API
scopes = @("api", "read_user", "read_repository", "write_repository", "create_runner")
```

**Updated Runner Creation API:**
```powershell
# Uses POST /user/runners instead of deprecated registration token method
$runnerResponse = Invoke-RestMethod -Uri "$GitLabUrl/api/v4/user/runners" -Method POST -Body $runnerTokenData -Headers $runnerHeaders
```

### 3. Removed Deprecated Parameters

The script now avoids using deprecated parameters that are not allowed with authentication tokens:
- `--locked`
- `--access-level`
- `--run-untagged`
- `--tag-list`

These settings are now managed through the GitLab UI after runner registration.

### 4. Enhanced Error Handling

- Added token format validation (must start with `glrt-`)
- Improved error messages with specific guidance
- Added fallback instructions for manual token creation

## Error Resolution

The original error you encountered:
```
FATAL: Runner configuration other than name and executor configuration is reserved 
(specifically --locked, --access-level, --run-untagged, --maximum-timeout, --paused, 
--tag-list, and --maintenance-note) and cannot be specified when registering with a 
runner authentication token.
```

**Root Cause:** The script was trying to use deprecated registration token parameters with the new authentication token system.

**Solution:** Updated the script to:
1. Use the correct API endpoint (`POST /user/runners`)
2. Remove restricted parameters from the registration command
3. Provide proper authentication token validation

## GitLab Version Compatibility

| GitLab Version | Token System | Status |
|----------------|--------------|--------|
| < 15.6 | Registration Tokens | Legacy (still works) |
| 15.6 - 15.9 | Registration Tokens | Deprecated |
| 15.10+ | Authentication Tokens | **Current (Recommended)** |
| 20.0+ | Authentication Tokens | Required (registration tokens removed) |

## Manual Token Creation (If Needed)

If automatic token creation fails, follow these updated steps:

1. Go to `http://localhost:8929/admin/runners`
2. Login with `root` and the displayed password
3. Click **"New instance runner"**
4. Enter description: `Manual runner for iym`
5. Add tags: `docker,maven,auto`
6. Check **"Run untagged jobs"**
7. Click **"Create runner"**
8. Copy the authentication token (starts with `glrt-`)

## Benefits of the New System

1. **Better Security:** Authentication tokens are more secure and traceable
2. **Ownership Tracking:** Clear ownership records for runners
3. **Future-Proof:** Compatible with GitLab's roadmap
4. **UI Management:** Runner settings managed through GitLab UI
5. **System ID Support:** Allows reusing tokens across multiple runner managers

## Next Steps

1. Run the updated script: `.\setup-gitlab-complete.ps1`
2. The script will automatically create authentication tokens
3. If manual intervention is needed, follow the new workflow instructions
4. Manage runner settings through GitLab UI at `/admin/runners`

## References

- [GitLab Runner API Documentation](https://docs.gitlab.com/api/runners/)
- [New Runner Registration Workflow](https://docs.gitlab.com/ci/runners/new_creation_workflow/)
- [POST /user/runners API](https://docs.gitlab.com/api/runners/#create-a-runner-linked-to-the-current-user)
