package iym.common.service.db.mk;

import iym.common.model.entity.iym.mk.MahkemeSuclar;
import iym.common.service.db.GenericDbService;

import java.util.List;
import java.util.Optional;

public interface DbMahkemeSuclarService extends GenericDbService<MahkemeSuclar, Long> {

    List<MahkemeSuclar> findByMahkemeKararId(Long mahkemeKararId);

    Optional<MahkemeSuclar> findByMahkemeKararIdAndSucTipKodu(Long mahkemeKararId, String sucTipKodu);

}
