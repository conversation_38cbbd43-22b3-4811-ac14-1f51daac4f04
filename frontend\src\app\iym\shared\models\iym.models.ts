// IYM (İletiş<PERSON>in <PERSON>) Model Tanımları

// // Mahkeme Karar Request Modelleri
// export interface MahkemeKararRequest {
//     id: string;
//     kararTuru: KararTuru;
//     evrakDetay: EvrakDetay;
//     mahkemeKararBilgisi: MahkemeKararBilgisi;
// }

// export interface EvrakDetay {
//     evrakNo: string;
//     evrakTarihi: string; // LocalDateTime as ISO string
//     evrakKurumKodu: string;
//     evrakTuru: EvrakTuru;
//     havaleBirimi?: string;
//     aciklama?: string;
//     geldigiIlIlceKodu: string;
//     acilmi: boolean;
//     evrakKonusu?: string;
// }

// export interface MahkemeKararBilgisi {
//     mahkemeKararTipi: MahkemeKararTip;
//     mahkemeKararDetay: MahkemeKararDetay;
// }

export interface MahkemeKararDetay {
    mahkemeKodu: string;
    mahkemeKararNo: string;
    mahkemeIlIlceKodu: string;
    sorusturmaNo?: string;
    aciklama?: string;
}

export interface HedefDetayID {
    hedefNo: string;
    hedefTip: string;
    hedefAdi?: string;
    hedefSoyadi?: string;
    baslamaTarihi: string;
    suresi?: string;
    sureTipi: string;
    bimAidiyatKod: string;
    canakNo?: string;
}

export interface HedefDetayIT {
    sorguTipi: string;
    hedefNo: string;
    karsiHedefNo?: string;
    baslangicTarihi: string;
    bitisTarihi: string;
    tespitTuru: string;
    tespitTuruDetay?: string;
    aciklama?: string;
}
//
// // Specific Request Types
// export interface IDYeniKararRequest extends MahkemeKararRequest {
//     hedefDetayListesi: HedefDetayID[];
//     mahkemeAidiyatKodlari?: string[];
//     mahkemeSucTipiKodlari?: string[];
// }
//
// export interface IDUzatmaKarariRequest extends MahkemeKararRequest {
//     hedefDetayListesi: HedefDetayID[];
//     mahkemeAidiyatKodlari?: string[];
//     mahkemeSucTipiKodlari?: string[];
// }
//
// export interface IDSonlandirmaKarariRequest extends MahkemeKararRequest {
//     hedefDetayListesi: HedefDetayID[];
//     mahkemeAidiyatKodlari?: string[];
//     mahkemeSucTipiKodlari?: string[];
// }

// export interface ITKararRequest extends MahkemeKararRequest {
//     hedefDetayListesi: HedefDetayIT[];
// }

// Güncelleme request'leri dosyanın sonunda tanımlanmıştır

// export interface GenelEvrakRequest extends MahkemeKararRequest {
//     // General document request
// }

// // Enums
// export enum KararTuru {
//     ILETISIMIN_DENETLENMESI_YENI_KARAR = 'ILETISIMIN_DENETLENMESI_YENI_KARAR',
//     ILETISIMIN_DENETLENMESI_UZATMA_KARARI = 'ILETISIMIN_DENETLENMESI_UZATMA_KARARI',
//     ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI = 'ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI',
//     ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME = 'ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME',
//     ILETISIMIN_DENETLENMESI_HEDEF_ADSOYAD_GUNCELLEME = 'ILETISIMIN_DENETLENMESI_HEDEF_ADSOYAD_GUNCELLEME',
//     ILETISIMIN_DENETLENMESI_MAHKEMEKODU_GUNCELLEME = 'ILETISIMIN_DENETLENMESI_MAHKEMEKODU_GUNCELLEME',
//     ILETISIMIN_DENETLENMESI_CANAK_GUNCELLEME = 'ILETISIMIN_DENETLENMESI_CANAK_GUNCELLEME',
//     ILETISIMIN_TESPITI = 'ILETISIMIN_TESPITI',
//     GENEL_EVRAK = 'GENEL_EVRAK',
//     ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME = 'ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME'
// }
//
// /**
//  * Evrak türü
//  * @description Evrak türü enum tanımı
//  * @enum {string}
//  * @property {string} ILETISIMIN_TESPITI - İletişimin tespiti evrak türü
//  * @property {string} ILETISIMIN_DENETLENMESI - İletişimin denetlenmesi evrak türü
//  * @property {string} GENEL_EVRAK - Genel evrak türü
//  */
// export enum EvrakTuru {
//     ILETISIMIN_TESPITI = 'ILETISIMIN_TESPITI',
//     ILETISIMIN_DENETLENMESI = 'ILETISIMIN_DENETLENMESI',
//     GENEL_EVRAK = 'GENEL_EVRAK'
// }
//
// /**
//  * Mahkeme karar tipi
//  * @description Mahkeme karar tipi enum tanımı
//  * @enum {string}
//  * @property {string} ONLEYICI_HAKIM_KARARI - Önleyici hakim kararı
//  * @property {string} SINYAL_BILGI_DEGERLENDIRME_KARARI - Sinyal bilgi değerlendirme kararı
//  * @property {string} ABONE_KUTUK_BILGILERI_KARARI - Abone kütk bilgileri kararı
//  * @property {string} ONLEYICI_YAZILI_EMIR - Önleyici yazılı emir
//  * @property {string} ADLI_HAKIM_KARARI - Adli hakim kararı
//  * @property {string} ADLI_HAKIM_HTS_KARARI - Adli hakim HTS kararı
//  * @property {string} ADLI_YAZILI_EMIR - Adli yazılı emir
//  * @property {string} ADLI_KHK_YAZILI_EMIR - Adli KHK yazılı emir
//  * @property {string} ADLI_SAVCILIK_HTS_KARARI - Adli savcılık HTS kararı
//  * @property {string} HEDEF_AD_SOYAD_DEGISTIRME - Hedef ad soyad değiştirme
//  * @property {string} MAHKEME_KODU_DEGISTIRME - Mahkeme kodu değiştirme
//  * @property {string} MAHKEME_AIDIYAT_DEGISTIRME - Mahkeme aidiyat değiştirme
//  * @property {string} CANAK_NUMARA_DEGISTIRME - Canak numara değiştirme
//  * @property {string} ONLEYICI_SONLANDIRMA - Önleyici sonlandırma
//  * @property {string} ADLI_SONLANDIRMA - Adli sonlandırma
//  * @property {string} ADLI_SAVCILIK_SONLANDIRMA - Adli savcılık sonlandırma
//  * @property {string} ADLI_SAVCILIK_YER_TESPITI_SONLANDIRMA - Adli savcılık yer tespiti sonlandırma
//  * @property {string} ADLI_KHK_SONLANDIRMA - Adli KHK sonlandırma
//  * @property {string} ADLI_ASKERI_HAKIM_KARARI - Adli askeri hakim kararı
//  * @property {string} ADLI_ASKERI_SONLANDIRMA - Adli askeri sonlandırma
//  * @property {string} ADLI_ASKERI_SAVCILIK_SONLANDIRMA - Adli askeri savcılık sonlandırma
//  * @property {string} ADLI_ASKERI_YER_TESPITI_SONLANDIRMA - Adli askeri yer tespiti sonlandırma
//  */
// export enum MahkemeKararTip {
//     ONLEYICI_HAKIM_KARARI = 'ONLEYICI_HAKIM_KARARI',
//     SINYAL_BILGI_DEGERLENDIRME_KARARI = 'SINYAL_BILGI_DEGERLENDIRME_KARARI',
//     ABONE_KUTUK_BILGILERI_KARARI = 'ABONE_KUTUK_BILGILERI_KARARI',
//     ONLEYICI_YAZILI_EMIR = 'ONLEYICI_YAZILI_EMIR',
//     ADLI_HAKIM_KARARI = 'ADLI_HAKIM_KARARI',
//     ADLI_HAKIM_HTS_KARARI = 'ADLI_HAKIM_HTS_KARARI',
//     ADLI_YAZILI_EMIR = 'ADLI_YAZILI_EMIR',
//     ADLI_KHK_YAZILI_EMIR = 'ADLI_KHK_YAZILI_EMIR',
//     ADLI_SAVCILIK_HTS_KARARI = 'ADLI_SAVCILIK_HTS_KARARI',
//     HEDEF_AD_SOYAD_DEGISTIRME = 'HEDEF_AD_SOYAD_DEGISTIRME',
//     MAHKEME_KODU_DEGISTIRME = 'MAHKEME_KODU_DEGISTIRME',
//     MAHKEME_AIDIYAT_DEGISTIRME = 'MAHKEME_AIDIYAT_DEGISTIRME',
//     CANAK_NUMARA_DEGISTIRME = 'CANAK_NUMARA_DEGISTIRME',
//     ONLEYICI_SONLANDIRMA = 'ONLEYICI_SONLANDIRMA',
//     ADLI_SONLANDIRMA = 'ADLI_SONLANDIRMA',
//     ADLI_SAVCILIK_SONLANDIRMA = 'ADLI_SAVCILIK_SONLANDIRMA',
//     ADLI_SAVCILIK_YER_TESPITI_SONLANDIRMA = 'ADLI_SAVCILIK_YER_TESPITI_SONLANDIRMA',
//     ADLI_KHK_SONLANDIRMA = 'ADLI_KHK_SONLANDIRMA',
//     ADLI_ASKERI_HAKIM_KARARI = 'ADLI_ASKERI_HAKIM_KARARI',
//     ADLI_ASKERI_SONLANDIRMA = 'ADLI_ASKERI_SONLANDIRMA',
//     ADLI_ASKERI_SAVCILIK_SONLANDIRMA = 'ADLI_ASKERI_SAVCILIK_SONLANDIRMA',
//     ADLI_ASKERI_YER_TESPITI_SONLANDIRMA = 'ADLI_ASKERI_YER_TESPITI_SONLANDIRMA'
// }

export interface EvrakKayit {
    xmlVersiyon: string;
    evrakNo: string;
    evrakTarihi: string;
    evrakGeldigiKurum: string;
    evrakTipi: 'ILETISIMIN_DENETLENMESI' | 'ILETISIMIN_TESPITI';
    havaleBirim: string;
    aciklama?: string;
    gelIl: string;
    evrakKonusu?: string;
    acilmi: 'H' | 'E'; // Hayır/Evet
    mahkemeKarar: MahkemeKarar;
}

export interface MahkemeKarar {
    kararTip: string;
    mahkemeKodu: string;
    mahkemeKararNo: string;
    mahkemeIli: string;
    aciklama?: string;
    sorusturmaNo?: string;
    hedefler?: Hedef[];
    itkHedefler?: ItkHedef[];
    mahkemeAidiyat?: MahkemeAidiyat[];
    mahkemeSucTipi?: MahkemeSucTipi[];
}

export interface Hedef {
    hedefNo: string;
    hedefTip: string;
    hedefAdi?: string;
    hedefSoyadi?: string;
    baslamaTarihi: string;
    suresi?: string;
    sureTipi: string;
    bimAidiyatKod: string;
    canakNo?: string;
}

export interface ItkHedef {
    sorguTipi: string;
    hedefNo: string;
    karsiHedefNo?: string;
    baslangicTarihi: string;
    bitisTarihi: string;
    tespitTuru: string;
    tespitTuruDetay?: string;
    aciklama?: string;
}

export interface MahkemeAidiyat {
    aidiyatKod: string;
}

export interface MahkemeSucTipi {
    sucTipKod: string;
}

/**
 * Karar tipleri
 * @description Karar tipleri enum tanımı
 * @enum {string}
 * @property {string} ONLEYICI_HAKIM_KARARI - Önleyici hakim kararı
 * @property {string} ONLEYICI_YAZILI_EMIR - Önleyici yazılı emir
 * @property {string} ADLI_HAKIM_KARARI - Adli hakim kararı
 * @property {string} ADLI_YAZILI_EMIR - Adli yazılı emir
 * @property {string} ADLI_KHK_YAZILI_EMIR - Adli KHK yazılı emir
 * @property {string} BILGI_ALMA - Bilgi alma
 * @property {string} ONLEYICI_SONLANDIRMA - Önleyici sonlandırma
 * @property {string} ADLI_SONLANDIRMA - Adli sonlandırma
 * @property {string} ADLI_KHK_SONLANDIRMA - Adli KHK sonlandırma
 * @property {string} ADLI_ASKERI_HAKIM_KARARI - Adli askeri hakim kararı
 * @property {string} ADLI_ASKERI_SONLANDIRMA - Adli askeri sonlandırma
 * @property {string} ONLEYICI_HAKIM_T_KARAR - Önleyici hakim T karar
 */
export enum KararTipleri {
    ONLEYICI_HAKIM_KARARI = 'ONLEYICI_HAKIM_KARARI',
    ONLEYICI_YAZILI_EMIR = 'ONLEYICI_YAZILI_EMIR',
    ADLI_HAKIM_KARARI = 'ADLI_HAKIM_KARARI',
    ADLI_YAZILI_EMIR = 'ADLI_YAZILI_EMIR',
    ADLI_KHK_YAZILI_EMIR = 'ADLI_KHK_YAZILI_EMIR',
    BILGI_ALMA = 'BILGI_ALMA',
    ONLEYICI_SONLANDIRMA = 'ONLEYICI_SONLANDIRMA',
    ADLI_SONLANDIRMA = 'ADLI_SONLANDIRMA',
    ADLI_KHK_SONLANDIRMA = 'ADLI_KHK_SONLANDIRMA',
    ADLI_ASKERI_HAKIM_KARARI = 'ADLI_ASKERI_HAKIM_KARARI',
    ADLI_ASKERI_SONLANDIRMA = 'ADLI_ASKERI_SONLANDIRMA',
    ONLEYICI_HAKIM_T_KARAR = 'ONLEYICI_HAKIM_T_KARAR'
}

/**
 * Süre tipi
 * @description Süre tipi enum tanımı
 * @enum {string}
 * @property {string} GUN - Gün cinsinden süre
 * @property {string} AY - Ay cinsinden süre
 * @property {string} HICBIRI - Süre belirtilmemiş
 */
export enum SureTip {
    GUN = 'GUN',
    AY = 'AY',
    HICBIRI = 'HICBIRI'
}

/**
 * Hedef tipleri
 * @description Hedef tipleri enum tanımı
 * @enum {string}
 * @property {string} GSM - GSM hedef tipi
 * @property {string} SABIT - Sabit telefon hedef tipi
 * @property {string} UYDU - Uydu hedef tipi
 * @property {string} YURT_DISI - Yurt dışı hedef tipi
 * @property {string} UMTH_MSISDN - UMTH MSISDN hedef tipi
 * @property {string} UMTH_USERNAME - UMTH kullanıcı adı hedef tipi
 * @property {string} UMTH_IP - UMTH IP hedef tipi
 * @property {string} UMTH_PINCODE - UMTH pin kodu hedef tipi
 * @property {string} EPOSTA - E-posta hedef tipi
 * @property {string} IP_TAKIP - IP takip hedef tipi
 * @property {string} URL_WEB_ADRESI_TAKIP - URL/web adresi takip hedef tipi
 * @property {string} ADSL_ABONE_TAKIP - ADSL abone takip hedef tipi
 * @property {string} GPRS - GPRS hedef tipi
 * @property {string} IP_ENGELLEME - IP engelleme hedef tipi
 * @property {string} DOMAIN_ENGELLEME - Domain engelleme hedef tipi
 * @property {string} IMEI - IMEI hedef tipi
 * @property {string} IMSI - IMSI hedef tipi
 * @property {string} GPRS_IMSI - GPRS IMSI hedef tipi
 * @property {string} TT_XDSL_MSISDN - TT XDSL MSISDN hedef tipi
 * @property {string} TT_XDSL_TEMOSNO - TT XDSL temos numarası hedef tipi
 * @property {string} TT_XDSL_USERNAME - TT XDSL kullanıcı adı hedef tipi
 * @property {string} TT_XDSL_IP - TT XDSL IP hedef tipi
 * @property {string} GPRS_GSM - GPRS GSM hedef tipi
 * @property {string} GPRS_IMEI - GPRS IMEI hedef tipi
 * @property {string} GPRS_YURT_DISI - GPRS yurt dışı hedef tipi
 * @property {string} GSM_YER_TESPITI - GSM yer tespiti hedef tipi
 */
export enum HedefTipleri {
    GSM = 'GSM',
    SABIT = 'SABIT',
    UYDU = 'UYDU',
    YURT_DISI = 'YURT_DISI',
    UMTH_MSISDN = 'UMTH_MSISDN',
    UMTH_USERNAME = 'UMTH_USERNAME',
    UMTH_IP = 'UMTH_IP',
    UMTH_PINCODE = 'UMTH_PINCODE',
    EPOSTA = 'EPOSTA',
    IP_TAKIP = 'IP_TAKIP',
    URL_WEB_ADRESI_TAKIP = 'URL_WEB_ADRESI_TAKIP',
    ADSL_ABONE_TAKIP = 'ADSL_ABONE_TAKIP',
    GPRS = 'GPRS',
    IP_ENGELLEME = 'IP_ENGELLEME',
    DOMAIN_ENGELLEME = 'DOMAIN_ENGELLEME',
    IMEI = 'IMEI',
    IMSI = 'IMSI',
    GPRS_IMSI = 'GPRS_IMSI',
    TT_XDSL_MSISDN = 'TT_XDSL_MSISDN',
    TT_XDSL_TEMOSNO = 'TT_XDSL_TEMOSNO',
    TT_XDSL_USERNAME = 'TT_XDSL_USERNAME',
    TT_XDSL_IP = 'TT_XDSL_IP',
    GPRS_GSM = 'GPRS_GSM',
    GPRS_IMEI = 'GPRS_IMEI',
    GPRS_YURT_DISI = 'GPRS_YURT_DISI',
    GSM_YER_TESPITI = 'GSM_YER_TESPITI'
}

// Arama Filtreleri
export interface EvrakAramaFiltresi {
    evrakNo?: string;
    baslangicTarihi?: Date;
    bitisTarihi?: Date;
    mahkemeKodu?: string;
    evrakTipi?: string;
    kararTipi?: string;
    hedefNo?: string;
}

export interface ParametreAramaFiltresi {
    parametreAdi?: string;
    parametreGrubu?: string;
    aktifMi?: boolean;
}

// Sonuç Modelleri
export interface EvrakAramaSonucu {
    evrakNo: string;
    evrakTarihi: string;
    evrakTipi: string;
    mahkemeKodu: string;
    kararTipi: string;
    durumu: string;
    aciklama?: string;
}

export interface ParametreSonucu {
    id: number;
    parametreAdi: string;
    parametreDegeri: string;
    parametreGrubu: string;
    aciklama?: string;
    aktifMi: boolean;
    olusturmaTarihi: Date;
    guncellemeTarihi?: Date;
}

// XML İşlem Sonuçları
export interface XmlValidasyonSonucu {
    gecerliMi: boolean;
    hatalar: XmlHata[];
    uyarilar: XmlUyari[];
}

export interface XmlHata {
    satir: number;
    sutun: number;
    mesaj: string;
    seviye: 'ERROR' | 'WARNING' | 'INFO';
}

export interface XmlUyari {
    satir: number;
    sutun: number;
    mesaj: string;
    oneri?: string;
}

// Dosya Yükleme
export interface DosyaYuklemeResponse {
    basarili: boolean;
    dosyaAdi: string;
    dosyaBoyutu: number;
    yuklenmeTarihi: Date;
    hataMesaji?: string;
    validasyonSonucu?: XmlValidasyonSonucu;
}

// Sistem Parametreleri
export interface SistemParametresi {
    id: number;
    anahtar: string;
    deger: string;
    aciklama?: string;
    kategori: string;
    veriTipi: 'STRING' | 'NUMBER' | 'BOOLEAN' | 'DATE';
    zorunluMu: boolean;
    varsayilanDeger?: string;
}

// İstatistik Modelleri
export interface IymIstatistik {
    toplamEvrak: number;
    bekleyenEvrak: number;
    islenenEvrak: number;
    hatalıEvrak: number;
    bugunIslemler: number;
    sonIslemTarihi?: Date;
}

// Rapor Modelleri
export interface RaporParametresi {
    raporTipi: 'GUNLUK' | 'HAFTALIK' | 'AYLIK' | 'OZEL';
    baslangicTarihi: Date;
    bitisTarihi: Date;
    mahkemeKodu?: string;
    evrakTipi?: string;
    formatTipi: 'PDF' | 'EXCEL' | 'CSV';
}

export interface RaporSonucu {
    raporId: string;
    raporAdi: string;
    olusturmaTarihi: Date;
    dosyaYolu: string;
    dosyaBoyutu: number;
    indirmeLinki: string;
}

// İletişim tespiti modelleri
export interface IletisimTespitiFiltresi {
    evrakNo?: string;
    hedefBilgisi?: string;
    tespitiTuru?: string;
    durum?: string;
    baslangicTarihi?: Date;
    bitisTarihi?: Date;
}

export interface IletisimTespitiSonucu {
    id: number;
    evrakNo: string;
    mahkemeKodu: string;
    hedefBilgisi: string;
    tespitiTuru: string;
    durum: string;
    talepTarihi: Date;
    tamamlanmaTarihi?: Date;
    sonucBilgisi?: string;
    hataMesaji?: string;
    ilerlemeYuzdesi: number;
}

// // ============================================================================
// // RESPONSE WRAPPER - Backend Response Format
// // ============================================================================
// export interface Response<T> {
//     resultCode: string;
//     resultDetails: string | null;
//     exception: any | null;
//     result: T;
//     success: boolean;
// }
//
// export interface ValidationError {
//     field?: string;
//     message: string;
//     code?: string;
// }

// // ============================================================================
// // RESPONSE TYPES - Backend response structures
// // ============================================================================
// export interface IDYeniKararResponse {
//     requestId: string;
//     response: MakosApiResponse;
//     btkEvrakId: number;
// }
//
// export interface MakosApiResponse {
//     responseCode: string;
//     responseMessage: string;
// }

// ============================================================================
// GÜNCELLEME REQUEST'LERİ - Backend'e uygun nested yapılar
// ============================================================================

// Güncelleme Tipi Enum
export enum GuncellemeTip {
    EKLE = 'EKLE',
    CIKAR = 'CIKAR'
}

// // Aidiyat Güncelleme
// export interface HedefAidiyatDetay {
//     hedef: Hedef;
//     aidiyatKodu: string;
// }
//
// export interface AidiyatGuncellemeDetay {
//     guncellemeTip: GuncellemeTip;
//     hedefAidiyatDetay: HedefAidiyatDetay;
// }

// export interface AidiyatGuncellemeKararDetay {
//     mahkemeKararDetay: MahkemeKararDetay;
//     aidiyatGuncellemeDetayList: AidiyatGuncellemeDetay[];
// }

// export interface IDAidiyatBilgisiGuncellemeRequest extends MahkemeKararRequest {
//     aidiyatGuncellemeKararDetayListesi: AidiyatGuncellemeKararDetay[];
// }

// // Hedef Ad Soyad Güncelleme
// export interface HedefWithAdSoyad {
//     hedefNo: string;
//     ad: string;
//     soyad: string;
// }
//
// export interface HedefAdSoyadGuncellemeKararDetay {
//     mahkemeKararDetay: MahkemeKararDetay;
//     hedefAdSoyadListesi: HedefWithAdSoyad[];
// }

// export interface IDHedefAdSoyadGuncellemeRequest extends MahkemeKararRequest {
//     hedefAdSoyadGuncellemeKararDetayListesi: HedefAdSoyadGuncellemeKararDetay[];
// }

// // Mahkeme Kodu Güncelleme
// export interface MahkemeKoduGuncellemeDetay {
//     mahkemeKararDetay: MahkemeKararDetay;
//     yeniMahkemeKodu: string;
// }

// export interface IDMahkemeKoduGuncellemeRequest extends MahkemeKararRequest {
//     mahkemeKoduGuncellemeDetayListesi: MahkemeKoduGuncellemeDetay[];
// }

// // Çanak Güncelleme
// export interface CanakHedefDetay {
//     hedef: Hedef;
//     canakHedefNo: string;
// }
//
// export interface CanakGuncellemeDetay {
//     guncellemeTip: GuncellemeTip;
//     canakHedefDetay: CanakHedefDetay;
// }
//
// export interface CanakGuncellemeKararDetay {
//     mahkemeKararDetay: MahkemeKararDetay;
//     canakGuncellemeDetayList: CanakGuncellemeDetay[];
// }

// export interface IDCanakGuncellemeRequest extends MahkemeKararRequest {
//     canakGuncellemeKararDetayListesi: CanakGuncellemeKararDetay[];
// }
