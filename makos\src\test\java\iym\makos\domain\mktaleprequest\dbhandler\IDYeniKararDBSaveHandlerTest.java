package iym.makos.domain.mktaleprequest.dbhandler;

import iym.common.model.entity.iym.talep.HedeflerAidiyatTalep;
import iym.common.model.entity.iym.talep.HedeflerTalep;
import iym.common.model.entity.iym.talep.MahkemeAidiyatTalep;
import iym.common.model.entity.iym.talep.MahkemeSuclarTalep;
import iym.common.service.db.DbHedeflerTalepService;
import iym.common.service.db.mktalep.DbHedeflerAidiyatTalepService;
import iym.common.service.db.mktalep.DbMahkemeAidiyatTalepService;
import iym.common.service.db.mktalep.DbMahkemeSuclarTalepService;
import iym.makos.domain.base.BaseDomainUnitTest;
import iym.makos.domain.mktalep.requestprocessor.dbhandler.IDYeniKararDBSaveHandler;
import iym.makos.domain.mktalep.requestprocessor.dbhandler.MahkemeKararRequestCommonDbSaver;
import iym.makos.errors.MakosResponseErrorCodes;
import iym.makos.errors.MakosResponseException;
import iym.makos.mapper.KararRequestMapper;
import iym.makos.model.api.IDHedefDetay;
import iym.makos.model.dto.mktalep.request.id.IDYeniKararRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.time.LocalDateTime;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.*;


@DisplayName("IDYeniKararDBSaveHandlerTest Unit Tests")
public class IDYeniKararDBSaveHandlerTest extends BaseDomainUnitTest {

    @Mock
    private DbHedeflerTalepService dbHedeflerTalepService;
    @Mock
    private DbHedeflerAidiyatTalepService dbHedeflerAidiyatTalepService;
    @Mock
    private KararRequestMapper kararRequestMapper;
    @Mock
    private DbMahkemeAidiyatTalepService dbMahkemeAidiyatTalepService;
    @Mock
    private DbMahkemeSuclarTalepService dbMahkemeSuclarTalepService;
    @Mock
    private MahkemeKararRequestCommonDbSaver mahkemeKararRequestCommonDbSaver;

    @InjectMocks
    private IDYeniKararDBSaveHandler dbSaveHandler;

    private IDYeniKararRequest testRequest;
    private LocalDateTime testDate;
    private Long testUserId;

    @BeforeEach
    void setUp() {
        testRequest = new IDYeniKararRequest();
        testRequest.setMahkemeAidiyatKodlari(List.of("A1", "A2"));
        testRequest.setMahkemeSucTipiKodlari(List.of("S1", "S2"));

        IDHedefDetay hedefDetay = IDHedefDetay.builder()
                .hedefAidiyatKodlari(List.of("HA1", "HA2"))
                .build();
        testRequest.setHedefDetayListesi(List.of(hedefDetay));

        testDate = LocalDateTime.now();
        testUserId = 42L;
    }

    @Test
    void kaydet_ShouldSaveAllEntitiesAndReturnId() {
        // GIVEN
        Long expectedTalepId = 100L;
        when(mahkemeKararRequestCommonDbSaver.handleDbSave(testRequest, testDate, testUserId))
                .thenReturn(expectedTalepId);

        when(dbMahkemeAidiyatTalepService.save(any(MahkemeAidiyatTalep.class)))
                .thenAnswer(x -> x.getArgument(0));
        when(dbMahkemeSuclarTalepService.save(any(MahkemeSuclarTalep.class)))
                .thenAnswer(x -> x.getArgument(0));

        HedeflerTalep hedefEntity = new HedeflerTalep();
        hedefEntity.setId(50L);
        when(kararRequestMapper.toHedeflerTalep(any(IDHedefDetay.class), eq(expectedTalepId), eq(testUserId), eq(testDate)))
                .thenReturn(hedefEntity);

        when(dbHedeflerTalepService.save(hedefEntity)).thenReturn(hedefEntity);
        when(dbHedeflerAidiyatTalepService.save(any(HedeflerAidiyatTalep.class)))
                .thenAnswer(x -> x.getArgument(0));

        // WHEN
        Long result = dbSaveHandler.kaydet(testRequest, testDate, testUserId);

        // THEN
        assertThat(result).isEqualTo(expectedTalepId);

        // Verify MahkemeAidiyatTalep ve SuclarTalep kaydedildi
        verify(dbMahkemeAidiyatTalepService, times(2)).save(any(MahkemeAidiyatTalep.class));
        verify(dbMahkemeSuclarTalepService, times(2)).save(any(MahkemeSuclarTalep.class));

        //verify method calls
        verify(dbHedeflerTalepService).save(hedefEntity);
        verify(dbHedeflerAidiyatTalepService, times(2)).save(any(HedeflerAidiyatTalep.class));
    }

    @Test
    void kaydet_ShouldThrowException_WhenHandleDbSaveReturnsNull() {
        // GIVEN
        when(mahkemeKararRequestCommonDbSaver.handleDbSave(testRequest, testDate, testUserId))
                .thenReturn(null);

        // WHEN & THEN
        assertThatThrownBy(() -> dbSaveHandler.kaydet(testRequest, testDate, testUserId))
                .isInstanceOf(MakosResponseException.class)
                .hasMessageContaining(MakosResponseErrorCodes.MKTALEP_KAYIT_HATASI);

        //verify method call
        verify(mahkemeKararRequestCommonDbSaver).handleDbSave(testRequest, testDate, testUserId);

    }


    @Test
    void kaydet_ShouldThrow_WhenMahkemeAidiyatKayitFails() {
        // GIVEN
        Long expectedTalepId = 100L;
        when(mahkemeKararRequestCommonDbSaver.handleDbSave(testRequest, testDate, testUserId))
                .thenReturn(expectedTalepId);

        // Mock successful aidiyat save to reach the suç tipi error
        MahkemeAidiyatTalep aidiyatEntity = new MahkemeAidiyatTalep();
        aidiyatEntity.setId(1L);
        when(dbMahkemeAidiyatTalepService.save(any(MahkemeAidiyatTalep.class)))
                .thenReturn(aidiyatEntity);

        // Make suç tipi save fail
        when(dbMahkemeSuclarTalepService.save(any(MahkemeSuclarTalep.class)))
                .thenReturn(null);

        // WHEN & THEN
        assertThatThrownBy(() -> dbSaveHandler.kaydet(testRequest, testDate, testUserId))
                .isInstanceOf(MakosResponseException.class)
                .hasMessageContaining("Mahkeme karar talep suç tipi kaydetme hatası");

        //verify method calls
        verify(mahkemeKararRequestCommonDbSaver).handleDbSave(testRequest, testDate, testUserId);
        verify(dbMahkemeAidiyatTalepService, times(2)).save(any(MahkemeAidiyatTalep.class)); // All 2 aidiyat calls succeed
        verify(dbMahkemeSuclarTalepService, times(1)).save(any(MahkemeSuclarTalep.class)); // Only 1 call before exception
    }

    @Test
    void kaydet_ShouldThrow_WhenAidiyatKayitActuallyFails() {
        // GIVEN
        Long expectedTalepId = 100L;
        when(mahkemeKararRequestCommonDbSaver.handleDbSave(testRequest, testDate, testUserId))
                .thenReturn(expectedTalepId);

        // Make aidiyat save fail (this is the first save operation)
        when(dbMahkemeAidiyatTalepService.save(any(MahkemeAidiyatTalep.class)))
                .thenReturn(null);

        // WHEN & THEN
        assertThatThrownBy(() -> dbSaveHandler.kaydet(testRequest, testDate, testUserId))
                .isInstanceOf(MakosResponseException.class)
                .hasMessageContaining("Mahkeme karar talep aidiyat kaydetme hatası");

        verify(mahkemeKararRequestCommonDbSaver).handleDbSave(testRequest, testDate, testUserId);
        verify(dbMahkemeAidiyatTalepService, times(1)).save(any(MahkemeAidiyatTalep.class)); // Only 1 call before exception
    }

    @Test
    void kaydet_ShouldThrow_WhenHedefAidiyatKayitFails() {
        // GIVEN
        Long expectedTalepId = 100L;
        when(mahkemeKararRequestCommonDbSaver.handleDbSave(testRequest, testDate, testUserId))
                .thenReturn(expectedTalepId);

        // Mock successful mahkeme aidiyat saves (2 calls for A1, A2)
        MahkemeAidiyatTalep aidiyatEntity = new MahkemeAidiyatTalep();
        aidiyatEntity.setId(1L);
        when(dbMahkemeAidiyatTalepService.save(any(MahkemeAidiyatTalep.class)))
                .thenReturn(aidiyatEntity);

        // Mock successful mahkeme suç saves (2 calls for S1, S2)
        MahkemeSuclarTalep sucEntity = new MahkemeSuclarTalep();
        sucEntity.setId(2L);
        when(dbMahkemeSuclarTalepService.save(any(MahkemeSuclarTalep.class)))
                .thenReturn(sucEntity);

        // Mock successful hedefler talep save
        HedeflerTalep hedefEntity = new HedeflerTalep();
        hedefEntity.setId(50L);
        when(kararRequestMapper.toHedeflerTalep(any(), anyLong(), anyLong(), any()))
                .thenReturn(hedefEntity);
        when(dbHedeflerTalepService.save(any(HedeflerTalep.class)))
                .thenReturn(hedefEntity);

        // Make hedefler aidiyat save fail
        when(dbHedeflerAidiyatTalepService.save(any(HedeflerAidiyatTalep.class)))
                .thenReturn(null);

        // WHEN & THEN
        assertThatThrownBy(() -> dbSaveHandler.kaydet(testRequest, testDate, testUserId))
                .isInstanceOf(MakosResponseException.class)
                .hasMessageContaining("Mahkeme karar talep hedefler aidiyat kaydetme hatası");

        //verify method calls
        verify(mahkemeKararRequestCommonDbSaver).handleDbSave(testRequest, testDate, testUserId);
        verify(dbMahkemeAidiyatTalepService, times(2)).save(any(MahkemeAidiyatTalep.class));
        verify(dbMahkemeSuclarTalepService, times(2)).save(any(MahkemeSuclarTalep.class));
        verify(kararRequestMapper).toHedeflerTalep(any(), anyLong(), anyLong(), any());
        verify(dbHedeflerTalepService).save(any(HedeflerTalep.class));
        verify(dbHedeflerAidiyatTalepService, times(1)).save(any(HedeflerAidiyatTalep.class)); // Only 1 call before exception

    }

}


