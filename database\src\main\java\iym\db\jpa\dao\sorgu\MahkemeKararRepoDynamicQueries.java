package iym.db.jpa.dao.sorgu;

import iym.common.model.entity.iym.mk.sorgu.MahkemeKararSorguInfo;
import iym.common.model.entity.iym.mk.sorgu.MahkemeKararSorguParam;

import java.util.List;


public interface MahkemeKararRepoDynamicQueries {

    List<MahkemeKararSorguInfo> mahkemeKararSorgu(String kurumKodu, MahkemeKararSorguParam sorguParam);

    List<MahkemeKararSorguInfo> islenecekKararListesi(String kurumKodu);
}