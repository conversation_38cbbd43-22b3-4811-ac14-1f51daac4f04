package iym.common.service.db.mkislem;

import iym.common.model.entity.iym.mkislem.MahkemeSuclarIslem;
import iym.common.service.db.GenericDbService;

import java.util.List;
import java.util.Optional;

/**
 * Service interface for MahkemeSuclarIslem entity
 */
public interface DbMahkemeSuclarIslemService extends GenericDbService<MahkemeSuclarIslem, Long> {
    //mahkemeKararIslemId
    List<MahkemeSuclarIslem> findByMahkemeKararIslemId(Long mahkemeKararIslemId);

    Optional<MahkemeSuclarIslem> findByMahkemeKararIdAndSucTipKodu(Long mahkemeKararId, String sucTipKodu);

}
