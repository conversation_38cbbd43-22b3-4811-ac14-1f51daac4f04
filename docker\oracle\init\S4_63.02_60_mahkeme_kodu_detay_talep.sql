-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create sequence for MAHKEME_KODU_DETAY_TALEP_SEQ if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = 'MAH<PERSON><PERSON>_KODU_DETAY_TALEP_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.MAHKEME_KODU_DETAY_TALEP_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

-- Create MAHKEME_AIDIYAT_TALEP table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'MAHKEME_KODU_DETAY_TALEP';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.MAHKEME_KODU_DETAY_TALEP (
      ID NUMBER NOT NULL,
      <PERSON>H<PERSON><PERSON>_KARAR_DETAY_ID NUMBER NOT NULL,
      <PERSON>H<PERSON>ME_KODU VARCHAR2(25 BYTE) NOT NULL,
      MAHKEME_ADI VARCHAR2(250 BYTE) NOT NULL,
      DURUMU VARCHAR2(15 BYTE),
      CONSTRAINT MAHKEME_KODU_DETAY_TALEP_IDX PRIMARY KEY (ID) ENABLE
    )';

  END IF;
END;
/


COMMIT;
