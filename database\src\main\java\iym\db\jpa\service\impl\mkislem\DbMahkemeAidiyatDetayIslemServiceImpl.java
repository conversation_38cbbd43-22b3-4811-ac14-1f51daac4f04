package iym.db.jpa.service.impl.mkislem;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.mkislem.MahkemeAidiyatDetayIslem;
import iym.common.service.db.mkislem.DbMahkemeAidiyatDetayIslemService;
import iym.db.jpa.dao.mkislem.MahkemeAidiyatDetayIslemRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Service implementation for MahkemeAidiyatDetayIslem entity
 */
@Service
public class DbMahkemeAidiyatDetayIslemServiceImpl extends GenericDbServiceImpl<MahkemeAidiyatDetayIslem, Long> implements DbMahkemeAidiyatDetayIslemService {

    private final MahkemeAidiyatDetayIslemRepo mahkemeAidiyatDetayIslemRepo;

    @Autowired
    public DbMahkemeAidiyatDetayIslemServiceImpl(MahkemeAidiyatDetayIslemRepo repository) {
        super(repository);
        this.mahkemeAidiyatDetayIslemRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public List<MahkemeAidiyatDetayIslem> findByMahkemeKararIslemId(Long mahkemeKararIslemId){
        return mahkemeAidiyatDetayIslemRepo.findByMahkemeKararIslemId(mahkemeKararIslemId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MahkemeAidiyatDetayIslem> findByMahkemeKararDetayIslemId(Long mahkemeKararDetayIslemId){
        return  mahkemeAidiyatDetayIslemRepo.findByDetayMahkemeKararIslemId(mahkemeKararDetayIslemId);
    }

}
