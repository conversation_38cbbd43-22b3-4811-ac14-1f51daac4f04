package iym.common.service.file;

import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

@Slf4j
public class FilePersister {


    private static class Singleton{
        private static final FilePersister INSTANCE = new FilePersister();
    }

    public static FilePersister getInstance(){
        return Singleton.INSTANCE;
    }

    public boolean saveFileToDisk(String directory, String fileName, byte[] fileContent) {
        try {

            // create path for the directory
            Files.createDirectories(Path.of(directory));

            // Persist file to disk
            Path filePath = Paths.get(directory + File.separator + fileName);
            Files.write(filePath, fileContent);

            return true;
        } catch (IOException e) {
            log.error("saveFileToDisk failed. fileName:{}", fileName, e);
            return false;
        }
    }

    public boolean saveFileToDisk(String filePath, byte[] fileContent) {
        try {

            Path path = Path.of(filePath);

            // create path for the parent directory
            Files.createDirectories(path.getParent());

            // Persist file to disk
            Files.write(path, fileContent);

            return true;
        } catch (IOException e) {
            log.error("saveFileToDisk failed. filePath:{}", filePath, e);
            return false;
        }
    }

}
