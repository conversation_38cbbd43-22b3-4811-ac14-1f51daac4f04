package iym.common.service.db;

import iym.common.model.entity.iym.Iller;

import java.util.List;
import java.util.Optional;

/**
 * Service interface for Iller entity
 */
public interface DbIllerService extends GenericDbService<Iller, String> {

    Optional<Iller> findByIlIlceKodu(String ilIlceKodu);

    List<Iller> findByIlKodStartingWith(String ilKodPrefix);

    List<Iller> findByIlAdi(String ilAdi);


}
