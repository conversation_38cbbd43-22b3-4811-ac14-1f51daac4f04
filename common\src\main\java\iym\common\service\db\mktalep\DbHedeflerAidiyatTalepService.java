package iym.common.service.db.mktalep;

import iym.common.model.entity.iym.talep.HedeflerAidiyatTalep;
import iym.common.service.db.GenericDbService;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Service interface for HedeflerAidiyatTalep entity
 */
public interface DbHedeflerAidiyatTalepService extends GenericDbService<HedeflerAidiyatTalep, Long> {

    Optional<HedeflerAidiyatTalep> findById(Long id);

    List<HedeflerAidiyatTalep> findByHedeflerTalepId(Long hedefTalepId);

    List<HedeflerAidiyatTalep> findByTarihBetween(LocalDateTime startDate, LocalDateTime endDate);
    
    Optional<HedeflerAidiyatTalep> findByHedefTalepIdAndAidiyatKod(Long hedefId, String aidiyatKod);
    

}
