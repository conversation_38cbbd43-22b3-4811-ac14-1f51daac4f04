package iym.db.jpa.dao;

import iym.common.model.entity.iym.SorguTipleri;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Repository interface for SorguTipi entity
 */
@Repository
public interface SorguTipleriRepo extends JpaRepository<SorguTipleri, Long> {

    Optional<SorguTipleri> findBySorguTipi(Long sorguTipi);

}
