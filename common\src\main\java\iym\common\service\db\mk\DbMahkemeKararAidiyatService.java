package iym.common.service.db.mk;

import iym.common.model.entity.iym.mk.MahkemeAidiyat;
import iym.common.service.db.GenericDbService;

import java.util.List;
import java.util.Optional;

/**
 * Service interface for MahkemeKararAidiyat entity
 */
public interface DbMahkemeKararAidiyatService extends GenericDbService<MahkemeAidiyat, Long> {

    List<MahkemeAidiyat> findByMahkemeKararId(Long mahkemeKararId);

    Optional<MahkemeAidiyat> findByMahkemeKararIdAndAidiyatKod(Long mahkemeKararId, String aidiyatKod);
}
