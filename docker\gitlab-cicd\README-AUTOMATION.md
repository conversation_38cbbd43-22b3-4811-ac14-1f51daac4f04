# GitLab CI/CD Otomatik Kurulum Rehberi

Bu rehber, yeni yazılımcıların GitLab CI/CD ortamını tamamen otomatik olarak kurmasını sağlar.

## 🚀 Hızlı Başlangıç (Yeni Yazılımcılar İçin)

### Ö<PERSON>ere<PERSON>
- Windows 11
- Docker Desktop (çalışır durumda)
- Git
- PowerShell

### 1. <PERSON>k Komutl<PERSON>

```powershell
# Ana proje dizinine git
cd C:\temp\iym

# GitLab ve Runner'ı başlat
cd docker\gitlab-cicd
docker-compose up -d

# GitLab'ın hazır olmasını bekle (3-5 dakika)
# Sonra otomatik kurulum scriptini çalıştır
.\setup-new-developer.ps1
```

### 2. Proje Entegrasyonu

```powershell
# Proje otomatik kurulum scriptini çalıştır
.\setup-project-auto.ps1
```

## 📋 <PERSON> (Gerekirse)

### GitLab Erişim Bilgileri
- **URL:** http://localhost:8929
- **Username:** root
- **Password:** Script çalıştırıldığında otomatik gösterilir

### Registration Token Alma
1. http://localhost:8929/admin/runners adresine git
2. "Register an instance runner" butonuna tıkla
3. Registration token'ı kopyala

### GitLab'da Proje Oluşturma (Detaylı)
1. http://localhost:8929 adresine gidin
2. Username: `root`, Password: (script çalıştırıldığında gösterilir)
3. **New project** butonuna tıklayın
4. **Create blank project** seçin

**📝 Proje Ayarları:**
- **Project name:** `iym`
- **Project slug:** `iym` (otomatik doldurulur)
- **Visibility Level:** `Private` ✅
- **Project Configuration:**
  - ❌ Initialize repository with a README (UNCHECKED)
  - ❌ Enable Static Application Security Testing (UNCHECKED)
  - ❌ Enable Dynamic Application Security Testing (UNCHECKED)

⚠️ **ÖNEMLİ:** Tüm checkboxlar boş olmalı, aksi halde mevcut repo push edilemez!

5. **Create project** butonuna tıklayın

### Git Remote Ekleme
```bash
# Script otomatik olarak yapar, manuel gerekmiyor
git remote add gitlab http://localhost:8929/root/iym.git
git push gitlab main
```

## 🔄 Pipeline Durumu Kontrol

### Web Arayüzü
- **Pipelines:** http://localhost:8929/root/iym/-/pipelines
- **Container Registry:** http://localhost:5050

### Terminal Kontrol
```powershell
# Runner durumu
docker exec gitlab-cicd-runner-1 gitlab-runner list

# GitLab servisleri
docker exec gitlab-cicd-web-1 gitlab-ctl status

# Container'lar
docker-compose ps
```

## 🐳 Docker İmajları

Pipeline başarılı olduğunda şu imajlar oluşturulur:
- `localhost:5050/iym/backend:latest`
- `localhost:5050/iym/makos:latest`
- `localhost:5050/iym/frontend:latest`

## 🔧 Sorun Giderme

### GitLab Başlatma Sorunları
```powershell
# Container'ları yeniden başlat
docker-compose down
docker-compose up -d

# Logları kontrol et
docker-compose logs web
docker-compose logs runner
```

### Runner Kayıt Sorunları
```powershell
# Runner'ı yeniden kaydet
docker exec gitlab-cicd-runner-1 gitlab-runner unregister --all-runners
# Sonra setup-new-developer.ps1 scriptini tekrar çalıştır
```

### Push Sorunları
```powershell
# Git credential'ları temizle
git config --global credential.helper ""
git config --global http.sslVerify false

# Remote'u güncelle (password ile)
git remote set-url gitlab ***********************************/root/iym.git
```

## 📊 Pipeline Aşamaları

1. **Build Stage**
   - Backend (Maven)
   - Frontend (Node.js)

2. **Test Stage**
   - Unit testler
   - Test raporları

3. **Docker Stage**
   - Backend Docker imajı
   - Makos Docker imajı
   - Frontend Docker imajı

4. **Deploy Stage** (Manuel)
   - Development ortamı
   - Production ortamı

## 🎯 Başarı Kriterleri

✅ GitLab ve Runner çalışıyor
✅ Proje GitLab'da oluşturuldu
✅ Pipeline başarıyla çalıştı
✅ Docker imajları oluşturuldu
✅ Container Registry'de imajlar görünüyor

## 🔄 Günlük Kullanım

Artık her kod değişikliğinde:
1. `git add .`
2. `git commit -m "Your message"`
3. `git push gitlab branch-name`
4. Pipeline otomatik başlar
5. http://localhost:8929/root/iym/-/pipelines adresinden izle

## 🎉 Tebrikler!

Artık tamamen local GitLab CI/CD ortamınız hazır! 
Her commit'te otomatik olarak:
- Kod derlenir
- Testler çalışır
- Docker imajları oluşturulur
- Artifactlar saklanır

**Yeni yazılımcılar sadece bu README'yi takip ederek 10 dakikada ortamı kurabilir!**
