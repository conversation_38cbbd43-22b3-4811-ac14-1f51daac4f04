package iym.common.service.db.mk;

import iym.common.model.entity.iym.mk.MahkemeAidiyatDetay;
import iym.common.service.db.GenericDbService;

import java.util.List;

public interface DbMahkemeAidiyatDetayService extends GenericDbService<MahkemeAidiyatDetay, Long> {

    List<MahkemeAidiyatDetay> findByMahkemeKararId(Long mahkemeKararId);
    List<MahkemeAidiyatDetay> findByMahkemeKararDetayId(Long mahkemeKararDetayId);
}
