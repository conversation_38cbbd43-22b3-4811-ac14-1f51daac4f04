-- Create schema and grant privileges

-- Enable DBMS_OUTPUT for debugging
SET SERVEROUTPUT ON;

BEGIN
  DBMS_OUTPUT.PUT_LINE('=== STARTING: 01.00_create_schema.sql ===');
  DBMS_OUTPUT.PUT_LINE('Creating tablespace, user, and granting privileges...');
END;
/

-- Create tablespace for IYM
CREATE TABLESPACE iym_data
DATAFILE 'iym_data.dbf' SIZE 100M
AUTOEXTEND ON NEXT 50M MAXSIZE UNLIMITED;

-- Create user/schema if not exists
DECLARE
  user_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO user_exists FROM dba_users WHERE username = 'IYM';
  IF user_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE USER iym IDENTIFIED BY iym DEFAULT TABLESPACE iym_data QUOTA UNLIMITED ON iym_data';
  END IF;
END;
/

-- Grant necessary privileges
DECLARE
  user_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO user_exists FROM dba_users WHERE username = 'IYM';
  IF user_exists > 0 THEN
    EXECUTE IMMEDIATE 'GRANT CREATE SESSION TO iym';
    EXECUTE IMMEDIATE 'GRANT CREATE TABLE TO iym';
    EXECUTE IMMEDIATE 'GRANT CREATE VIEW TO iym';
    EXECUTE IMMEDIATE 'GRANT CREATE SEQUENCE TO iym';
    EXECUTE IMMEDIATE 'GRANT CREATE PROCEDURE TO iym';
    EXECUTE IMMEDIATE 'GRANT CREATE TRIGGER TO iym';
    EXECUTE IMMEDIATE 'GRANT CREATE TYPE TO iym';
    EXECUTE IMMEDIATE 'GRANT CREATE SYNONYM TO iym';
    EXECUTE IMMEDIATE 'GRANT UNLIMITED TABLESPACE TO iym';
  END IF;
END;
/

-- Connect as IYM user for further operations
CONNECT iym/iym@//localhost:1521/XE;

-- Create sequence for KULLANICI_GOREV2
CREATE SEQUENCE iym.KULLANICI_GOREV2_SEQ
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE;

-- Create sequence for EVRAK_GELEN_KURUMLAR
CREATE SEQUENCE iym.EVRAK_GELEN_KURUMLAR_SEQ
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE;

-- Create sequence for KULLANICI_KURUM
CREATE SEQUENCE iym.KULLANICI_KURUM_SEQ
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE;

BEGIN
  DBMS_OUTPUT.PUT_LINE('=== COMPLETED: 01.00_create_schema.sql ===');
  DBMS_OUTPUT.PUT_LINE('Schema, user, and sequences created successfully');
END;
/
