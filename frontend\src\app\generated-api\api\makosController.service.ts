/**
 * IYM Backend OpenAPI definition
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
/* tslint:disable:no-unused-variable member-ordering */

import { Inject, Injectable, Optional }                      from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams,
         HttpResponse, HttpEvent, HttpParameterCodec, HttpContext 
        }       from '@angular/common/http';
import { CustomHttpParameterCodec }                          from '../encoder';
import { Observable }                                        from 'rxjs';

// @ts-ignore
import { HealthCheckResponse } from '../model/healthCheckResponse';
// @ts-ignore
import { IDAidiyatBilgisiGuncellemeRequest } from '../model/iDAidiyatBilgisiGuncellemeRequest';
// @ts-ignore
import { IDHedefGuncellemeRequest } from '../model/iDHedefGuncellemeRequest';
// @ts-ignore
import { IDMahkemeKararGuncellemeRequest } from '../model/iDMahkemeKararGuncellemeRequest';
// @ts-ignore
import { IDMahkemeKararGuncellemeResponse } from '../model/iDMahkemeKararGuncellemeResponse';
// @ts-ignore
import { IDMahkemeKararTalepIslenecekRequest } from '../model/iDMahkemeKararTalepIslenecekRequest';
// @ts-ignore
import { IDMahkemeKararTalepSorgulamaRequest } from '../model/iDMahkemeKararTalepSorgulamaRequest';
// @ts-ignore
import { IDSonlandirmaKarariRequest } from '../model/iDSonlandirmaKarariRequest';
// @ts-ignore
import { IDSucTipiGuncellemeRequest } from '../model/iDSucTipiGuncellemeRequest';
// @ts-ignore
import { IDUzatmaKarariRequest } from '../model/iDUzatmaKarariRequest';
// @ts-ignore
import { IDYeniKararRequest } from '../model/iDYeniKararRequest';
// @ts-ignore
import { ITKararRequest } from '../model/iTKararRequest';
// @ts-ignore
import { ITKararResponse } from '../model/iTKararResponse';
// @ts-ignore
import { MahkemeKararTalepBilgisiRequest } from '../model/mahkemeKararTalepBilgisiRequest';
// @ts-ignore
import { MahkemeKararTalepStateUpdateRequest } from '../model/mahkemeKararTalepStateUpdateRequest';
// @ts-ignore
import { ResponseEvrakGelenKurumlarResponse } from '../model/responseEvrakGelenKurumlarResponse';
// @ts-ignore
import { ResponseIDAidiyatBilgisiGuncellemeResponse } from '../model/responseIDAidiyatBilgisiGuncellemeResponse';
// @ts-ignore
import { ResponseIDHedefGuncellemeResponse } from '../model/responseIDHedefGuncellemeResponse';
// @ts-ignore
import { ResponseIDMahkemeKararTalepIslenecekResponse } from '../model/responseIDMahkemeKararTalepIslenecekResponse';
// @ts-ignore
import { ResponseIDMahkemeKararTalepSorgulamaResponse } from '../model/responseIDMahkemeKararTalepSorgulamaResponse';
// @ts-ignore
import { ResponseIDSonlandirmaKarariResponse } from '../model/responseIDSonlandirmaKarariResponse';
// @ts-ignore
import { ResponseIDSucTipiGuncellemeResponse } from '../model/responseIDSucTipiGuncellemeResponse';
// @ts-ignore
import { ResponseIDUzatmaKarariResponse } from '../model/responseIDUzatmaKarariResponse';
// @ts-ignore
import { ResponseIDYeniKararResponse } from '../model/responseIDYeniKararResponse';
// @ts-ignore
import { ResponseIllerResponse } from '../model/responseIllerResponse';
// @ts-ignore
import { ResponseMahkemeKararTalepQueryResponse } from '../model/responseMahkemeKararTalepQueryResponse';
// @ts-ignore
import { ResponseMahkemeKararTalepStateUpdateResponse } from '../model/responseMahkemeKararTalepStateUpdateResponse';
// @ts-ignore
import { ResponseMahkemeKararTipleriResponse } from '../model/responseMahkemeKararTipleriResponse';
// @ts-ignore
import { ResponseMahkemeKodlariResponse } from '../model/responseMahkemeKodlariResponse';
// @ts-ignore
import { ResponseSorguTipiListResponse } from '../model/responseSorguTipiListResponse';
// @ts-ignore
import { ResponseSucTipleriResponse } from '../model/responseSucTipleriResponse';
// @ts-ignore
import { ResponseTespitTuruListResponse } from '../model/responseTespitTuruListResponse';

// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS }                     from '../variables';
import { Configuration }                                     from '../configuration';
import { BaseService } from '../api.base.service';



@Injectable({
  providedIn: 'root'
})
export class MakosControllerService extends BaseService {

    constructor(protected httpClient: HttpClient, @Optional() @Inject(BASE_PATH) basePath: string|string[], @Optional() configuration?: Configuration) {
        super(basePath, configuration);
    }

    /**
     * @param mahkemeKararDosyasi 
     * @param mahkemeKararDetay 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public aidiyatBilgisiGuncelle(mahkemeKararDosyasi: Blob, mahkemeKararDetay: IDAidiyatBilgisiGuncellemeRequest, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<ResponseIDAidiyatBilgisiGuncellemeResponse>;
    public aidiyatBilgisiGuncelle(mahkemeKararDosyasi: Blob, mahkemeKararDetay: IDAidiyatBilgisiGuncellemeRequest, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<ResponseIDAidiyatBilgisiGuncellemeResponse>>;
    public aidiyatBilgisiGuncelle(mahkemeKararDosyasi: Blob, mahkemeKararDetay: IDAidiyatBilgisiGuncellemeRequest, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<ResponseIDAidiyatBilgisiGuncellemeResponse>>;
    public aidiyatBilgisiGuncelle(mahkemeKararDosyasi: Blob, mahkemeKararDetay: IDAidiyatBilgisiGuncellemeRequest, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (mahkemeKararDosyasi === null || mahkemeKararDosyasi === undefined) {
            throw new Error('Required parameter mahkemeKararDosyasi was null or undefined when calling aidiyatBilgisiGuncelle.');
        }
        if (mahkemeKararDetay === null || mahkemeKararDetay === undefined) {
            throw new Error('Required parameter mahkemeKararDetay was null or undefined when calling aidiyatBilgisiGuncelle.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (BearerAuth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('BearerAuth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;

        // to determine the Content-Type header
        const consumes: string[] = [
            'multipart/form-data'
        ];

        const canConsumeForm = this.canConsumeForm(consumes);

        let localVarFormParams: { append(param: string, value: any): any; };
        let localVarUseForm = false;
        let localVarConvertFormParamsToString = false;
        // use FormData to transmit files using content-type "multipart/form-data"
        // see https://stackoverflow.com/questions/4007969/application-x-www-form-urlencoded-or-multipart-form-data
        localVarUseForm = canConsumeForm;
        if (localVarUseForm) {
            localVarFormParams = new FormData();
        } else {
            localVarFormParams = new HttpParams({encoder: this.encoder});
        }

        if (mahkemeKararDosyasi !== undefined) {
            localVarFormParams = localVarFormParams.append('mahkemeKararDosyasi', <any>mahkemeKararDosyasi) as any || localVarFormParams;
        }
        if (mahkemeKararDetay !== undefined) {
            localVarFormParams = localVarFormParams.append('mahkemeKararDetay', localVarUseForm ? new Blob([JSON.stringify(mahkemeKararDetay)], {type: 'application/json'}) : <any>mahkemeKararDetay) as any || localVarFormParams;
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/api/makos/aidiyat-bilgisi-guncelle`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ResponseIDAidiyatBilgisiGuncellemeResponse>('post', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: localVarConvertFormParamsToString ? localVarFormParams.toString() : localVarFormParams,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public healthCheck(observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: '*/*', context?: HttpContext, transferCache?: boolean}): Observable<HealthCheckResponse>;
    public healthCheck(observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: '*/*', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<HealthCheckResponse>>;
    public healthCheck(observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: '*/*', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<HealthCheckResponse>>;
    public healthCheck(observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: '*/*', context?: HttpContext, transferCache?: boolean}): Observable<any> {

        let localVarHeaders = this.defaultHeaders;

        // authentication (BearerAuth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('BearerAuth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            '*/*'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/api/makos/health`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<HealthCheckResponse>('get', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public healthCheckAuthorized(observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: '*/*', context?: HttpContext, transferCache?: boolean}): Observable<HealthCheckResponse>;
    public healthCheckAuthorized(observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: '*/*', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<HealthCheckResponse>>;
    public healthCheckAuthorized(observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: '*/*', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<HealthCheckResponse>>;
    public healthCheckAuthorized(observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: '*/*', context?: HttpContext, transferCache?: boolean}): Observable<any> {

        let localVarHeaders = this.defaultHeaders;

        // authentication (BearerAuth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('BearerAuth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            '*/*'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/api/makos/healthCheckAuthorized`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<HealthCheckResponse>('get', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * @param mahkemeKararDosyasi 
     * @param mahkemeKararDetay 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public hedefBilgisiGuncelle(mahkemeKararDosyasi: Blob, mahkemeKararDetay: IDHedefGuncellemeRequest, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<ResponseIDHedefGuncellemeResponse>;
    public hedefBilgisiGuncelle(mahkemeKararDosyasi: Blob, mahkemeKararDetay: IDHedefGuncellemeRequest, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<ResponseIDHedefGuncellemeResponse>>;
    public hedefBilgisiGuncelle(mahkemeKararDosyasi: Blob, mahkemeKararDetay: IDHedefGuncellemeRequest, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<ResponseIDHedefGuncellemeResponse>>;
    public hedefBilgisiGuncelle(mahkemeKararDosyasi: Blob, mahkemeKararDetay: IDHedefGuncellemeRequest, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (mahkemeKararDosyasi === null || mahkemeKararDosyasi === undefined) {
            throw new Error('Required parameter mahkemeKararDosyasi was null or undefined when calling hedefBilgisiGuncelle.');
        }
        if (mahkemeKararDetay === null || mahkemeKararDetay === undefined) {
            throw new Error('Required parameter mahkemeKararDetay was null or undefined when calling hedefBilgisiGuncelle.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (BearerAuth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('BearerAuth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;

        // to determine the Content-Type header
        const consumes: string[] = [
            'multipart/form-data'
        ];

        const canConsumeForm = this.canConsumeForm(consumes);

        let localVarFormParams: { append(param: string, value: any): any; };
        let localVarUseForm = false;
        let localVarConvertFormParamsToString = false;
        // use FormData to transmit files using content-type "multipart/form-data"
        // see https://stackoverflow.com/questions/4007969/application-x-www-form-urlencoded-or-multipart-form-data
        localVarUseForm = canConsumeForm;
        if (localVarUseForm) {
            localVarFormParams = new FormData();
        } else {
            localVarFormParams = new HttpParams({encoder: this.encoder});
        }

        if (mahkemeKararDosyasi !== undefined) {
            localVarFormParams = localVarFormParams.append('mahkemeKararDosyasi', <any>mahkemeKararDosyasi) as any || localVarFormParams;
        }
        if (mahkemeKararDetay !== undefined) {
            localVarFormParams = localVarFormParams.append('mahkemeKararDetay', localVarUseForm ? new Blob([JSON.stringify(mahkemeKararDetay)], {type: 'application/json'}) : <any>mahkemeKararDetay) as any || localVarFormParams;
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/api/makos/hedef-bilgisi-guncelle`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ResponseIDHedefGuncellemeResponse>('post', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: localVarConvertFormParamsToString ? localVarFormParams.toString() : localVarFormParams,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public iller(observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<ResponseIllerResponse>;
    public iller(observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<ResponseIllerResponse>>;
    public iller(observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<ResponseIllerResponse>>;
    public iller(observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {

        let localVarHeaders = this.defaultHeaders;

        // authentication (BearerAuth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('BearerAuth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/api/makos/iller`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ResponseIllerResponse>('get', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * @param iDMahkemeKararTalepIslenecekRequest 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public islenecekKararListele(iDMahkemeKararTalepIslenecekRequest: IDMahkemeKararTalepIslenecekRequest, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<ResponseIDMahkemeKararTalepIslenecekResponse>;
    public islenecekKararListele(iDMahkemeKararTalepIslenecekRequest: IDMahkemeKararTalepIslenecekRequest, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<ResponseIDMahkemeKararTalepIslenecekResponse>>;
    public islenecekKararListele(iDMahkemeKararTalepIslenecekRequest: IDMahkemeKararTalepIslenecekRequest, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<ResponseIDMahkemeKararTalepIslenecekResponse>>;
    public islenecekKararListele(iDMahkemeKararTalepIslenecekRequest: IDMahkemeKararTalepIslenecekRequest, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (iDMahkemeKararTalepIslenecekRequest === null || iDMahkemeKararTalepIslenecekRequest === undefined) {
            throw new Error('Required parameter iDMahkemeKararTalepIslenecekRequest was null or undefined when calling islenecekKararListele.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (BearerAuth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('BearerAuth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/api/makos/islenecek-karar-listele`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ResponseIDMahkemeKararTalepIslenecekResponse>('post', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: iDMahkemeKararTalepIslenecekRequest,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public kurumlar(observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<ResponseEvrakGelenKurumlarResponse>;
    public kurumlar(observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<ResponseEvrakGelenKurumlarResponse>>;
    public kurumlar(observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<ResponseEvrakGelenKurumlarResponse>>;
    public kurumlar(observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {

        let localVarHeaders = this.defaultHeaders;

        // authentication (BearerAuth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('BearerAuth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/api/makos/kurumlar`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ResponseEvrakGelenKurumlarResponse>('get', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * @param mahkemeKararDetay 
     * @param mahkemeKararDosyasi 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public mahkemeBilgisiGuncelle(mahkemeKararDetay: IDMahkemeKararGuncellemeRequest, mahkemeKararDosyasi: Blob, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: '*/*', context?: HttpContext, transferCache?: boolean}): Observable<IDMahkemeKararGuncellemeResponse>;
    public mahkemeBilgisiGuncelle(mahkemeKararDetay: IDMahkemeKararGuncellemeRequest, mahkemeKararDosyasi: Blob, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: '*/*', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<IDMahkemeKararGuncellemeResponse>>;
    public mahkemeBilgisiGuncelle(mahkemeKararDetay: IDMahkemeKararGuncellemeRequest, mahkemeKararDosyasi: Blob, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: '*/*', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<IDMahkemeKararGuncellemeResponse>>;
    public mahkemeBilgisiGuncelle(mahkemeKararDetay: IDMahkemeKararGuncellemeRequest, mahkemeKararDosyasi: Blob, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: '*/*', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (mahkemeKararDetay === null || mahkemeKararDetay === undefined) {
            throw new Error('Required parameter mahkemeKararDetay was null or undefined when calling mahkemeBilgisiGuncelle.');
        }
        if (mahkemeKararDosyasi === null || mahkemeKararDosyasi === undefined) {
            throw new Error('Required parameter mahkemeKararDosyasi was null or undefined when calling mahkemeBilgisiGuncelle.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (BearerAuth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('BearerAuth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            '*/*'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;

        // to determine the Content-Type header
        const consumes: string[] = [
            'multipart/form-data'
        ];

        const canConsumeForm = this.canConsumeForm(consumes);

        let localVarFormParams: { append(param: string, value: any): any; };
        let localVarUseForm = false;
        let localVarConvertFormParamsToString = false;
        // use FormData to transmit files using content-type "multipart/form-data"
        // see https://stackoverflow.com/questions/4007969/application-x-www-form-urlencoded-or-multipart-form-data
        localVarUseForm = canConsumeForm;
        if (localVarUseForm) {
            localVarFormParams = new FormData();
        } else {
            localVarFormParams = new HttpParams({encoder: this.encoder});
        }

        if (mahkemeKararDetay !== undefined) {
            localVarFormParams = localVarFormParams.append('mahkemeKararDetay', localVarUseForm ? new Blob([JSON.stringify(mahkemeKararDetay)], {type: 'application/json'}) : <any>mahkemeKararDetay) as any || localVarFormParams;
        }
        if (mahkemeKararDosyasi !== undefined) {
            localVarFormParams = localVarFormParams.append('mahkemeKararDosyasi', <any>mahkemeKararDosyasi) as any || localVarFormParams;
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/api/makos/mahkeme-bilgisi-guncelle`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<IDMahkemeKararGuncellemeResponse>('post', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: localVarConvertFormParamsToString ? localVarFormParams.toString() : localVarFormParams,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * @param mahkemeKararTalepBilgisiRequest 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public mahkemeKararTalepBilgisi(mahkemeKararTalepBilgisiRequest: MahkemeKararTalepBilgisiRequest, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<ResponseMahkemeKararTalepQueryResponse>;
    public mahkemeKararTalepBilgisi(mahkemeKararTalepBilgisiRequest: MahkemeKararTalepBilgisiRequest, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<ResponseMahkemeKararTalepQueryResponse>>;
    public mahkemeKararTalepBilgisi(mahkemeKararTalepBilgisiRequest: MahkemeKararTalepBilgisiRequest, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<ResponseMahkemeKararTalepQueryResponse>>;
    public mahkemeKararTalepBilgisi(mahkemeKararTalepBilgisiRequest: MahkemeKararTalepBilgisiRequest, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (mahkemeKararTalepBilgisiRequest === null || mahkemeKararTalepBilgisiRequest === undefined) {
            throw new Error('Required parameter mahkemeKararTalepBilgisiRequest was null or undefined when calling mahkemeKararTalepBilgisi.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (BearerAuth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('BearerAuth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/api/makos/mahkeme-karar-talep-bilgisi`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ResponseMahkemeKararTalepQueryResponse>('post', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: mahkemeKararTalepBilgisiRequest,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * @param iDMahkemeKararTalepSorgulamaRequest 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public mahkemeKararTalepSorgu(iDMahkemeKararTalepSorgulamaRequest: IDMahkemeKararTalepSorgulamaRequest, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<ResponseIDMahkemeKararTalepSorgulamaResponse>;
    public mahkemeKararTalepSorgu(iDMahkemeKararTalepSorgulamaRequest: IDMahkemeKararTalepSorgulamaRequest, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<ResponseIDMahkemeKararTalepSorgulamaResponse>>;
    public mahkemeKararTalepSorgu(iDMahkemeKararTalepSorgulamaRequest: IDMahkemeKararTalepSorgulamaRequest, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<ResponseIDMahkemeKararTalepSorgulamaResponse>>;
    public mahkemeKararTalepSorgu(iDMahkemeKararTalepSorgulamaRequest: IDMahkemeKararTalepSorgulamaRequest, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (iDMahkemeKararTalepSorgulamaRequest === null || iDMahkemeKararTalepSorgulamaRequest === undefined) {
            throw new Error('Required parameter iDMahkemeKararTalepSorgulamaRequest was null or undefined when calling mahkemeKararTalepSorgu.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (BearerAuth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('BearerAuth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/api/makos/mahkeme-karar-talep-sorgu`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ResponseIDMahkemeKararTalepSorgulamaResponse>('post', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: iDMahkemeKararTalepSorgulamaRequest,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public mahkemeKararTipleri(observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<ResponseMahkemeKararTipleriResponse>;
    public mahkemeKararTipleri(observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<ResponseMahkemeKararTipleriResponse>>;
    public mahkemeKararTipleri(observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<ResponseMahkemeKararTipleriResponse>>;
    public mahkemeKararTipleri(observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {

        let localVarHeaders = this.defaultHeaders;

        // authentication (BearerAuth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('BearerAuth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/api/makos/mahkeme-karar-tipleri`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ResponseMahkemeKararTipleriResponse>('get', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public mahkemeKodlari(observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<ResponseMahkemeKodlariResponse>;
    public mahkemeKodlari(observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<ResponseMahkemeKodlariResponse>>;
    public mahkemeKodlari(observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<ResponseMahkemeKodlariResponse>>;
    public mahkemeKodlari(observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {

        let localVarHeaders = this.defaultHeaders;

        // authentication (BearerAuth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('BearerAuth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/api/makos/mahkeme-kodlari`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ResponseMahkemeKodlariResponse>('get', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * @param mahkemeKararDosyasi 
     * @param mahkemeKararDetay 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public sonlandirmaKarariID(mahkemeKararDosyasi: Blob, mahkemeKararDetay: IDSonlandirmaKarariRequest, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<ResponseIDSonlandirmaKarariResponse>;
    public sonlandirmaKarariID(mahkemeKararDosyasi: Blob, mahkemeKararDetay: IDSonlandirmaKarariRequest, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<ResponseIDSonlandirmaKarariResponse>>;
    public sonlandirmaKarariID(mahkemeKararDosyasi: Blob, mahkemeKararDetay: IDSonlandirmaKarariRequest, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<ResponseIDSonlandirmaKarariResponse>>;
    public sonlandirmaKarariID(mahkemeKararDosyasi: Blob, mahkemeKararDetay: IDSonlandirmaKarariRequest, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (mahkemeKararDosyasi === null || mahkemeKararDosyasi === undefined) {
            throw new Error('Required parameter mahkemeKararDosyasi was null or undefined when calling sonlandirmaKarariID.');
        }
        if (mahkemeKararDetay === null || mahkemeKararDetay === undefined) {
            throw new Error('Required parameter mahkemeKararDetay was null or undefined when calling sonlandirmaKarariID.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (BearerAuth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('BearerAuth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;

        // to determine the Content-Type header
        const consumes: string[] = [
            'multipart/form-data'
        ];

        const canConsumeForm = this.canConsumeForm(consumes);

        let localVarFormParams: { append(param: string, value: any): any; };
        let localVarUseForm = false;
        let localVarConvertFormParamsToString = false;
        // use FormData to transmit files using content-type "multipart/form-data"
        // see https://stackoverflow.com/questions/4007969/application-x-www-form-urlencoded-or-multipart-form-data
        localVarUseForm = canConsumeForm;
        if (localVarUseForm) {
            localVarFormParams = new FormData();
        } else {
            localVarFormParams = new HttpParams({encoder: this.encoder});
        }

        if (mahkemeKararDosyasi !== undefined) {
            localVarFormParams = localVarFormParams.append('mahkemeKararDosyasi', <any>mahkemeKararDosyasi) as any || localVarFormParams;
        }
        if (mahkemeKararDetay !== undefined) {
            localVarFormParams = localVarFormParams.append('mahkemeKararDetay', localVarUseForm ? new Blob([JSON.stringify(mahkemeKararDetay)], {type: 'application/json'}) : <any>mahkemeKararDetay) as any || localVarFormParams;
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/api/makos/sonlandirma-karari-id`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ResponseIDSonlandirmaKarariResponse>('post', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: localVarConvertFormParamsToString ? localVarFormParams.toString() : localVarFormParams,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public sorguTipleri(observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<ResponseSorguTipiListResponse>;
    public sorguTipleri(observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<ResponseSorguTipiListResponse>>;
    public sorguTipleri(observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<ResponseSorguTipiListResponse>>;
    public sorguTipleri(observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {

        let localVarHeaders = this.defaultHeaders;

        // authentication (BearerAuth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('BearerAuth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/api/makos/sorgu-tipleri`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ResponseSorguTipiListResponse>('get', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * @param mahkemeKararDosyasi 
     * @param mahkemeKararDetay 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public sucTipiGuncelle(mahkemeKararDosyasi: Blob, mahkemeKararDetay: IDSucTipiGuncellemeRequest, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<ResponseIDSucTipiGuncellemeResponse>;
    public sucTipiGuncelle(mahkemeKararDosyasi: Blob, mahkemeKararDetay: IDSucTipiGuncellemeRequest, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<ResponseIDSucTipiGuncellemeResponse>>;
    public sucTipiGuncelle(mahkemeKararDosyasi: Blob, mahkemeKararDetay: IDSucTipiGuncellemeRequest, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<ResponseIDSucTipiGuncellemeResponse>>;
    public sucTipiGuncelle(mahkemeKararDosyasi: Blob, mahkemeKararDetay: IDSucTipiGuncellemeRequest, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (mahkemeKararDosyasi === null || mahkemeKararDosyasi === undefined) {
            throw new Error('Required parameter mahkemeKararDosyasi was null or undefined when calling sucTipiGuncelle.');
        }
        if (mahkemeKararDetay === null || mahkemeKararDetay === undefined) {
            throw new Error('Required parameter mahkemeKararDetay was null or undefined when calling sucTipiGuncelle.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (BearerAuth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('BearerAuth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;

        // to determine the Content-Type header
        const consumes: string[] = [
            'multipart/form-data'
        ];

        const canConsumeForm = this.canConsumeForm(consumes);

        let localVarFormParams: { append(param: string, value: any): any; };
        let localVarUseForm = false;
        let localVarConvertFormParamsToString = false;
        // use FormData to transmit files using content-type "multipart/form-data"
        // see https://stackoverflow.com/questions/4007969/application-x-www-form-urlencoded-or-multipart-form-data
        localVarUseForm = canConsumeForm;
        if (localVarUseForm) {
            localVarFormParams = new FormData();
        } else {
            localVarFormParams = new HttpParams({encoder: this.encoder});
        }

        if (mahkemeKararDosyasi !== undefined) {
            localVarFormParams = localVarFormParams.append('mahkemeKararDosyasi', <any>mahkemeKararDosyasi) as any || localVarFormParams;
        }
        if (mahkemeKararDetay !== undefined) {
            localVarFormParams = localVarFormParams.append('mahkemeKararDetay', localVarUseForm ? new Blob([JSON.stringify(mahkemeKararDetay)], {type: 'application/json'}) : <any>mahkemeKararDetay) as any || localVarFormParams;
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/api/makos/suc-tipi-guncelle`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ResponseIDSucTipiGuncellemeResponse>('post', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: localVarConvertFormParamsToString ? localVarFormParams.toString() : localVarFormParams,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public sucTipleri(observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<ResponseSucTipleriResponse>;
    public sucTipleri(observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<ResponseSucTipleriResponse>>;
    public sucTipleri(observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<ResponseSucTipleriResponse>>;
    public sucTipleri(observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {

        let localVarHeaders = this.defaultHeaders;

        // authentication (BearerAuth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('BearerAuth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/api/makos/suc-tipleri`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ResponseSucTipleriResponse>('get', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * @param mahkemeKararTalepStateUpdateRequest 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public talepGuncelle(mahkemeKararTalepStateUpdateRequest: MahkemeKararTalepStateUpdateRequest, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<ResponseMahkemeKararTalepStateUpdateResponse>;
    public talepGuncelle(mahkemeKararTalepStateUpdateRequest: MahkemeKararTalepStateUpdateRequest, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<ResponseMahkemeKararTalepStateUpdateResponse>>;
    public talepGuncelle(mahkemeKararTalepStateUpdateRequest: MahkemeKararTalepStateUpdateRequest, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<ResponseMahkemeKararTalepStateUpdateResponse>>;
    public talepGuncelle(mahkemeKararTalepStateUpdateRequest: MahkemeKararTalepStateUpdateRequest, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (mahkemeKararTalepStateUpdateRequest === null || mahkemeKararTalepStateUpdateRequest === undefined) {
            throw new Error('Required parameter mahkemeKararTalepStateUpdateRequest was null or undefined when calling talepGuncelle.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (BearerAuth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('BearerAuth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        // to determine the Content-Type header
        const consumes: string[] = [
            'application/json'
        ];
        const httpContentTypeSelected: string | undefined = this.configuration.selectHeaderContentType(consumes);
        if (httpContentTypeSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Content-Type', httpContentTypeSelected);
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/api/makos/talep-guncelle`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ResponseMahkemeKararTalepStateUpdateResponse>('post', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: mahkemeKararTalepStateUpdateRequest,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public tespitTurleri(observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<ResponseTespitTuruListResponse>;
    public tespitTurleri(observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<ResponseTespitTuruListResponse>>;
    public tespitTurleri(observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<ResponseTespitTuruListResponse>>;
    public tespitTurleri(observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {

        let localVarHeaders = this.defaultHeaders;

        // authentication (BearerAuth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('BearerAuth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;


        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/api/makos/tespit-turleri`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ResponseTespitTuruListResponse>('get', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * @param mahkemeKararDosyasi 
     * @param mahkemeKararDetay 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public uzatmaKarariID(mahkemeKararDosyasi: Blob, mahkemeKararDetay: IDUzatmaKarariRequest, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<ResponseIDUzatmaKarariResponse>;
    public uzatmaKarariID(mahkemeKararDosyasi: Blob, mahkemeKararDetay: IDUzatmaKarariRequest, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<ResponseIDUzatmaKarariResponse>>;
    public uzatmaKarariID(mahkemeKararDosyasi: Blob, mahkemeKararDetay: IDUzatmaKarariRequest, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<ResponseIDUzatmaKarariResponse>>;
    public uzatmaKarariID(mahkemeKararDosyasi: Blob, mahkemeKararDetay: IDUzatmaKarariRequest, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (mahkemeKararDosyasi === null || mahkemeKararDosyasi === undefined) {
            throw new Error('Required parameter mahkemeKararDosyasi was null or undefined when calling uzatmaKarariID.');
        }
        if (mahkemeKararDetay === null || mahkemeKararDetay === undefined) {
            throw new Error('Required parameter mahkemeKararDetay was null or undefined when calling uzatmaKarariID.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (BearerAuth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('BearerAuth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;

        // to determine the Content-Type header
        const consumes: string[] = [
            'multipart/form-data'
        ];

        const canConsumeForm = this.canConsumeForm(consumes);

        let localVarFormParams: { append(param: string, value: any): any; };
        let localVarUseForm = false;
        let localVarConvertFormParamsToString = false;
        // use FormData to transmit files using content-type "multipart/form-data"
        // see https://stackoverflow.com/questions/4007969/application-x-www-form-urlencoded-or-multipart-form-data
        localVarUseForm = canConsumeForm;
        if (localVarUseForm) {
            localVarFormParams = new FormData();
        } else {
            localVarFormParams = new HttpParams({encoder: this.encoder});
        }

        if (mahkemeKararDosyasi !== undefined) {
            localVarFormParams = localVarFormParams.append('mahkemeKararDosyasi', <any>mahkemeKararDosyasi) as any || localVarFormParams;
        }
        if (mahkemeKararDetay !== undefined) {
            localVarFormParams = localVarFormParams.append('mahkemeKararDetay', localVarUseForm ? new Blob([JSON.stringify(mahkemeKararDetay)], {type: 'application/json'}) : <any>mahkemeKararDetay) as any || localVarFormParams;
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/api/makos/uzatma-karari-id`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ResponseIDUzatmaKarariResponse>('post', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: localVarConvertFormParamsToString ? localVarFormParams.toString() : localVarFormParams,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * @param mahkemeKararDosyasi 
     * @param mahkemeKararDetay 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public yeniKararID(mahkemeKararDosyasi: Blob, mahkemeKararDetay: IDYeniKararRequest, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<ResponseIDYeniKararResponse>;
    public yeniKararID(mahkemeKararDosyasi: Blob, mahkemeKararDetay: IDYeniKararRequest, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<ResponseIDYeniKararResponse>>;
    public yeniKararID(mahkemeKararDosyasi: Blob, mahkemeKararDetay: IDYeniKararRequest, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<ResponseIDYeniKararResponse>>;
    public yeniKararID(mahkemeKararDosyasi: Blob, mahkemeKararDetay: IDYeniKararRequest, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: 'application/json', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (mahkemeKararDosyasi === null || mahkemeKararDosyasi === undefined) {
            throw new Error('Required parameter mahkemeKararDosyasi was null or undefined when calling yeniKararID.');
        }
        if (mahkemeKararDetay === null || mahkemeKararDetay === undefined) {
            throw new Error('Required parameter mahkemeKararDetay was null or undefined when calling yeniKararID.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (BearerAuth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('BearerAuth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            'application/json'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;

        // to determine the Content-Type header
        const consumes: string[] = [
            'multipart/form-data'
        ];

        const canConsumeForm = this.canConsumeForm(consumes);

        let localVarFormParams: { append(param: string, value: any): any; };
        let localVarUseForm = false;
        let localVarConvertFormParamsToString = false;
        // use FormData to transmit files using content-type "multipart/form-data"
        // see https://stackoverflow.com/questions/4007969/application-x-www-form-urlencoded-or-multipart-form-data
        localVarUseForm = canConsumeForm;
        if (localVarUseForm) {
            localVarFormParams = new FormData();
        } else {
            localVarFormParams = new HttpParams({encoder: this.encoder});
        }

        if (mahkemeKararDosyasi !== undefined) {
            localVarFormParams = localVarFormParams.append('mahkemeKararDosyasi', <any>mahkemeKararDosyasi) as any || localVarFormParams;
        }
        if (mahkemeKararDetay !== undefined) {
            localVarFormParams = localVarFormParams.append('mahkemeKararDetay', localVarUseForm ? new Blob([JSON.stringify(mahkemeKararDetay)], {type: 'application/json'}) : <any>mahkemeKararDetay) as any || localVarFormParams;
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/api/makos/yeni-karar-id`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ResponseIDYeniKararResponse>('post', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: localVarConvertFormParamsToString ? localVarFormParams.toString() : localVarFormParams,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

    /**
     * @param mahkemeKararDosyasi 
     * @param mahkemeKararDetay 
     * @param observe set whether or not to return the data Observable as the body, response or events. defaults to returning the body.
     * @param reportProgress flag to report request and response progress.
     */
    public yeniKararIT(mahkemeKararDosyasi: Blob, mahkemeKararDetay: ITKararRequest, observe?: 'body', reportProgress?: boolean, options?: {httpHeaderAccept?: '*/*', context?: HttpContext, transferCache?: boolean}): Observable<ITKararResponse>;
    public yeniKararIT(mahkemeKararDosyasi: Blob, mahkemeKararDetay: ITKararRequest, observe?: 'response', reportProgress?: boolean, options?: {httpHeaderAccept?: '*/*', context?: HttpContext, transferCache?: boolean}): Observable<HttpResponse<ITKararResponse>>;
    public yeniKararIT(mahkemeKararDosyasi: Blob, mahkemeKararDetay: ITKararRequest, observe?: 'events', reportProgress?: boolean, options?: {httpHeaderAccept?: '*/*', context?: HttpContext, transferCache?: boolean}): Observable<HttpEvent<ITKararResponse>>;
    public yeniKararIT(mahkemeKararDosyasi: Blob, mahkemeKararDetay: ITKararRequest, observe: any = 'body', reportProgress: boolean = false, options?: {httpHeaderAccept?: '*/*', context?: HttpContext, transferCache?: boolean}): Observable<any> {
        if (mahkemeKararDosyasi === null || mahkemeKararDosyasi === undefined) {
            throw new Error('Required parameter mahkemeKararDosyasi was null or undefined when calling yeniKararIT.');
        }
        if (mahkemeKararDetay === null || mahkemeKararDetay === undefined) {
            throw new Error('Required parameter mahkemeKararDetay was null or undefined when calling yeniKararIT.');
        }

        let localVarHeaders = this.defaultHeaders;

        // authentication (BearerAuth) required
        localVarHeaders = this.configuration.addCredentialToHeaders('BearerAuth', 'Authorization', localVarHeaders, 'Bearer ');

        const localVarHttpHeaderAcceptSelected: string | undefined = options?.httpHeaderAccept ?? this.configuration.selectHeaderAccept([
            '*/*'
        ]);
        if (localVarHttpHeaderAcceptSelected !== undefined) {
            localVarHeaders = localVarHeaders.set('Accept', localVarHttpHeaderAcceptSelected);
        }

        const localVarHttpContext: HttpContext = options?.context ?? new HttpContext();

        const localVarTransferCache: boolean = options?.transferCache ?? true;

        // to determine the Content-Type header
        const consumes: string[] = [
            'multipart/form-data'
        ];

        const canConsumeForm = this.canConsumeForm(consumes);

        let localVarFormParams: { append(param: string, value: any): any; };
        let localVarUseForm = false;
        let localVarConvertFormParamsToString = false;
        // use FormData to transmit files using content-type "multipart/form-data"
        // see https://stackoverflow.com/questions/4007969/application-x-www-form-urlencoded-or-multipart-form-data
        localVarUseForm = canConsumeForm;
        if (localVarUseForm) {
            localVarFormParams = new FormData();
        } else {
            localVarFormParams = new HttpParams({encoder: this.encoder});
        }

        if (mahkemeKararDosyasi !== undefined) {
            localVarFormParams = localVarFormParams.append('mahkemeKararDosyasi', <any>mahkemeKararDosyasi) as any || localVarFormParams;
        }
        if (mahkemeKararDetay !== undefined) {
            localVarFormParams = localVarFormParams.append('mahkemeKararDetay', localVarUseForm ? new Blob([JSON.stringify(mahkemeKararDetay)], {type: 'application/json'}) : <any>mahkemeKararDetay) as any || localVarFormParams;
        }

        let responseType_: 'text' | 'json' | 'blob' = 'json';
        if (localVarHttpHeaderAcceptSelected) {
            if (localVarHttpHeaderAcceptSelected.startsWith('text')) {
                responseType_ = 'text';
            } else if (this.configuration.isJsonMime(localVarHttpHeaderAcceptSelected)) {
                responseType_ = 'json';
            } else {
                responseType_ = 'blob';
            }
        }

        let localVarPath = `/api/makos/yeni-karar-it`;
        const { basePath, withCredentials } = this.configuration;
        return this.httpClient.request<ITKararResponse>('post', `${basePath}${localVarPath}`,
            {
                context: localVarHttpContext,
                body: localVarConvertFormParamsToString ? localVarFormParams.toString() : localVarFormParams,
                responseType: <any>responseType_,
                ...(withCredentials ? { withCredentials } : {}),
                headers: localVarHeaders,
                observe: observe,
                transferCache: localVarTransferCache,
                reportProgress: reportProgress
            }
        );
    }

}
