# Migrating from GitHub Actions to GitLab CI/CD

This guide provides step-by-step instructions for migrating your CI/CD workflows from GitHub Actions to self-hosted GitLab CI/CD.

## Table of Contents

1. [Setting Up GitLab](#setting-up-gitlab)
2. [Migrating Repositories](#migrating-repositories)
3. [Converting GitHub Actions Workflows to GitLab CI/CD](#converting-github-actions-workflows-to-gitlab-cicd)
4. [Setting Up GitLab Runners](#setting-up-gitlab-runners)
5. [Migrating Secrets and Variables](#migrating-secrets-and-variables)
6. [Migrating Docker Image Registry](#migrating-docker-image-registry)
7. [Testing the Migration](#testing-the-migration)

## Setting Up GitLab

Follow these steps to set up your self-hosted GitLab instance:

### For Windows

1. Create the external volumes for GitLab data:
   ```powershell
   .\setup-external-volumes.ps1
   ```

2. Start GitLab and GitLab Runner:
   ```powershell
   docker-compose -f docker-compose.windows.yml up -d
   ```

3. Wait for G<PERSON><PERSON><PERSON> to initialize (this may take a few minutes).

4. Access GitLab at http://localhost:8929 and get the root password:
   ```powershell
   docker exec -it docker-gitlab-cicd-web-1 grep 'Password:' /etc/gitlab/initial_root_password
   ```

5. Log in with username `root` and the password from the previous step.

### For Linux

1. Create the external volumes for GitLab data:
   ```bash
   ./setup-external-volumes.sh
   ```

2. Start GitLab and GitLab Runner:
   ```bash
   docker-compose -f docker-compose.linux.yml up -d
   ```

3. Wait for GitLab to initialize (this may take a few minutes).

4. Access GitLab at http://localhost:8929 and get the root password:
   ```bash
   docker exec -it docker-gitlab-cicd-web-1 grep 'Password:' /etc/gitlab/initial_root_password
   ```

5. Log in with username `root` and the password from the previous step.

## Migrating Repositories

1. Create a new project in GitLab:
   - Go to GitLab > New Project > Create blank project
   - Enter project name and other details
   - Click "Create project"

2. Push your existing repository to GitLab:
   ```bash
   # Clone your existing repository if you don't have it locally
   git clone https://github.com/your-username/your-repo.git
   cd your-repo

   # Add GitLab as a new remote
   git remote add gitlab http://localhost:8929/your-username/your-repo.git

   # Push to GitLab
   git push -u gitlab --all
   git push -u gitlab --tags
   ```

## Converting GitHub Actions Workflows to GitLab CI/CD

Here's how to convert your GitHub Actions workflows to GitLab CI/CD:

1. Create a `.gitlab-ci.yml` file in the root of your repository (you can use the provided example as a starting point).

2. Map GitHub Actions concepts to GitLab CI/CD:

   | GitHub Actions | GitLab CI/CD |
   |----------------|---------------|
   | `on` | `only`/`except` |
   | `jobs` | `jobs` |
   | `steps` | `script` |
   | `uses` | `image` |
   | `with` | `variables` |
   | `env` | `variables` |
   | `needs` | `dependencies` |
   | `matrix` | `parallel` |
   | `artifacts` | `artifacts` |
   | `cache` | `cache` |

3. Example conversion from GitHub Actions to GitLab CI/CD:

   **GitHub Actions (ci.yml)**:
   ```yaml
   name: CI

   on:
     push:
       branches: [ main, develop ]
     pull_request:
       branches: [ main, develop ]

   jobs:
     test:
       runs-on: ubuntu-latest
       steps:
         - uses: actions/checkout@v4
         - uses: actions/setup-java@v4
           with:
             java-version: '17'
             distribution: 'temurin'
         - name: Run tests
           run: mvn test
   ```

   **GitLab CI/CD (.gitlab-ci.yml)**:
   ```yaml
   stages:
     - test

   test-job:
     stage: test
     image: maven:3.9.6-eclipse-temurin-17
     script:
       - mvn test
     only:
       - main
       - develop
       - merge_requests
   ```

4. For the IYM project, convert the GitHub Actions workflows (`ci.yml` and `docker-build.yml`) to GitLab CI/CD format using the provided example in `gitlab-ci.yml.example`.

## Setting Up GitLab Runners

GitLab Runners are responsible for executing your CI/CD jobs. Follow these steps to set up a runner:

### For Windows

1. Register the GitLab Runner:
   ```powershell
   .\setup-runner.ps1
   ```

2. Follow the prompts to enter the registration token from GitLab.

### For Linux

1. Register the GitLab Runner:
   ```bash
   ./setup-runner.sh
   ```

2. Follow the prompts to enter the registration token from GitLab.

## Migrating Secrets and Variables

1. In GitLab, go to Settings > CI/CD > Variables.

2. Add each secret or variable from GitHub Actions:
   - Click "Add Variable"
   - Enter the name and value
   - Set the appropriate protection and masking options
   - Click "Add Variable"

3. For the IYM project, you'll need to migrate the following variables:
   - `JAVA_VERSION`
   - `MAVEN_OPTS`
   - Any other secrets used in your GitHub Actions workflows

## Migrating Docker Image Registry

1. GitLab includes a built-in Docker Container Registry at `http://localhost:5050`.

2. Update your Docker build and push commands in the CI/CD pipeline:

   **GitHub Actions**:
   ```yaml
   - name: Build and push Docker image
     uses: docker/build-push-action@v5
     with:
       context: .
       file: ./backend/Dockerfile
       push: true
       tags: ghcr.io/${{ github.repository }}/backend:${{ env.BRANCH_NAME }}
   ```

   **GitLab CI/CD**:
   ```yaml
   docker-build-backend:
     stage: docker
     image: docker:20
     services:
       - docker:dind
     script:
       - docker build -t localhost:5050/iym/backend:$CI_COMMIT_REF_SLUG -f backend/Dockerfile .
       - docker login -u gitlab-ci-token -p $CI_JOB_TOKEN localhost:5050
       - docker push localhost:5050/iym/backend:$CI_COMMIT_REF_SLUG
   ```

## Testing the Migration

1. Push a commit to your GitLab repository to trigger the CI/CD pipeline.

2. Monitor the pipeline execution in GitLab > CI/CD > Pipelines.

3. Verify that all jobs complete successfully.

4. Check that Docker images are pushed to the GitLab Container Registry.

5. If any issues occur, check the job logs for error messages and troubleshoot accordingly.

## Conclusion

You have successfully migrated your CI/CD workflows from GitHub Actions to self-hosted GitLab CI/CD. Your project now benefits from a fully self-hosted solution with unlimited private repositories, pipelines, and built-in Docker Container Registry.

For more information, refer to the [GitLab CI/CD documentation](https://docs.gitlab.com/ee/ci/).