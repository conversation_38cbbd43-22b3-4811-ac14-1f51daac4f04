package iym.db.jpa.service.impl.mktalep;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.talep.MahkemeSucTipiDetayTalep;
import iym.common.service.db.mktalep.DbMahkemeSucTipiDetayTalepService;
import iym.db.jpa.dao.mktalep.MahkemeSucTipiDetayTalepRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class DbMahkemeSucTipiDetayTalepServiceImp extends GenericDbServiceImpl<MahkemeSucTipiDetayTalep, Long> implements DbMahkemeSucTipiDetayTalepService {

    private final MahkemeSucTipiDetayTalepRepo mahkemeSucTipiDetayTalepRepo;

    @Autowired
    public DbMahkemeSucTipiDetayTalepServiceImp(MahkemeSucTipiDetayTalepRepo repository) {
        super(repository);
        this.mahkemeSucTipiDetayTalepRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public List<MahkemeSucTipiDetayTalep>  findByMahkemeKararDetayTalepId(Long mahkemeKararDetayTalepId){
        return mahkemeSucTipiDetayTalepRepo.findByMahkemeKararDetayTalepId(mahkemeKararDetayTalepId);
    }

}
