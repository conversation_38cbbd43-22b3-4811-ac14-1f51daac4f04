package iym.db.jpa.service.impl.mk;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.mk.MahkemeAidiyatDetay;
import iym.common.service.db.mk.DbMahkemeAidiyatDetayService;
import iym.db.jpa.dao.mk.MahkemeAidiyatDetayRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Service
public class DbMahkemeAidiyatDetayServiceImpl extends GenericDbServiceImpl<MahkemeAidiyatDetay, Long> implements DbMahkemeAidiyatDetayService {

    private final MahkemeAidiyatDetayRepo mahkemeAidiyatDetayRepo;

    @Autowired
    public DbMahkemeAidiyatDetayServiceImpl(MahkemeAidiyatDetayRepo repository) {
        super(repository);
        this.mahkemeAidiyatDetayRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public List<MahkemeAidiyatDetay> findByMahkemeKararId(Long mahkemeKararId) {
        return mahkemeAidiyatDetayRepo.findByMahkemeKararId(mahkemeKararId);
    }



    @Override
    @Transactional(readOnly = true)
    public List<MahkemeAidiyatDetay> findByMahkemeKararDetayId(Long mahkemeKararDetayId) {
        return mahkemeAidiyatDetayRepo.findByMahkemeKararDetayId(mahkemeKararDetayId);
    }

}
