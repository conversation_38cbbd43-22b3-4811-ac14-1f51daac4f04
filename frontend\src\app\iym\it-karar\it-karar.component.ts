import {Component, OnInit} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormArray, FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators} from '@angular/forms';
import {Router} from '@angular/router';

// PrimeNG Imports
import {CardModule} from 'primeng/card';
import {ButtonModule} from 'primeng/button';
import {InputTextModule} from 'primeng/inputtext';
import {InputTextarea} from 'primeng/inputtextarea';
import {SelectModule} from 'primeng/select';
import {DatePickerModule} from 'primeng/datepicker';
import {CheckboxModule} from 'primeng/checkbox';
import {ToastModule} from 'primeng/toast';
import {ProgressSpinnerModule} from 'primeng/progressspinner';
import {FileUploadModule} from 'primeng/fileupload';
import {TableModule} from 'primeng/table';
import {DialogModule} from 'primeng/dialog';
import {MessageService} from 'primeng/api';

import {EvrakDetay, ITHedefDetay, ITKararRequest, MahkemeKararBilgisi} from '../../generated-api';

// Services
import {TalepService} from '../shared/services/talep.service';

@Component({
    selector: 'app-it-karar',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        CardModule,
        ButtonModule,
        InputTextModule,
        InputTextarea,
        SelectModule,
        DatePickerModule,
        CheckboxModule,
        ToastModule,
        ProgressSpinnerModule,
        FileUploadModule,
        TableModule,
        DialogModule
    ],
    providers: [MessageService],
    templateUrl: './it-karar.component.html',
    styleUrls: ['./it-karar.component.scss']
})
export class ItKararComponent implements OnInit {

    talepForm: FormGroup;
    hedefDialogVisible = false;
    hedefForm: FormGroup;
    editingHedefIndex = -1;
    yukleniyor = false;
    seciliDosya: File | null = null;

    // Dropdown Options
    evrakTuruOptions = [
        {label: 'İletişimin Tespiti', value: EvrakDetay.EvrakTuruEnum.IletisiminTespiti}
    ];

    mahkemeKararTipOptions = [
        {label: 'Adli Hakim HTS Kararı', value: MahkemeKararBilgisi.MahkemeKararTipiEnum.AdliHakimHtsKarari},
        {label: 'Adli Savcılık HTS Kararı', value: MahkemeKararBilgisi.MahkemeKararTipiEnum.AdliSavcilikHtsKarari},
        {
            label: 'Sinyal Bilgi Değerlendirme Kararı',
            value: MahkemeKararBilgisi.MahkemeKararTipiEnum.SinyalBilgiDegerlendirmeKarari
        }
    ];

    sorguTipiOptions = [
        {label: 'HTS (Hücresel Telefon Sistemi)', value: 'HTS'},
        {label: 'Yer Tespiti', value: 'YER_TESPITI'},
        {label: 'Sinyal Bilgi', value: 'SINYAL_BILGI'},
        {label: 'Trafik Verisi', value: 'TRAFIK_VERISI'}
    ];

    tespitTuruOptions = [
        {label: 'Anlık Konum', value: 'ANLIK_KONUM'},
        {label: 'Geçmiş Konum', value: 'GECMIS_KONUM'},
        {label: 'Sürekli Takip', value: 'SUREKLI_TAKIP'},
        {label: 'Baz İstasyonu Bilgisi', value: 'BAZ_ISTASYONU'}
    ];

    constructor(
        private fb: FormBuilder,
        private talepService: TalepService,
        private messageService: MessageService,
        private router: Router
    ) {
        this.talepForm = this.createForm();
        this.hedefForm = this.createHedefForm();
    }

    ngOnInit(): void {
        // Component initialization
    }

    private createForm(): FormGroup {
        return this.fb.group({
            // EvrakDetay fields
            evrakNo: ['', [Validators.required, Validators.maxLength(50)]],
            evrakTarihi: [new Date(), Validators.required],
            evrakKurumKodu: ['', Validators.required],
            evrakTuru: [EvrakDetay.EvrakTuruEnum.IletisiminTespiti, Validators.required],
            havaleBirimi: [''],
            evrakAciklama: [''],
            geldigiIlIlceKodu: ['', Validators.required],
            acilmi: [false],
            evrakKonusu: [''],

            // MahkemeKararBilgisi fields
            mahkemeKararTipi: [null, Validators.required],

            // MahkemeKararDetay fields
            mahkemeKodu: ['', Validators.required],
            mahkemeKararNo: ['', Validators.required],
            mahkemeIlIlceKodu: ['', Validators.required],
            sorusturmaNo: [''],
            mahkemeAciklama: [''],

            // IT specific fields
            hedefDetayListesi: this.fb.array([])
        });
    }

    private createHedefForm(): FormGroup {
        return this.fb.group({
            sorguTipi: [null, Validators.required],
            hedefNo: ['', Validators.required],
            karsiHedefNo: [''],
            baslangicTarihi: [new Date(), Validators.required],
            bitisTarihi: [new Date(), Validators.required],
            tespitTuru: [null, Validators.required],
            tespitTuruDetay: [''],
            aciklama: ['']
        });
    }

    get hedefDetayListesi(): FormArray {
        return this.talepForm.get('hedefDetayListesi') as FormArray;
    }

    get hedefler(): ITHedefDetay[] {
        return this.hedefDetayListesi.value;
    }

    hedefEkleDialog(): void {
        this.editingHedefIndex = -1;
        this.hedefForm.reset();
        this.hedefForm.patchValue({
            baslangicTarihi: new Date(),
            bitisTarihi: new Date()
        });
        this.hedefDialogVisible = true;
    }

    hedefDuzenleDialog(index: number): void {
        this.editingHedefIndex = index;
        const hedef = this.hedefDetayListesi.at(index).value;
        this.hedefForm.patchValue({
            ...hedef,
            baslangicTarihi: new Date(hedef.baslangicTarihi),
            bitisTarihi: new Date(hedef.bitisTarihi)
        });
        this.hedefDialogVisible = true;
    }

    hedefKaydet(): void {
        if (this.hedefForm.valid) {
            const hedefData = {
                ...this.hedefForm.value,
                baslangicTarihi: this.hedefForm.value.baslangicTarihi.toISOString(),
                bitisTarihi: this.hedefForm.value.bitisTarihi.toISOString()
            };

            if (this.editingHedefIndex >= 0) {
                // Güncelleme
                this.hedefDetayListesi.at(this.editingHedefIndex).patchValue(hedefData);
                this.messageService.add({
                    severity: 'success',
                    summary: 'Başarılı',
                    detail: 'Hedef güncellendi'
                });
            } else {
                // Yeni ekleme
                this.hedefDetayListesi.push(this.fb.control(hedefData));
                this.messageService.add({
                    severity: 'success',
                    summary: 'Başarılı',
                    detail: 'Hedef eklendi'
                });
            }

            this.hedefDialogVisible = false;
            this.hedefForm.reset();
        } else {
            this.messageService.add({
                severity: 'warn',
                summary: 'Uyarı',
                detail: 'Lütfen tüm zorunlu alanları doldurun'
            });
        }
    }

    hedefSil(index: number): void {
        this.hedefDetayListesi.removeAt(index);
        this.messageService.add({
            severity: 'info',
            summary: 'Bilgi',
            detail: 'Hedef silindi'
        });
    }

    onDosyaSecildi(event: any): void {
        const dosyalar = event.files;
        if (dosyalar && dosyalar.length > 0) {
            this.seciliDosya = dosyalar[0];
            this.messageService.add({
                severity: 'info',
                summary: 'Dosya Seçildi',
                detail: `${this.seciliDosya?.name} dosyası seçildi`
            });
        }
    }

    onSubmit(): void {
        if (this.talepForm.valid && this.hedefDetayListesi.length > 0 && this.seciliDosya) {
            this.yukleniyor = true;

            const formData = this.talepForm.value;

            // Create request object
            const request: ITKararRequest = {
                id: this.generateUUID(),
                kararTuru: ITKararRequest.KararTuruEnum.IletisiminTespiti,
                evrakDetay: {
                    evrakNo: formData.evrakNo,
                    evrakTarihi: formData.evrakTarihi.toISOString(),
                    evrakKurumKodu: formData.evrakKurumKodu,
                    evrakTuru: formData.evrakTuru,
                    havaleBirimi: formData.havaleBirimi,
                    aciklama: formData.evrakAciklama,
                    geldigiIlIlceKodu: formData.geldigiIlIlceKodu,
                    acilmi: formData.acilmi,
                    evrakKonusu: formData.evrakKonusu
                },
                mahkemeKararBilgisi: {
                    mahkemeKararTipi: formData.mahkemeKararTipi,
                    mahkemeKararDetay: {
                        mahkemeKodu: formData.mahkemeKodu,
                        mahkemeKararNo: formData.mahkemeKararNo,
                        mahkemeIlIlceKodu: formData.mahkemeIlIlceKodu,
                        sorusturmaNo: formData.sorusturmaNo,
                        aciklama: formData.mahkemeAciklama
                    }
                },
                hedefDetayListesi: formData.hedefDetayListesi
            };

            // Send request
            this.talepService.itKararGonder(request, this.seciliDosya).subscribe({
                next: (response) => {
                    this.yukleniyor = false;
                    this.messageService.add({
                        severity: 'success',
                        summary: 'Başarılı',
                        detail: 'IT Karar talebi başarıyla gönderildi'
                    });
                    this.talepForm.reset();
                    this.hedefDetayListesi.clear();
                    this.seciliDosya = null;
                },
                error: (error) => {
                    this.yukleniyor = false;
                    this.messageService.add({
                        severity: 'error',
                        summary: 'Hata',
                        detail: 'Talep gönderilirken hata oluştu: ' + error.message
                    });
                }
            });
        } else {
            let errorMessage = 'Lütfen kontrol edin: ';
            if (!this.talepForm.valid) errorMessage += 'Form alanları, ';
            if (this.hedefDetayListesi.length === 0) errorMessage += 'En az bir hedef, ';
            if (!this.seciliDosya) errorMessage += 'Dosya seçimi ';

            this.messageService.add({
                severity: 'warn',
                summary: 'Uyarı',
                detail: errorMessage
            });
            this.markFormGroupTouched();
        }
    }

    private markFormGroupTouched(): void {
        Object.keys(this.talepForm.controls).forEach(key => {
            const control = this.talepForm.get(key);
            control?.markAsTouched();
        });
    }

    private generateUUID(): string {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }

    onReset(): void {
        this.talepForm.reset();
        this.hedefDetayListesi.clear();
        this.seciliDosya = null;
        this.messageService.add({
            severity: 'info',
            summary: 'Form Sıfırlandı',
            detail: 'Tüm alanlar temizlendi'
        });
    }

    isFieldInvalid(fieldName: string): boolean {
        const field = this.talepForm.get(fieldName);
        return !!(field && field.invalid && (field.dirty || field.touched));
    }

    isHedefFieldInvalid(fieldName: string): boolean {
        const field = this.hedefForm.get(fieldName);
        return !!(field && field.invalid && (field.dirty || field.touched));
    }

    getFieldError(fieldName: string): string {
        const field = this.talepForm.get(fieldName);
        if (field?.errors) {
            if (field.errors['required']) {
                return 'Bu alan zorunludur';
            }
            if (field.errors['maxlength']) {
                return `Maksimum ${field.errors['maxlength'].requiredLength} karakter olmalıdır`;
            }
        }
        return '';
    }

    getHedefFieldError(fieldName: string): string {
        const field = this.hedefForm.get(fieldName);
        if (field?.errors) {
            if (field.errors['required']) {
                return 'Bu alan zorunludur';
            }
        }
        return '';
    }

    formatTarih(tarih: string): string {
        return new Date(tarih).toLocaleDateString('tr-TR');
    }

    getSorguTipiLabel(value: string): string {
        const option = this.sorguTipiOptions.find(opt => opt.value === value);
        return option ? option.label : value;
    }

    getTespitTuruLabel(value: string): string {
        const option = this.tespitTuruOptions.find(opt => opt.value === value);
        return option ? option.label : value;
    }

    hedefDialogKapat(): void {
        this.hedefDialogVisible = false;
        this.hedefForm.reset();
        this.editingHedefIndex = -1;
    }
}
