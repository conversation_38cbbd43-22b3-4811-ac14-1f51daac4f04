import { Injectable } from '@angular/core';
import {
  HttpInterceptor, HttpRequest, HttpHandler, HttpEvent
} from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable()
export class AcceptHeaderInterceptor implements HttpInterceptor {
  intercept(req: HttpRequest<any>, next: <PERSON>ttpHand<PERSON>): Observable<HttpEvent<any>> {
    // Add Accept: 'application/json' header to all requests
    const modifiedReq = req.clone({
      setHeaders: {
        'Accept': 'application/json'
      }
    });
    
    return next.handle(modifiedReq);
  }
}
