package iym.db.jpa.dao.mkislem;

import iym.common.model.entity.iym.mkislem.MahkemeAidiyatIslem;
import iym.common.model.entity.iym.talep.MahkemeAidiyatTalep;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface MahkemeAidiyatIslemRepo extends JpaRepository<MahkemeAidiyatIslem, Long> {

    List<MahkemeAidiyatIslem> findByMahkemeKararIslemId(Long mahkemeKararIslemId);

}
