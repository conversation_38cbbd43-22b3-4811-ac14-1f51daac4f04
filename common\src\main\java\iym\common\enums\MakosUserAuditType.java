package iym.common.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Locale;
import java.util.Optional;

/**
 * Enum for MAKOS user audit types
 * Defines the types of user operations that are audited in the MAKOS module
 */
@Schema(description = "MAKOS kullanıcı denetim türleri", type = "string", allowableValues = {
        "LOGIN",
        "LOGOUT",
        "IMPERSONATE_LOGIN",
        "CHANGE_PASSWORD",
        "GET_USERS_FOR_ADMIN",
        "ACTIVATE",
        "DEACTIVATE",
        "ADD",
        "UPDATE",
        "DELETE",
        "REGISTER"
})
public enum MakosUserAuditType {

    LOGIN(0),
    LOGOUT(1),
    IMPERSONATE_LOGIN(2),
    CHANGE_PASSWORD(3),
    GET_USERS_FOR_ADMIN(4),
    ACTIVATE(5),
    DEACTIVATE(6),
    ADD(7),
    UPDATE(8),
    DELETE(9),
    REGISTER(10);

    private final int value;

    MakosUserAuditType(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    /**
     * Convert integer value to MakosUserAuditType
     * @param auditType integer value
     * @return Optional containing the corresponding MakosUserAuditType
     */
    public static Optional<MakosUserAuditType> convert(int auditType) {
        for (MakosUserAuditType type : MakosUserAuditType.values()) {
            if (type.value == auditType) {
                return Optional.of(type);
            }
        }
        return Optional.empty();
    }

    /**
     * Get MakosUserAuditType based on request URL endpoint
     * @param requestURL the last segment of the request URL
     * @return corresponding MakosUserAuditType or null if not found
     */
    public static MakosUserAuditType getUserAuditType(String requestURL) {
        if (requestURL == null || requestURL.trim().isEmpty()) {
            return null;
        }
        // Normalize by converting to lowercase (ENGLISH locale) and removing all non-alphabetic characters
        String normalizedURL = requestURL.toLowerCase(Locale.ENGLISH).replaceAll("[^a-z]", "");

        // Use if-else instead of switch to avoid potential issues
        if ("login".equals(normalizedURL)) {
            return MakosUserAuditType.LOGIN;
        } else if ("logout".equals(normalizedURL)) {
            return MakosUserAuditType.LOGOUT;
        } else if ("impersonatelogin".equals(normalizedURL)) {
            return MakosUserAuditType.IMPERSONATE_LOGIN;
        } else if ("changepassword".equals(normalizedURL)) {
            return MakosUserAuditType.CHANGE_PASSWORD;
        } else if ("getusersforadmin".equals(normalizedURL)) {
            return MakosUserAuditType.GET_USERS_FOR_ADMIN;
        } else if ("activate".equals(normalizedURL)) {
            return MakosUserAuditType.ACTIVATE;
        } else if ("deactivate".equals(normalizedURL)) {
            return MakosUserAuditType.DEACTIVATE;
        } else if ("add".equals(normalizedURL)) {
            return MakosUserAuditType.ADD;
        } else if ("update".equals(normalizedURL)) {
            return MakosUserAuditType.UPDATE;
        } else if ("delete".equals(normalizedURL)) {
            return MakosUserAuditType.DELETE;
        } else if ("register".equals(normalizedURL)) {
            return MakosUserAuditType.REGISTER;
        } else {
            return null;
        }
    }
}
