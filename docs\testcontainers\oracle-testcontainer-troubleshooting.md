# Oracle TestContainer Troubleshooting Guide

## Common Issues and Solutions

### 1. DataSourceAutoConfiguration.EmbeddedDatabaseConfiguration Access Error

**Error:**
```
java: org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration.EmbeddedDatabaseConfiguration has protected access
```

**Cause:** 
The inner class `EmbeddedDatabaseConfiguration` has protected access and cannot be referenced directly in exclusions.

**Solutions:**

#### Option A: Properties-Based Approach (Recommended)
Use properties to disable embedded database detection:

```properties
# In application-testcontainers-oracle.properties
spring.datasource.embedded-database-connection=none
spring.test.database.replace=none
```

This is automatically handled by `AbstractOracleTestContainer` via `@DynamicPropertySource`.

#### Option B: Exclude Entire DataSourceAutoConfiguration
If you need stronger exclusion, exclude the entire auto-configuration:

```java
@SpringBootTest(exclude = {DataSourceAutoConfiguration.class})
@ActiveProfiles("testcontainers-oracle")
public class MyOracleTest extends AbstractOracleTestContainer {
    // Test implementation
}
```

#### Option C: Use @TestPropertySource
Override properties at test level:

```java
@SpringBootTest
@TestPropertySource(properties = {
    "spring.datasource.embedded-database-connection=none",
    "spring.test.database.replace=none"
})
@ActiveProfiles("testcontainers-oracle")
public class MyOracleTest extends AbstractOracleTestContainer {
    // Test implementation
}
```

### 2. H2 vs Oracle Conflict

**Error:**
```
BeanDefinitionStoreException: Failed to process import candidates for configuration class
```

**Solution:**
Ensure these properties are set (automatically handled by AbstractOracleTestContainer):

```java
@DynamicPropertySource
static void overrideProperties(DynamicPropertyRegistry registry) {
    // Disable embedded database detection
    registry.add("spring.datasource.embedded-database-connection", () -> "none");
    registry.add("spring.test.database.replace", () -> "none");
    
    // Oracle configuration
    registry.add("spring.datasource.url", ORACLE_CONTAINER::getJdbcUrl);
    // ... other Oracle properties
}
```

### 3. DataSource Injection Issues

**Error:**
```
NoSuchBeanDefinitionException: No qualifying bean of type 'javax.sql.DataSource'
```

**Solution:**
Ensure proper annotations:

```java
@SpringBootTest
@ActiveProfiles("testcontainers-oracle")
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
public class MyOracleTest extends AbstractOracleTestContainer {
    
    @Autowired
    private DataSource dataSource; // Automatically injected
}
```

### 4. Container Startup Timeout

**Error:**
```
org.testcontainers.containers.ContainerLaunchException: Timed out waiting for container
```

**Solution:**
Increase timeout in AbstractOracleTestContainer:

```java
@Container
protected static final OracleContainer ORACLE_CONTAINER = new OracleContainer("gvenzl/oracle-xe:********-slim-faststart")
    .withStartupTimeout(Duration.ofMinutes(5)); // Increase from 3 to 5 minutes
```

### 5. Schema Loading Issues

**Error:**
```
SQLException: ORA-00942: table or view does not exist
```

**Solution:**
Ensure production schema is loaded:

```java
@BeforeAll
void initializeContainerAndSchema() {
    // This is automatically called in AbstractOracleTestContainer
    // Loads all scripts from docker/oracle/init
}
```

### 6. Connection Pool Issues

**Error:**
```
HikariPool: Connection is not available
```

**Solution:**
Adjust connection pool settings in application-testcontainers-oracle.properties:

```properties
spring.datasource.hikari.connection-timeout=60000
spring.datasource.hikari.maximum-pool-size=5
spring.datasource.hikari.minimum-idle=2
```

## Best Practices

### 1. Minimal Test Configuration

```java
@SpringBootTest
@ActiveProfiles("testcontainers-oracle")
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class MyOracleTest extends AbstractOracleTestContainer {
    // Minimal configuration - everything else is automatic
}
```

### 2. For JPA Tests

```java
@DataJpaTest
@ActiveProfiles("testcontainers-oracle")
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class MyJpaTest extends AbstractOracleTestContainer {
    // JPA-specific testing
}
```

### 3. Debug Configuration

Enable debug logging to troubleshoot issues:

```properties
logging.level.org.springframework.boot.autoconfigure=DEBUG
logging.level.org.testcontainers=DEBUG
logging.level.com.zaxxer.hikari=DEBUG
logging.level.org.springframework.test.context=DEBUG
```

## Migration Checklist

When migrating from H2 to Oracle TestContainer:

- [ ] Extend `AbstractOracleTestContainer`
- [ ] Add `@ActiveProfiles("testcontainers-oracle")`
- [ ] Add `@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)`
- [ ] Add `@TestInstance(TestInstance.Lifecycle.PER_CLASS)`
- [ ] Remove H2-specific configuration
- [ ] Update SQL scripts for Oracle compatibility
- [ ] Test with Oracle-specific features (sequences, constraints, etc.)

## Performance Tips

1. **Container Reuse**: Use static container for faster test execution
2. **Connection Pooling**: Optimize HikariCP settings for TestContainers
3. **Schema Loading**: Load schema once per test class, not per test method
4. **Parallel Execution**: Be careful with parallel test execution and shared containers
