package iym.db.jpa.service.impl.mk;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.mk.HedeflerAidiyat;
import iym.common.service.db.mk.DbHedeflerAidiyatService;
import iym.db.jpa.dao.mk.HedeflerAidiyatRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Service implementation for HedeflerAidiyat entity
 */
@Service
public class DbHedeflerAidiyatServiceImpl extends GenericDbServiceImpl<HedeflerAidiyat, Long> implements DbHedeflerAidiyatService {

    private final HedeflerAidiyatRepo hedeflerAidiyatRepo;

    @Autowired
    public DbHedeflerAidiyatServiceImpl(HedeflerAidiyatRepo repository) {
        super(repository);
        this.hedeflerAidiyatRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<HedeflerAidiyat> findById(Long id){
        return hedeflerAidiyatRepo.findById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HedeflerAidiyat> findByHedeflerId(Long hedefId){
        return hedeflerAidiyatRepo.findByHedefId(hedefId);
    }

}
