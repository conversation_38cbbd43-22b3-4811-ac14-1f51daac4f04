package iym.common.model.entity.iym.mk;

import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Entity class for HEDEFLER_TALEP table
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "HedeflerAidiyat")
@Table(name = "HEDEFLER_AIDIYAT")
public class HedeflerAidiyat implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "MAHKEME_HEDEFLER_AIDIYAT_SEQ")
    @SequenceGenerator(name = "MAHKEME_HEDEFLER_AIDIYAT_SEQ", sequenceName = "MAHKEME_HEDEFLER_AIDIYAT_SEQ", allocationSize = 1)
    private Long id;

    @Column(name = "HEDEF_ID", nullable = false)
    private Long hedefId;

    @Column(name = "AIDIYAT_KOD", length = 15, nullable = false)
    @Size(max = 15)
    private String aidiyatKod;


    @Column(name = "TARIH", nullable = false)
    private LocalDateTime tarih;

    @Column(name = "KULLANICI_ID", nullable = false)
    private Long kullanciId;


}
