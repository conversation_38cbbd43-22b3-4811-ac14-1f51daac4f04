# Oracle Database Docker Environment for IYM Project

Bu dizin, IYM projesi için Oracle 11g veritabanı Docker ortamını içerir. Oracle 11g veritabanını Docker ile çalıştırabilir ve geliştirme ortamınızı hızlıca kurabilirsiniz.

## Gereksinimler

- Docker Desktop for Windows
- Yönetici (Administrator) hakları

## Kullanım

### Veritabanını Başlatma

Veritabanını başlatmak için oracle dizininde aşağıdaki komutu çalıştırın:

```powershell
# PowerShell
cd docker\oracle
docker-compose up -d
```

```cmd
# Command Prompt (CMD)
cd docker\oracle
docker-compose up -d
```

Bu komut, Oracle 11g veritabanını başlatacak ve gerekli şemaları ve tabloları oluşturacaktır. İlk çalıştırmada veritabanının başlatılması biraz zaman alabilir.

> **Not**: <PERSON>er komutlarını çalıştırmak için <PERSON> (administrator) haklarına sahip olmanız gerekebilir.

### Veritabanı Durumunu Kontrol Etme

Veritabanının durumunu kontrol etmek için:

```powershell
# PowerShell veya CMD
cd docker\oracle
docker-compose ps
```

### Veritabanı Loglarını Görüntüleme

Veritabanı loglarını görüntülemek için:

```powershell
# PowerShell veya CMD
cd docker\oracle
docker-compose logs -f oracle
```

### Veritabanına Bağlanma

Veritabanına SQL*Plus ile bağlanmak için:

```powershell
# PowerShell veya CMD
docker exec -it iym-oracle sqlplus iym/iym@//localhost:1521/XE
```

### Uygulamayı Çalıştırma

Uygulamayı çalıştırmak için (artık Docker profili belirtmeye gerek yok):

```powershell
# PowerShell
cd backend
mvn spring-boot:run
```

```cmd
# Command Prompt (CMD)
cd backend
mvn spring-boot:run
```

Bu komut, uygulamayı çalıştıracak ve Docker'daki Oracle veritabanına bağlanacaktır. Eğer veritabanında veri yoksa, uygulama otomatik olarak örnek verileri ekleyecektir.

> **Not**: IntelliJ IDEA kullanıyorsanız, doğrudan IDE üzerinden uygulamayı çalıştırabilirsiniz.

### Veritabanını Durdurma

Veritabanını durdurmak için:

```powershell
# PowerShell veya CMD
cd docker\oracle
docker-compose down
```

Eğer veritabanı verilerini de silmek istiyorsanız:

```powershell
# PowerShell veya CMD
cd docker\oracle
docker-compose down -v
```

## Veritabanı Bilgileri

- **Host**: localhost
- **Port**: 1521
- **SID**: XE
- **Kullanıcı Adı**: iym
- **Şifre**: iym
- **Şema**: iym

## DBeaver ile Bağlanma

DBeaver veritabanı yönetim aracını kullanarak Oracle veritabanına bağlanmak için:

1. DBeaver'ı açın
2. "New Database Connection" seçeneğine tıklayın
3. Oracle'ı seçin
4. Aşağıdaki bilgileri girin:
   - Host: localhost
   - Port: 1521
   - Database: XE
   - Username: iym
   - Password: iym
5. "Test Connection" butonuna tıklayarak bağlantıyı test edin
6. "Finish" butonuna tıklayarak bağlantıyı kaydedin

## JDBC Bağlantı Bilgileri

- **JDBC URL**: ***********************************
- **Driver Class**: oracle.jdbc.OracleDriver
- **Username**: iym
- **Password**: iym

## Örnek Veriler

Docker container'ı başlatıldığında, aşağıdaki örnek veriler otomatik olarak yüklenir:

- Kullanıcılar (KULLANICILAR tablosu)
  - admin / password (MD5 hash: e10adc3949ba59abbe56e057f20f883e)
- MAKOS Kullanıcıları (MAKOS_USER tablosu)
  - makos_admin / password (BCrypt hash: $2a$10$hmjzWHKnKcaC8PJ2Xy/bWuxQpo09JWSdUjSJIGn3I5LRI1hmM6hWO)
- IYM Kullanıcıları (IYM_USER tablosu)
  - iym_admin / 123456 password (BCrypt hash: $2a$10$hmjzWHKnKcaC8PJ2Xy/bWuxQpo09JWSdUjSJIGn3I5LRI1hmM6hWO)
- Örnek evrak kayıtları (EVRAK_KAYIT tablosu)
  - 3 adet örnek evrak kaydı:
    - Evrak Sıra No: 2023-001, Evrak Tipi: ILETISIM
    - Evrak Sıra No: 2023-002, Evrak Tipi: MAHKEME
    - Evrak Sıra No: 2023-003, Evrak Tipi: GENEL

## Sorun Giderme

### Docker Komutları Çalışmıyor

Eğer Docker komutları çalışmıyorsa, PowerShell veya Command Prompt'u yönetici olarak çalıştırmayı deneyin.

### Veritabanı Başlatılamıyor

Eğer veritabanı başlatılamıyorsa:

1. Docker Desktop'ın çalıştığından emin olun
2. Docker Desktop'ı yönetici olarak çalıştırmayı deneyin
3. Docker Desktop'ı yeniden başlatın
4. Eğer hala çalışmıyorsa, Docker Desktop'ı kaldırıp yeniden yükleyin

### Veritabanı Bağlantı Hatası

Eğer veritabanına bağlanırken hata alıyorsanız:

1. Docker Desktop'ın çalıştığından emin olun
2. `docker-compose ps` komutu ile veritabanı container'ının çalıştığından emin olun
3. Eğer çalışmıyorsa, `docker-compose up -d` komutu ile yeniden başlatın
4. Logları kontrol edin: `docker-compose logs -f oracle`

### Port Çakışması

Eğer 1521 portu başka bir uygulama tarafından kullanılıyorsa:

1. `docker-compose.yml` dosyasını açın
2. `ports` bölümünü değiştirin: `"1522:1521"` (1522 portunu kullanmak için)
3. `application.properties` dosyasında JDBC URL'i güncelleyin: `***********************************`

### Uygulama Veritabanına Bağlanamıyor

Eğer uygulama veritabanına bağlanamıyorsa:

1. Veritabanının çalıştığından emin olun: `docker-compose ps`
2. Veritabanı bağlantı bilgilerinin doğru olduğundan emin olun
3. Firewall ayarlarınızı kontrol edin
4. Docker Desktop'ın Windows Güvenlik Duvarı'nda izinli olduğundan emin olun
5. Veritabanı loglarını kontrol edin: `docker-compose logs oracle`
