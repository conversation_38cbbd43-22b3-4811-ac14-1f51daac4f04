stages:
  - build
  - test
  - docker
  - deploy

variables:
  MAVEN_OPTS: "-Dmaven.repo.local=$CI_HOME/.m2/repository -Xmx1024m"
  JAVA_VERSION: "17"
  CI: "true"  # Activates pipeline profile for OpenAPI generation

# Backend Build Job with OpenAPI Generation
build-backend:
  stage: build
  image: maven:3.9.6-eclipse-temurin-17
  cache:
    key:
      files:
        - pom.xml
    paths:
      - $CI_HOME/.m2/repository
    policy: pull-push
  script:
    - echo "Building backend with OpenAPI client generation (pipeline profile active)..."
    - mvn clean install -DskipTests -Ppipeline
#  artifacts:
#    paths:
#      - backend/target/*.jar
#      - makos/target/*.jar
#    expire_in: 1 day
  tags:
    - docker

# Frontend Build Job with NPM Caching
build-frontend:
  stage: build
  image: node:18
  cache:
    key:
      files:
        - frontend/package-lock.json
    paths:
      - frontend/.npm
    policy: pull-push
  script:
    - echo "Building frontend with NPM caching..."
    - cd frontend
    - npm ci --cache .npm --prefer-offline
    - npm run build --prod
#  artifacts:
#    paths:
#      - frontend/dist
#    expire_in: 1 day
  tags:
    - docker

# Unit Tests Job with Maven Caching
test-backend:
  stage: test
  image: maven:3.9.6-eclipse-temurin-17
  cache:
    key:
      files:
        - pom.xml
    paths:
      - $CI_HOME/.m2/repository
    policy: pull
  script:
    - echo "Running backend tests with cached dependencies..."
    - mvn test -Dspring.profiles.active=test -Dtest="!*IntegrationTest" -DfailIfNoTests=false
#  artifacts:
#    paths:
#      - "**/target/surefire-reports/*.xml"
#    reports:
#      junit: "**/target/surefire-reports/*.xml"
  dependencies:
    - build-backend
  tags:
    - docker

# Backend Docker Build Job
docker-build-backend:
  stage: docker
  image: docker:20
  services:
    - docker:dind
  script:
    - export VERSION=$(mvn help:evaluate -Dexpression=project.version -q -DforceStdout)
    - docker build -t localhost:5050/iym/backend:$VERSION -f backend/Dockerfile .
    - docker login -u gitlab-ci-token -p $CI_JOB_TOKEN localhost:5050
    - docker push localhost:5050/iym/backend:$VERSION
    # Tag with branch name for easier reference
    - docker tag localhost:5050/iym/backend:$VERSION localhost:5050/iym/backend:$CI_COMMIT_REF_SLUG
    - docker push localhost:5050/iym/backend:$CI_COMMIT_REF_SLUG
  dependencies:
    - build-backend
  tags:
    - docker
  only:
    - main
    - develop
    - tags

# Makos Docker Build Job
docker-build-makos:
  stage: docker
  image: docker:20
  services:
    - docker:dind
  script:
    - export VERSION=$(mvn help:evaluate -Dexpression=project.version -q -DforceStdout)
    - docker build -t localhost:5050/iym/makos:$VERSION -f makos/Dockerfile .
    - docker login -u gitlab-ci-token -p $CI_JOB_TOKEN localhost:5050
    - docker push localhost:5050/iym/makos:$VERSION
    # Tag with branch name for easier reference
    - docker tag localhost:5050/iym/makos:$VERSION localhost:5050/iym/makos:$CI_COMMIT_REF_SLUG
    - docker push localhost:5050/iym/makos:$CI_COMMIT_REF_SLUG
  dependencies:
    - build-backend
  tags:
    - docker
  only:
    - main
    - develop
    - tags

# Frontend Docker Build Job
docker-build-frontend:
  stage: docker
  image: docker:20
  services:
    - docker:dind
  script:
    - export VERSION=$(node -p "require('./frontend/package.json').version")
    - docker build -t localhost:5050/iym/frontend:$VERSION -f frontend/Dockerfile frontend/
    - docker login -u gitlab-ci-token -p $CI_JOB_TOKEN localhost:5050
    - docker push localhost:5050/iym/frontend:$VERSION
    # Tag with branch name for easier reference
    - docker tag localhost:5050/iym/frontend:$VERSION localhost:5050/iym/frontend:$CI_COMMIT_REF_SLUG
    - docker push localhost:5050/iym/frontend:$CI_COMMIT_REF_SLUG
  dependencies:
    - build-frontend
  tags:
    - docker
  only:
    - main
    - develop
    - tags

# Deploy to Development Environment
deploy-dev:
  stage: deploy
  image: docker:20
  services:
    - docker:dind
  script:
    - echo "Deploying to development environment..."
    - docker-compose -f docker/docker-compose.yml pull
    - docker-compose -f docker/docker-compose.yml up -d
  environment:
    name: development
    url: http://localhost:8080
  tags:
    - docker
  only:
    - develop
  when: manual

# Deploy to Production Environment
deploy-prod:
  stage: deploy
  image: docker:20
  services:
    - docker:dind
  script:
    - echo "Deploying to production environment..."
    - docker-compose -f docker/docker-compose.yml pull
    - docker-compose -f docker/docker-compose.yml up -d
  environment:
    name: production
    url: http://localhost:8080
  tags:
    - docker
  only:
    - main
    - tags
  when: manual
