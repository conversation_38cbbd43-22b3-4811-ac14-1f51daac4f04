import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class FileValidatorService {
  private readonly PDF_MAGIC_NUMBER = [0x25, 0x50, 0x44, 0x46]; // %PDF in ASCII

  async validatePdfFile(file: File): Promise<{valid: boolean, error?: string}> {
    // Check file type
    if (file.type !== 'application/pdf') {
      return { valid: false, error: 'Sadece PDF dosyaları kabul edilmektedir.' };
    }

    // Check PDF magic number with safer byte handling
    try {
      const buffer = await file.slice(0, 4).arrayBuffer();
      const bytes = new Uint8Array(buffer);
      
      // Direct byte comparison for PDF magic number
      if (bytes.length < 4) {
        return { valid: false, error: 'Geçersiz PDF dosyası.' };
      }
      
      for (let i = 0; i < 4; i++) {
        if (bytes[i] !== this.PDF_MAGIC_NUMBER[i]) {
          return { valid: false, error: 'Geçersiz PDF dosyası.' };
        }
      }
    } catch (error) {
      return { valid: false, error: '<PERSON><PERSON><PERSON> doğrulaması başarısız.' };
    }

    return { valid: true };
  }
}
