package iym.db.jpa.dao.mk;

import iym.common.model.entity.iym.mk.MahkemeAidiyatDetay;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MahkemeAidiyatDetayRepo extends JpaRepository<MahkemeAidiyatDetay, Long> {

    List<MahkemeAidiyatDetay> findByMahkemeKararId(Long mahkemeKararId);

    List<MahkemeAidiyatDetay> findByMahkemeKararDetayId(Long mahkemeKararDetayId);

}
