package iym.db.jpa.dao.mktalep;

import iym.common.model.entity.iym.talep.MahkemeAidiyatDetayTalep;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface MahkemeAidiyatDetayTalepRepo extends JpaRepository<MahkemeAidiyatDetayTalep, Long> {

    List<MahkemeAidiyatDetayTalep> findByMahkemeKararTalepId(Long mahkemeKararTalepId);

    List<MahkemeAidiyatDetayTalep> findByMahkemeKararDetayTalepId(Long mahkemeKararDetayTalepId);

}
