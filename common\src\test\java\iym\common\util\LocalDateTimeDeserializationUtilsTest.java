package iym.common.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for LocalDateTimeDeserializationUtils
 * Verifies that the utility correctly configures ObjectMapper to reject numeric values for LocalDateTime
 */
class LocalDateTimeDeserializationUtilsTest {

    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        LocalDateTimeDeserializationUtils.configureObjectMapperForLocalDateTime(objectMapper);
    }

    @Test
    void testValidLocalDateTimeStringDeserialization() throws Exception {
        // Test with valid ISO format string
        String validJson = "{\"dateTime\":\"2023-12-25T10:30:00\"}";
        TestClass result = objectMapper.readValue(validJson, TestClass.class);
        
        assertNotNull(result);
        assertNotNull(result.dateTime);
        assertEquals(2023, result.dateTime.getYear());
        assertEquals(12, result.dateTime.getMonthValue());
        assertEquals(25, result.dateTime.getDayOfMonth());
        assertEquals(10, result.dateTime.getHour());
        assertEquals(30, result.dateTime.getMinute());
    }

    @Test
    void testNumericLocalDateTimeDeserializationThrowsException() {
        // Test with numeric timestamp - should throw exception
        String invalidJson = "{\"dateTime\":1703505000000}";
        
        Exception exception = assertThrows(Exception.class, () -> {
            objectMapper.readValue(invalidJson, TestClass.class);
        });
        
        assertTrue(exception.getMessage().contains("LocalDateTime deserialization from numeric values is not allowed"));
        assertTrue(exception.getMessage().contains("1703505000000"));
    }

    @Test
    void testNullLocalDateTimeDeserialization() throws Exception {
        // Test with null value
        String nullJson = "{\"dateTime\":null}";
        TestClass result = objectMapper.readValue(nullJson, TestClass.class);
        
        assertNotNull(result);
        assertNull(result.dateTime);
    }

    @Test
    void testEmptyStringLocalDateTimeDeserialization() throws Exception {
        // Test with empty string - should be treated as null
        String emptyJson = "{\"dateTime\":\"\"}";
        TestClass result = objectMapper.readValue(emptyJson, TestClass.class);
        
        assertNotNull(result);
        assertNull(result.dateTime);
    }

    @Test
    void testInvalidFormatStringDeserializationThrowsException() {
        // Test with invalid format string
        String invalidFormatJson = "{\"dateTime\":\"2023-12-25 10:30:00\"}";
        
        Exception exception = assertThrows(Exception.class, () -> {
            objectMapper.readValue(invalidFormatJson, TestClass.class);
        });
        
        // Should throw parsing exception for invalid format
        assertTrue(exception.getMessage().contains("Text") || exception.getMessage().contains("parse"));
    }

    // Test class for deserialization
    public static class TestClass {
        public LocalDateTime dateTime;

        public TestClass() {}

        public TestClass(LocalDateTime dateTime) {
            this.dateTime = dateTime;
        }
    }
}
