package iym.makos.integration;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import iym.common.enums.KararTuru;
import iym.common.util.JsonUtils;
import iym.makos.domain.mktalep.requestprocessor.service.MahkemeKararProcessService;
import iym.makos.model.MakosApiResponse;
import iym.makos.model.MakosResponseCode;
import iym.makos.model.dto.mktalep.request.MkTalepRequest;
import iym.makos.model.dto.mktalep.request.id.IDUzatmaKarariRequest;
import iym.makos.model.dto.mktalep.request.id.IDUzatmaKarariResponse;
import iym.makos.model.dto.mktalep.request.id.IDYeniKararRequest;
import iym.makos.model.dto.mktalep.request.id.IDYeniKararResponse;
import iym.makos.service.file.FilePersisterService;
import iym.makos.controller.MahkemeKararTalepController;
import iym.makos.controller.MahkemeKararTestDataGenerator;
import jakarta.servlet.Filter;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatcher;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.nio.charset.StandardCharsets;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.containsString;
import static org.hamcrest.Matchers.containsStringIgnoringCase;
import static org.junit.jupiter.api.parallel.ExecutionMode.SAME_THREAD;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.when;
import static org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers.springSecurity;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@ContextConfiguration
@SpringBootTest
//@AutoConfigureMockMvc(addFilters = false)
@Execution(SAME_THREAD)
@Slf4j
class MahkemeKararTalepControllerIntegrationTest {

    //@Autowired
    MockMvc mockMvc;

    @MockitoBean
    FilePersisterService filePersisterService;

    @MockitoBean
    MahkemeKararProcessService mahkemeKararProcessService;

    @InjectMocks
    MahkemeKararTalepController mahkemeKararTalepController;

    @Autowired
    WebApplicationContext applicationContext;

    private AutoCloseable openMocks;

    private final ObjectMapper objectMapper = JsonUtils.getMapper();

    private static final String ENDPOINT_YENI_KARAR_ID = "/mahkemeKararTalep/yeniKararID";
    private static final String ENDPOINT_UZATMA_KARAR_ID = "/mahkemeKararTalep/uzatmaKarariID";

    private final MahkemeKararTestDataGenerator testDataGenerator = new MahkemeKararTestDataGenerator();

    //private Lock lock = new ReentrantLock();

    @BeforeEach
    public void setup() {
        openMocks = MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders
                //.standaloneSetup(mahkemeKararTalepController)
                .webAppContextSetup(applicationContext)
                .apply(springSecurity())
                .addFilters(new Filter[]{})
                .alwaysDo(print())
                .build();
    }

    @AfterEach
    public void tearDown() throws Exception {
        openMocks.close();
    }

    @Test
    @WithMockUser
    public void yeniKararIDShouldFailWhenEmptyInput() throws Exception {
        mockMvc.perform(post(ENDPOINT_YENI_KARAR_ID)
                        .contentType(MediaType.MULTIPART_FORM_DATA_VALUE))
                .andExpect(status().is4xxClientError());
    }

    @Test
    @WithMockUser
    //@ResourceLock(value = "lock")
    public void yeniKararIDShouldFailWhenOnlyJsonData() throws Exception {
        mockMvc.perform(MockMvcRequestBuilders
                        .multipart(ENDPOINT_YENI_KARAR_ID)
                        .file(createMockMultipartFileForJsonPart(testDataGenerator.createValidIDYeniKararRequest()))
                        .accept(MediaType.APPLICATION_JSON_VALUE))
                .andExpect(status().is4xxClientError())
                .andExpect(content().string(containsString("Required part '" + MahkemeKararTalepController.MAHKEME_KARAR_FILE_PART + "' is not present")))
        //.andDo(print())
        ;
    }

    @Test
    @WithMockUser
    //@ResourceLock(value = "lock")
    public void yeniKararIDShouldFailWhenOnlyFileData() throws Exception {
        mockMvc.perform(MockMvcRequestBuilders
                        .multipart(ENDPOINT_YENI_KARAR_ID)
                        .file(createMockMultipartFileForFilePart("filename", "<<mahkeme karar data>>"))
                        .accept(MediaType.APPLICATION_JSON_VALUE))
                .andExpect(status().is4xxClientError())
                .andExpect(content().string(containsString("Required part '" + MahkemeKararTalepController.MAHKEME_KARAR_DETAY_JSON_PART + "' is not present")))
                .andDo(print());

    }

    @Test
    @WithMockUser
    //@ResourceLock(value = "lock")
    public void yeniKararIDShouldFailOnInvalidInput() throws Exception {
        IDYeniKararRequest request = testDataGenerator.createValidIDYeniKararRequest();

        String fileContent = "<<mahkeme karar data>>";
        String fileName = MahkemeKararTalepController.MAHKEME_KARAR_FILE_PART + ".txt";

        // remove a @NutNull field
        request.setId(null);

        mockMvc.perform(MockMvcRequestBuilders
                        .multipart(ENDPOINT_YENI_KARAR_ID)
                        .file(createMockMultipartFileForFilePart(fileName, fileContent))
                        .file(createMockMultipartFileForJsonPart(request))
                        .accept(MediaType.APPLICATION_JSON_VALUE))
                .andExpect(status().is4xxClientError())
                .andExpect(content().contentType("application/problem+json"))
                .andExpect(jsonPath("$.status").value(400))
                .andExpect(jsonPath("$.title").value("Bad Request"));
    }

    @Test
    @WithMockUser
    //@ResourceLock(value = "lock")
    public void yeniKararIDShouldFailOnInvalidKaraTuru() throws Exception {
        IDYeniKararRequest request = testDataGenerator.createValidIDYeniKararRequest();
        request.setKararTuru(KararTuru.ILETISIMIN_DENETLENMESI_UZATMA_KARARI);

        String fileContent = "<<mahkeme karar data>>";
        String fileName = MahkemeKararTalepController.MAHKEME_KARAR_FILE_PART + ".txt";

        mockMvc.perform(MockMvcRequestBuilders
                        .multipart(ENDPOINT_YENI_KARAR_ID)
                        .file(createMockMultipartFileForFilePart(fileName, fileContent))
                        .file(createMockMultipartFileForJsonPart(request))
                        .accept(MediaType.APPLICATION_JSON_VALUE))
                .andExpect(status().is4xxClientError())
                .andExpect(content().contentType("application/problem+json"))
                .andExpect(jsonPath("$.status").value(400))
                .andExpect(jsonPath("$.title").value("Bad Request"));
    }


    @Test
    @WithMockUser
    //@ResourceLock(value = "lock")
    public void yeniKararIDShouldFailOnFileSaveError() throws Exception {

        String fileContent = "<<mahkeme karar data-2>>";
        String fileName = MahkemeKararTalepController.MAHKEME_KARAR_FILE_PART + "." + UUID.randomUUID();

        when(filePersisterService.saveFileToDisk(any(), any())).thenReturn(false);
        /*
        when(filePersisterService.saveFileToDisk(path, fileContent.getBytes(StandardCharsets.UTF_8))).thenAnswer(invocationOnMock -> {
            boolean result = false;
            log.info("saveFileToDisk yeniKararIDShouldFailOnFileSaveError path:" + path + ",result:" + result);
            return result;
        });
         */

        mockMvc.perform(MockMvcRequestBuilders
                        .multipart(ENDPOINT_YENI_KARAR_ID)
                        .file(createMockMultipartFileForFilePart(fileName, fileContent))
                        .file(createMockMultipartFileForJsonPart(testDataGenerator.createValidIDYeniKararRequest()))
                        .accept(MediaType.APPLICATION_JSON_VALUE))
                .andExpect(status().is(HttpStatus.INTERNAL_SERVER_ERROR.value()))
                .andExpect(content().string(containsString("Dosya saklama hatasi")));
    }

    @Test
    @WithMockUser
    //@ResourceLock(value = "lock")
    public void yeniKararIDShouldSuccessOnMahkemeKararProcessServiceSuccess() throws Exception {

        UUID uuid = UUID.randomUUID();
        IDYeniKararRequest request = testDataGenerator.createValidIDYeniKararRequest(uuid);

        long btkEvrakId = 100L;
        IDYeniKararResponse success = IDYeniKararResponse.builder()
                .requestId(uuid)
                .btkEvrakId(btkEvrakId)
                .response(MakosApiResponse.builder()
                        .responseCode(MakosResponseCode.SUCCESS)
                        .build())
                .build();

        String fileContent = "<<mahkeme karar data-2>>";
        String fileName = MahkemeKararTalepController.MAHKEME_KARAR_FILE_PART + "." + uuid;
        when(filePersisterService.saveFileToDisk(any(), any())).thenReturn(true);
        when(mahkemeKararProcessService.process(argThat(new MkTalepRequestMatcher(request)), any(), any())).thenReturn(success);

        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders
                        .multipart(ENDPOINT_YENI_KARAR_ID)
                        .file(createMockMultipartFileForFilePart(fileName, fileContent))
                        .file(createMockMultipartFileForJsonPart(request))
                        .accept(MediaType.APPLICATION_JSON_VALUE))
                .andExpect(status().isOk())
                .andReturn();

        String result = mvcResult.getResponse().getContentAsString();
        IDYeniKararResponse response = objectMapper.readValue(result, IDYeniKararResponse.class);
        assertThat(response).isNotNull();
        assertThat(response.getRequestId()).isEqualTo(request.getId());
        assertThat(response.getBtkEvrakId()).isEqualTo(btkEvrakId);
        assertThat(response.getResponse().getResponseCode()).isEqualTo(success.getResponse().getResponseCode());
    }

    @Test
    @WithMockUser
    //@ResourceLock(value = "lock")
    public void yeniKararIDShouldFailOnMahkemeKararProcessServiceReturnInvalidRequest() throws Exception {

        UUID uuid = UUID.randomUUID();
        IDYeniKararRequest request = testDataGenerator.createValidIDYeniKararRequest(uuid);

        long btkEvrakId = 101L;
        IDYeniKararResponse failed = IDYeniKararResponse.builder()
                .requestId(uuid)
                .btkEvrakId(btkEvrakId)
                .response(MakosApiResponse.builder()
                        .responseCode(MakosResponseCode.INVALID_REQUEST)
                        .responseMessage("Error")
                        .build())
                .build();


        String fileContent = "<<mahkeme karar data-3>>";
        String fileName = MahkemeKararTalepController.MAHKEME_KARAR_FILE_PART + "." + uuid;
        when(filePersisterService.saveFileToDisk(any(), any())).thenReturn(true);
        when(mahkemeKararProcessService.process(argThat(new MkTalepRequestMatcher(request)), any(), any())).thenReturn(failed);

        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders
                        .multipart(ENDPOINT_YENI_KARAR_ID)
                        .file(createMockMultipartFileForFilePart(fileName, fileContent))
                        .file(createMockMultipartFileForJsonPart(request))
                        .accept(MediaType.APPLICATION_JSON_VALUE))
                .andExpect(status().is(MakosResponseCode.toHttpStatus(MakosResponseCode.INVALID_REQUEST).value()))
                .andReturn();

        String result = mvcResult.getResponse().getContentAsString();
        IDYeniKararResponse response = objectMapper.readValue(result, IDYeniKararResponse.class);
        assertThat(response).isNotNull();
        assertThat(response.getRequestId()).isEqualTo(request.getId());
        assertThat(response.getBtkEvrakId()).isEqualTo(btkEvrakId);
        assertThat(response.getResponse().getResponseCode()).isEqualTo(failed.getResponse().getResponseCode());
    }

    @Test
    @WithMockUser
    //@ResourceLock(value = "lock")
    public void yeniKararIDShouldFailOnMahkemeKararProcessServiceReturnFailed() throws Exception {

        UUID uuid = UUID.randomUUID();
        IDYeniKararRequest request = testDataGenerator.createValidIDYeniKararRequest(uuid);

        long btkEvrakId = 102L;
        IDYeniKararResponse failed = IDYeniKararResponse.builder()
                .requestId(uuid)
                .btkEvrakId(btkEvrakId)
                .response(MakosApiResponse.builder()
                        .responseCode(MakosResponseCode.FAILED)
                        .responseMessage("Error")
                        .build())
                .build();


        String fileContent = "<<mahkeme karar data-4>>";
        String fileName = MahkemeKararTalepController.MAHKEME_KARAR_FILE_PART + "." + uuid;
        when(filePersisterService.saveFileToDisk(any(), any())).thenReturn(true);
        when(mahkemeKararProcessService.process(argThat(new MkTalepRequestMatcher(request)), any(), any())).thenReturn(failed);

        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders
                        .multipart(ENDPOINT_YENI_KARAR_ID)
                        .file(createMockMultipartFileForFilePart(fileName, fileContent))
                        .file(createMockMultipartFileForJsonPart(request))
                        .accept(MediaType.APPLICATION_JSON_VALUE))
                .andExpect(status().is(MakosResponseCode.toHttpStatus(MakosResponseCode.FAILED).value()))
                .andReturn();

        String result = mvcResult.getResponse().getContentAsString();
        IDYeniKararResponse response = objectMapper.readValue(result, IDYeniKararResponse.class);
        assertThat(response).isNotNull();
        assertThat(response.getRequestId()).isEqualTo(request.getId());
        assertThat(response.getBtkEvrakId()).isEqualTo(btkEvrakId);
        assertThat(response.getResponse().getResponseCode()).isEqualTo(failed.getResponse().getResponseCode());
    }

    @Test
    @WithMockUser
    public void uzatmaKarariIDShouldFailWhenEmptyInput() throws Exception {
        mockMvc.perform(post(ENDPOINT_UZATMA_KARAR_ID)
                        .contentType(MediaType.MULTIPART_FORM_DATA_VALUE))
                .andExpect(status().is4xxClientError());
    }

    @Test
    @WithMockUser
    //@ResourceLock(value = "lock")
    public void uzatmaKarariIDShouldFailWhenOnlyJsonData() throws Exception {
        mockMvc.perform(MockMvcRequestBuilders
                        .multipart(ENDPOINT_UZATMA_KARAR_ID)
                        .file(createMockMultipartFileForJsonPart(testDataGenerator.createValidIDUzatmaKararRequest()))
                        .accept(MediaType.APPLICATION_JSON_VALUE))
                .andExpect(status().is4xxClientError())
                .andExpect(content().string(containsString("Required part '" + MahkemeKararTalepController.MAHKEME_KARAR_FILE_PART + "' is not present")))
        //.andDo(print())
        ;
    }

    @Test
    @WithMockUser
    //@ResourceLock(value = "lock")
    public void uzatmaKarariIDShouldFailWhenOnlyFileData() throws Exception {
        mockMvc.perform(MockMvcRequestBuilders
                        .multipart(ENDPOINT_UZATMA_KARAR_ID)
                        .file(createMockMultipartFileForFilePart("filename", "<<mahkeme karar data>>"))
                        .accept(MediaType.APPLICATION_JSON_VALUE))
                .andExpect(status().is4xxClientError())
                .andExpect(content().string(containsString("Required part '" + MahkemeKararTalepController.MAHKEME_KARAR_DETAY_JSON_PART + "' is not present")))
                .andDo(print());
    }


    @Test
    @WithMockUser
    //@ResourceLock(value = "lock")
    public void uzatmaKarariIDShouldFailOnInvalidInput() throws Exception {
        IDUzatmaKarariRequest request = testDataGenerator.createValidIDUzatmaKararRequest();

        String fileContent = "<<mahkeme karar data>>";
        String fileName = MahkemeKararTalepController.MAHKEME_KARAR_FILE_PART + ".txt";

        // remove a @NutNull field
        request.setId(null);

        mockMvc.perform(MockMvcRequestBuilders
                        .multipart(ENDPOINT_UZATMA_KARAR_ID)
                        .file(createMockMultipartFileForFilePart(fileName, fileContent))
                        .file(createMockMultipartFileForJsonPart(request))
                        .accept(MediaType.APPLICATION_JSON_VALUE))
                .andExpect(status().is4xxClientError())
                .andExpect(content().contentType("application/problem+json"))
                .andExpect(jsonPath("$.status").value(400))
                .andExpect(jsonPath("$.title").value("Bad Request"));

    }

    @Test
    @WithMockUser
    //@ResourceLock(value = "lock")
    public void uzatmaKarariIDShouldFailOnInvalidKaraTuru() throws Exception {
        IDUzatmaKarariRequest request = testDataGenerator.createValidIDUzatmaKararRequest();
        request.setKararTuru(KararTuru.ILETISIMIN_DENETLENMESI_YENI_KARAR);

        String fileContent = "<<mahkeme karar data>>";
        String fileName = MahkemeKararTalepController.MAHKEME_KARAR_FILE_PART + ".txt";

        mockMvc.perform(MockMvcRequestBuilders
                        .multipart(ENDPOINT_UZATMA_KARAR_ID)
                        .file(createMockMultipartFileForFilePart(fileName, fileContent))
                        .file(createMockMultipartFileForJsonPart(request))
                        .accept(MediaType.APPLICATION_JSON_VALUE))
                .andExpect(status().is4xxClientError())
                .andExpect(content().contentType("application/problem+json"))
                .andExpect(jsonPath("$.status").value(400))
                .andExpect(jsonPath("$.title").value("Bad Request"));
    }

    @Test
    @WithMockUser
    //@ResourceLock(value = "lock")
    public void uzatmaKarariIDShouldFailOnFileSaveError() throws Exception {

        String fileContent = "<<mahkeme karar data-2>>";
        String fileName = MahkemeKararTalepController.MAHKEME_KARAR_FILE_PART + "." + UUID.randomUUID();

        when(filePersisterService.saveFileToDisk(any(), any())).thenReturn(false);

        mockMvc.perform(MockMvcRequestBuilders
                        .multipart(ENDPOINT_UZATMA_KARAR_ID)
                        .file(createMockMultipartFileForFilePart(fileName, fileContent))
                        .file(createMockMultipartFileForJsonPart(testDataGenerator.createValidIDUzatmaKararRequest()))
                        .accept(MediaType.APPLICATION_JSON_VALUE))
                .andExpect(status().is(HttpStatus.INTERNAL_SERVER_ERROR.value()))
                .andExpect(content().string(containsString("Dosya saklama hatasi")));
    }

    @Test
    @WithMockUser
    //@ResourceLock(value = "lock")
    public void uzatmaKarariIDShouldSuccessOnMahkemeKararProcessServiceSuccess() throws Exception {

        UUID uuid = UUID.randomUUID();
        IDUzatmaKarariRequest request = testDataGenerator.createValidIDUzatmaKararRequest(uuid);

        IDUzatmaKarariResponse success = IDUzatmaKarariResponse.builder()
                .requestId(uuid)
                .response(MakosApiResponse.builder()
                        .responseCode(MakosResponseCode.SUCCESS)
                        .build())
                .build();

        String fileContent = "<<mahkeme karar data-2>>";
        String fileName = MahkemeKararTalepController.MAHKEME_KARAR_FILE_PART + "." + uuid;
        when(filePersisterService.saveFileToDisk(any(), any())).thenReturn(true);
        when(mahkemeKararProcessService.process(argThat(new MkTalepRequestMatcher(request)), any(), any())).thenReturn(success);

        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders
                        .multipart(ENDPOINT_UZATMA_KARAR_ID)
                        .file(createMockMultipartFileForFilePart(fileName, fileContent))
                        .file(createMockMultipartFileForJsonPart(request))
                        .accept(MediaType.APPLICATION_JSON_VALUE))
                .andExpect(status().isOk())
                //.andDo(print())
                .andReturn();

        String result = mvcResult.getResponse().getContentAsString();
        IDUzatmaKarariResponse response = objectMapper.readValue(result, IDUzatmaKarariResponse.class);
        assertThat(response).isNotNull();
        assertThat(response.getRequestId()).isEqualTo(request.getId());
        assertThat(response.getResponse().getResponseCode()).isEqualTo(success.getResponse().getResponseCode());
    }

    @Test
    @WithMockUser
    //@ResourceLock(value = "lock")
    public void uzatmaKarariIDShouldFailOnMahkemeKararProcessServiceReturnInvalidRequest() throws Exception {

        UUID uuid = UUID.randomUUID();
        IDUzatmaKarariRequest request = testDataGenerator.createValidIDUzatmaKararRequest(uuid);

        IDUzatmaKarariResponse failed = IDUzatmaKarariResponse.builder()
                .requestId(uuid)
                .response(MakosApiResponse.builder()
                        .responseCode(MakosResponseCode.INVALID_REQUEST)
                        .responseMessage("Error")
                        .build())
                .build();


        String fileContent = "<<mahkeme karar data-3>>";
        String fileName = MahkemeKararTalepController.MAHKEME_KARAR_FILE_PART + "." + uuid;
        when(filePersisterService.saveFileToDisk(any(), any())).thenReturn(true);
        when(mahkemeKararProcessService.process(argThat(new MkTalepRequestMatcher(request)), any(), any())).thenReturn(failed);

        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders
                        .multipart(ENDPOINT_UZATMA_KARAR_ID)
                        .file(createMockMultipartFileForFilePart(fileName, fileContent))
                        .file(createMockMultipartFileForJsonPart(request))
                        .accept(MediaType.APPLICATION_JSON_VALUE))
                .andExpect(status().is(MakosResponseCode.toHttpStatus(MakosResponseCode.INVALID_REQUEST).value()))
                .andReturn();

        String result = mvcResult.getResponse().getContentAsString();
        IDUzatmaKarariResponse response = objectMapper.readValue(result, IDUzatmaKarariResponse.class);
        assertThat(response).isNotNull();
        assertThat(response.getRequestId()).isEqualTo(request.getId());
        assertThat(response.getResponse().getResponseCode()).isEqualTo(failed.getResponse().getResponseCode());
    }

    @Test
    @WithMockUser
    //@ResourceLock(value = "lock")
    public void uzatmaKarariIDShouldFailOnMahkemeKararProcessServiceReturnFailed() throws Exception {

        UUID uuid = UUID.randomUUID();
        IDUzatmaKarariRequest request = testDataGenerator.createValidIDUzatmaKararRequest(uuid);

        IDUzatmaKarariResponse failed = IDUzatmaKarariResponse.builder()
                .requestId(uuid)
                .response(MakosApiResponse.builder()
                        .responseCode(MakosResponseCode.FAILED)
                        .responseMessage("Error")
                        .build())
                .build();


        String fileContent = "<<mahkeme karar data-4>>";
        String fileName = MahkemeKararTalepController.MAHKEME_KARAR_FILE_PART + "." + uuid;
        when(filePersisterService.saveFileToDisk(any(), any())).thenReturn(true);
        when(mahkemeKararProcessService.process(argThat(new MkTalepRequestMatcher(request)), any(), any())).thenReturn(failed);

        MvcResult mvcResult = mockMvc.perform(MockMvcRequestBuilders
                        .multipart(ENDPOINT_UZATMA_KARAR_ID)
                        .file(createMockMultipartFileForFilePart(fileName, fileContent))
                        .file(createMockMultipartFileForJsonPart(request))
                        .accept(MediaType.APPLICATION_JSON_VALUE))
                .andExpect(status().is(MakosResponseCode.toHttpStatus(MakosResponseCode.FAILED).value()))
                .andReturn();

        String result = mvcResult.getResponse().getContentAsString();
        IDUzatmaKarariResponse response = objectMapper.readValue(result, IDUzatmaKarariResponse.class);
        assertThat(response).isNotNull();
        assertThat(response.getRequestId()).isEqualTo(request.getId());
        assertThat(response.getResponse().getResponseCode()).isEqualTo(failed.getResponse().getResponseCode());
    }

    private MockMultipartFile createMockMultipartFileForFilePart(String originalFileName, String fileContent) {
        return new MockMultipartFile(
                MahkemeKararTalepController.MAHKEME_KARAR_FILE_PART,
                originalFileName,
                MediaType.TEXT_PLAIN_VALUE,
                fileContent.getBytes(StandardCharsets.UTF_8)
        );
    }

    private MockMultipartFile createMockMultipartFileForJsonPart(Object request) throws JsonProcessingException {
        return new MockMultipartFile(
                MahkemeKararTalepController.MAHKEME_KARAR_DETAY_JSON_PART,
                "mahkemeKararDetayJson",
                MediaType.APPLICATION_JSON_VALUE,
                objectMapper.writeValueAsString(request).getBytes(StandardCharsets.UTF_8));
    }

    private static class MkTalepRequestMatcher implements ArgumentMatcher<MkTalepRequest> {

        private final MkTalepRequest request;

        public MkTalepRequestMatcher(MkTalepRequest request) {
            this.request = request;
        }

        @Override
        public boolean matches(MkTalepRequest otherRequest) {
            return request.getId().equals(otherRequest.getId());
        }
    }
}