// ID Ye<PERSON> Karar Component Styles - Simplified

.p-card {
  border-radius: 8px;
}

.p-dialog {
  .p-dialog-content {
    padding: 1.5rem;
  }
  
  .p-dialog-footer {
    padding: 1rem 1.5rem;
  }
}
  


// ID Yeni Karar Component Styles - Essential hover effects

:host {
  // Basic responsive adjustments
  @media (max-width: 768px) {
    .grid {
      grid-template-columns: 1fr;
    }
  }
  
  // Hover effects for file upload area
  .border-dashed {
    transition: all 0.2s ease;
    
    &:hover {
      border-color: #10b981;
      background-color: #f0fdf4;
    }
  }
  
  // Hover effects for table rows
  .p-datatable-tbody > tr {
    transition: background-color 0.2s ease;
    
    &:hover {
      background-color: #f9fafb;
    }
  }
  
  // Custom chip styling
  .p-chip {
    margin: 0.125rem;
    transition: all 0.2s ease;
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    &.p-chip-info {
      background-color: #dbeafe;
      color: #1e40af;
      border: 1px solid #93c5fd;
      
      &:hover {
        background-color: #bfdbfe;
        border-color: #60a5fa;
      }
    }
  }
  
  // Chip container styling
  .flex.flex-wrap.gap-2 {
    min-height: 2.5rem;
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    background-color: #f9fafb;
    
    &:focus-within {
      border-color: #3b82f6;
      box-shadow: 0 0 0 1px #3b82f6;
    }
  }
}
