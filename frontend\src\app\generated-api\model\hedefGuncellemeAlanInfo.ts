/**
 * IYM Backend OpenAPI definition
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface HedefGuncellemeAlanInfo { 
    hedefGuncellemeAlanTuru: HedefGuncellemeAlanInfo.HedefGuncellemeAlanTuruEnum;
    yeniDegeri?: string;
}
export namespace HedefGuncellemeAlanInfo {
    export const HedefGuncellemeAlanTuruEnum = {
        Ad: 'AD',
        Soyad: 'SOYAD',
        TckimlIkno: 'TCKIMlIKNO',
        CanakNo: 'CANAK_NO'
    } as const;
    export type HedefGuncellemeAlanTuruEnum = typeof HedefGuncellemeAlanTuruEnum[keyof typeof HedefGuncellemeAlanTuruEnum];
}


