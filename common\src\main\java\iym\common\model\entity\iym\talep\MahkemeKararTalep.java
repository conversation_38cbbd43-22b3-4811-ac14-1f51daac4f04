package iym.common.model.entity.iym.talep;

import iym.common.model.entity.iym.mk.sorgu.MahkemeKararTalepSorguInfo;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Entity class for MAHKEME_KARAR_TALEP table
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode

@Entity(name = "MahkemeKararTalep")
@Table(name = "MAHKEME_KARAR_TALEP")
@SqlResultSetMapping(
    name = "MahkemeKararTalepSorguInfoMapping",
    classes = @ConstructorResult(
        targetClass = MahkemeKararTalepSorguInfo.class,
        columns = {
            @ColumnResult(name = "ID", type = Long.class),
            @ColumnResult(name = "SORUSTURMA_NO", type = String.class),
            @ColumnResult(name = "MAHKEME_KARAR_NO", type = String.class),
            @ColumnResult(name = "MAHKEME_KODU", type = String.class),
            @ColumnResult(name = "MAHKEME_ADI", type = String.class),
            @ColumnResult(name = "DURUM", type = String.class),
            @ColumnResult(name = "ACIKLAMA", type = String.class),
            @ColumnResult(name = "KAYIT_TARIHI", type = LocalDateTime.class),
            @ColumnResult(name = "KULLANICI_ID", type = Long.class),
            @ColumnResult(name = "KULLANICI_ADI", type = String.class),
            @ColumnResult(name = "ADI", type = String.class),
            @ColumnResult(name = "SOYADI", type = String.class),
            @ColumnResult(name = "KURUM_KOD", type = String.class),
            @ColumnResult(name = "KURUM_ADI", type = String.class),
            @ColumnResult(name = "EVRAK_ID", type = Long.class),
            @ColumnResult(name = "EVRAK_SIRA_NO", type = String.class),
            @ColumnResult(name = "EVRAK_NO", type = String.class),
            @ColumnResult(name = "EVRAK_KONUSU", type = String.class)
        }
    )
)
public class MahkemeKararTalep implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "MAHKEME_KARAR_TALEP_SEQ")
    @SequenceGenerator(name = "MAHKEME_KARAR_TALEP_SEQ", sequenceName = "MAHKEME_KARAR_TALEP_SEQ", allocationSize = 1)
    private Long id;

    @Column(name = "EVRAK_ID", nullable = false)
    @NotNull
    private Long evrakId;

    @Column(name = "KULLANICI_ID", nullable = false)
    @NotNull
    private Long kullaniciId;

    @Column(name = "KAYIT_TARIHI", nullable = false)
    @NotNull
    private LocalDateTime kayitTarihi;

    @Column(name = "DURUM", length = 20)
    @Size(max = 20)
    private String durum;

    @Column(name = "HUKUK_BIRIM", length = 50)
    @Size(max = 50)
    private String hukukBirim;

    @Column(name = "KARAR_TIP", length = 20)
    @Size(max = 20)
    private String kararTip;

    @Column(name = "MAH_KARAR_BAS_TAR")
    private LocalDateTime mahKararBasTar;

    @Column(name = "MAH_KARAR_BITIS_TAR")
    private LocalDateTime mahKararBitisTar;

    @Column(name = "MAHKEME_ADI", length = 250)
    @Size(max = 250)
    private String mahkemeAdi;

    @Column(name = "MAHKEME_KARAR_NO", length = 50)
    @Size(max = 50)
    private String mahkemeKararNo;

    @Column(name = "MAHKEME_ILI", length = 4, nullable = false)
    @NotNull
    @Size(max = 4)
    private String mahkemeIlIlceKodu;

    @Column(name = "ACIKLAMA", length = 500)
    @Size(max = 500)
    private String aciklama;

    @Column(name = "HAKIM_SICIL_NO", length = 20)
    @Size(max = 20)
    private String hakimSicilNo;

    @Column(name = "SORUSTURMA_NO", length = 50)
    @Size(max = 50)
    private String sorusturmaNo;

    @Column(name = "GERCEK_MAH_ID")
    private Long gercekMahId;

    @Column(name = "MAHKEME_KODU", length = 10)
    @Size(max = 10)
    private String mahkemeKodu;
}
