# Oracle TestContainer Configuration Guide

## Overview

This guide explains how to configure Spring Boot to use Oracle TestContainers instead of embedded databases (H2) for integration tests.

## Problem Statement

When using Oracle TestContainers with Spring Boot, you may encounter conflicts where Spring Boot tries to auto-configure both:
1. **Embedded Database (H2)** - detected on classpath
2. **Oracle TestContainer** - configured via `@DynamicPropertySource`

This results in `BeanDefinitionStoreException` in `DataSourceAutoConfiguration$EmbeddedDatabaseConfiguration`.

## Solution: Enhanced AbstractOracleTestContainer

The `AbstractOracleTestContainer` has been enhanced to automatically resolve Oracle vs H2 conflicts:

### Key Features

1. **Automatic Conflict Resolution**: Disables embedded database detection
2. **Dynamic Configuration**: Uses `@DynamicPropertySource` for Oracle properties
3. **Production Schema Loading**: Automatically loads scripts from `docker/oracle/init`
4. **Optimized Connection Pooling**: Hikari<PERSON> configured for TestContainers

### How It Works

```java
@DynamicPropertySource
static void overrideProperties(DynamicPropertyRegistry registry) {
    // CRITICAL: Disable embedded database detection
    registry.add("spring.datasource.embedded-database-connection", () -> "none");
    registry.add("spring.test.database.replace", () -> "none");
    
    // Oracle TestContainer configuration
    registry.add("spring.datasource.url", ORACLE_CONTAINER::getJdbcUrl);
    registry.add("spring.datasource.username", ORACLE_CONTAINER::getUsername);
    registry.add("spring.datasource.password", ORACLE_CONTAINER::getPassword);
    // ... additional Oracle configuration
}
```

## Usage Patterns

### 1. Full Integration Tests (@SpringBootTest)

```java
@SpringBootTest
@ActiveProfiles("testcontainers-oracle")
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class MyIntegrationTest extends AbstractOracleTestContainer {
    
    @Autowired
    private DataSource dataSource; // Automatically configured Oracle DataSource
    
    @Test
    void testDatabaseConnection() throws SQLException {
        try (Connection connection = dataSource.getConnection()) {
            assertNotNull(connection);
            // Test implementation
        }
    }
}
```

### 2. JPA Layer Tests (@DataJpaTest)

```java
@DataJpaTest
@ActiveProfiles("testcontainers-oracle")
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class MyJpaTest extends AbstractOracleTestContainer {
    
    @Autowired
    private TestEntityManager entityManager;
    
    @Autowired
    private MyRepository repository;
    
    @Test
    void testJpaOperations() {
        // Test implementation
    }
}
```

### 3. Custom SQL File Testing

```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
@ActiveProfiles("testcontainers-oracle")
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class SqlFileTest extends AbstractOracleTestContainer {
    
    @ParameterizedTest
    @MethodSource("provideSqlFiles")
    void testSqlFile(Path sqlFile) throws Exception {
        executeAsJdbcStatements(sqlFile);
        // Verify SQL execution
    }
}
```

## Configuration Files

### application-testcontainers-oracle.properties

```properties
# Disable embedded database detection
spring.datasource.embedded-database-connection=none
spring.test.database.replace=none

# JPA/Hibernate configuration for Oracle
spring.jpa.database-platform=org.hibernate.dialect.OracleDialect
spring.jpa.properties.hibernate.default_schema=iym
spring.jpa.hibernate.ddl-auto=none

# Connection pooling optimized for TestContainers
spring.datasource.hikari.connection-timeout=60000
spring.datasource.hikari.maximum-pool-size=3
spring.datasource.hikari.minimum-idle=1
```

## Best Practices

### 1. Always Use Required Annotations

```java
@ActiveProfiles("testcontainers-oracle")  // Activates Oracle profile
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)  // Prevents H2 replacement
@TestInstance(TestInstance.Lifecycle.PER_CLASS)  // Required for non-static @BeforeAll
```

### 2. Dependency Management

Ensure H2 is only in test scope:

```xml
<dependency>
    <groupId>com.h2database</groupId>
    <artifactId>h2</artifactId>
    <scope>test</scope>
</dependency>
```

### 3. Container Lifecycle

The Oracle container is automatically managed:
- Started before tests via `@Container`
- Configured via `@DynamicPropertySource`
- Cleaned up after tests via `@AfterAll`

## Troubleshooting

### Common Issues

1. **BeanDefinitionStoreException**: Ensure `spring.datasource.embedded-database-connection=none`
2. **Connection Timeout**: Increase `spring.datasource.hikari.connection-timeout`
3. **Schema Not Found**: Verify production scripts in `docker/oracle/init`

### Debug Configuration

Enable debug logging:

```properties
logging.level.org.springframework.boot.autoconfigure=DEBUG
logging.level.org.testcontainers=DEBUG
logging.level.com.zaxxer.hikari=DEBUG
```

## Migration from H2 to Oracle TestContainer

1. Extend `AbstractOracleTestContainer`
2. Add `@ActiveProfiles("testcontainers-oracle")`
3. Add `@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)`
4. Remove H2-specific configuration
5. Update SQL scripts for Oracle compatibility

## Performance Optimization

- Container reuse across test classes (static container)
- Optimized connection pool settings
- Minimal initialization scripts
- Production schema loaded once per test class
