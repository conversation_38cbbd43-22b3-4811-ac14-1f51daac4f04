package iym.db.jpa.dao.mktalep;

import iym.common.model.entity.iym.talep.MahkemeSuclarTalep;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository interface for MahkemeSuclarTalep entity
 */
@Repository
public interface MahkemeSuclarTalepRepo extends JpaRepository<MahkemeSuclarTalep, Long> {

    Optional<MahkemeSuclarTalep> findById(Long id);

    List<MahkemeSuclarTalep> findByMahkemeKararTalepId(Long mahkemeKararTalepId);

    Optional<MahkemeSuclarTalep> findByMahkemeKararTalepIdAndSucTipKodu(Long mahkemeKararId, String sucTipKodu);
}
