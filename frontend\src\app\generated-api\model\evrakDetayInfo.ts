/**
 * IYM Backend OpenAPI definition
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface EvrakDetayInfo { 
    evrakNo?: string;
    evrakTarihi?: string;
    evrakKurumKodu?: string;
    evrakKurumAdi?: string;
    evrakTuru?: EvrakDetayInfo.EvrakTuruEnum;
    geldigiIlIlceKodu?: string;
    geldigiIlIlceAdi?: string;
    acilmi?: boolean;
    evrakKonusu?: string;
    aciklama?: string;
    durumu?: string;
}
export namespace EvrakDetayInfo {
    export const EvrakTuruEnum = {
        IletisiminTespiti: 'ILETISIMIN_TESPITI',
        IletisiminDenetlenmesi: 'ILETISIMIN_DENETLENMESI',
        GenelEvrak: 'GENEL_EVRAK'
    } as const;
    export type EvrakTuruEnum = typeof EvrakTuruEnum[keyof typeof EvrakTuruEnum];
}


