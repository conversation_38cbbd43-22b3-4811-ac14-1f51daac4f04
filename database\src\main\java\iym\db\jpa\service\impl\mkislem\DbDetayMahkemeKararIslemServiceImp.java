package iym.db.jpa.service.impl.mkislem;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.mkislem.DetayMahkemeKararIslem;
import iym.common.service.db.mkislem.DbDetayMahkemeKararIslemService;
import iym.db.jpa.dao.mkislem.DetayMahkemeKararIslemRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class DbDetayMahkemeKararIslemServiceImp extends GenericDbServiceImpl<DetayMahkemeKararIslem, Long> implements DbDetayMahkemeKararIslemService {

    private final DetayMahkemeKararIslemRepo detayMahkemeKararIslemRepo;

    @Autowired
    public DbDetayMahkemeKararIslemServiceImp(DetayMahkemeKararIslemRepo repository) {
        super(repository);
        this.detayMahkemeKararIslemRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public List<DetayMahkemeKararIslem> findByEvrakId(Long evrakId) {
        return detayMahkemeKararIslemRepo.findByEvrakId(evrakId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<DetayMahkemeKararIslem> findByMahkemeKararIslemId(Long mahkemeKararIslemId) {
        return detayMahkemeKararIslemRepo.findByMahkemeKararIslemId(mahkemeKararIslemId);
    }

}
