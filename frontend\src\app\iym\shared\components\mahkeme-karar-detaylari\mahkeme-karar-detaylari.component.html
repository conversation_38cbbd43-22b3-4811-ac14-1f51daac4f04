<div [formGroup]="form" class="mahkeme-karar-detaylari">
  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
    
    <!-- Mahkeme Kodu -->
    <div class="flex flex-col">
      <label 
        *ngIf="showLabels" 
        for="mahkemeKodu" 
        class="text-sm font-medium text-gray-700 mb-1">
        Mahkeme Kodu 
        <span *ngIf="required" class="text-red-500">*</span>
      </label>
      <input 
        pInputText 
        formControlName="mahkemeKodu" 
        placeholder="Mahkeme kodu giriniz"
        [class]="isFieldInvalid('mahkemeKodu') ? 'ng-invalid ng-dirty' : ''"
        [disabled]="disabled"
        class="w-full" />
      <small 
        *ngIf="isFieldInvalid('mahkemeKodu')" 
        class="text-red-500">
        {{ getFieldError('mahkemeKodu') }}
      </small>
    </div>

    <!-- Mahkeme İl/İlçe Kodu -->
    <div class="flex flex-col">
      <label 
        *ngIf="showLabels" 
        for="mahkemeIlIlceKodu" 
        class="text-sm font-medium text-gray-700 mb-1">
        Mahkeme İl/İlçe Kodu 
        <span *ngIf="required" class="text-red-500">*</span>
      </label>
      <input 
        pInputText 
        formControlName="mahkemeIlIlceKodu" 
        placeholder="Mahkeme il/ilçe kodu giriniz"
        [class]="isFieldInvalid('mahkemeIlIlceKodu') ? 'ng-invalid ng-dirty' : ''"
        [disabled]="disabled"
        class="w-full" />
      <small 
        *ngIf="isFieldInvalid('mahkemeIlIlceKodu')" 
        class="text-red-500">
        {{ getFieldError('mahkemeIlIlceKodu') }}
      </small>
    </div>

    <!-- Mahkeme Karar No -->
    <div class="flex flex-col">
      <label 
        *ngIf="showLabels" 
        for="mahkemeKararNo" 
        class="text-sm font-medium text-gray-700 mb-1">
        Mahkeme Karar No
      </label>
      <input 
        pInputText 
        formControlName="mahkemeKararNo" 
        placeholder="Mahkeme karar numarası giriniz"
        [disabled]="disabled"
        class="w-full" />
    </div>

    <!-- Soruşturma No -->
    <div class="flex flex-col">
      <label 
        *ngIf="showLabels" 
        for="sorusturmaNo" 
        class="text-sm font-medium text-gray-700 mb-1">
        Soruşturma No
      </label>
      <input 
        pInputText 
        formControlName="sorusturmaNo" 
        placeholder="Soruşturma numarası giriniz"
        [disabled]="disabled"
        class="w-full" />
    </div>

    <!-- Açıklama -->
    <div class="flex flex-col md:col-span-2">
      <label 
        *ngIf="showLabels" 
        for="aciklama" 
        class="text-sm font-medium text-gray-700 mb-1">
        Açıklama
      </label>
      <textarea 
        pTextarea 
        formControlName="aciklama" 
        placeholder="Açıklama giriniz"
        rows="2"
        [disabled]="disabled"
        class="w-full">
      </textarea>
    </div>

  </div>
</div>
