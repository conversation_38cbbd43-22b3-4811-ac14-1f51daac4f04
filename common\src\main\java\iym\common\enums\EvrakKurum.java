package iym.common.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Objects;

@Schema(description = "Evrak kurumu", type = "string", allowableValues = {
        "BILINMEYEN",
        "MIT",
        "EGMIDB",
        "JANDARMA",
        "EGMKOM",
        "EGMTEM",
        "EGMBSM",
        "EGMUMD",
        "EGMASAYIS",
        "EGMSBR"
})
public enum EvrakKurum {
    BILINMEYEN("0"),
    MIT("01"),
    EGMIDB("02"),
    JANDARMA("03"),
    EGMKOM("08"),
    EGMTEM("18"),
    EGMBSM("19"),
    EGMUMD("23") ,
    EGMASAYIS("25"),
    EGMSBR("26");

    private final String kurumKodu;


    EvrakKurum(String kurumKodu) {
        this.kurumKodu = kurumKodu;

    }

    @JsonValue
    public String getKurumKodu() {
        return this.kurumKodu;
    }

    //@JsonCreator
    public static EvrakKurum fromName(String name) {
        for (EvrakKurum kurum : EvrakKurum.values()) {
            if (kurum.name().equals(name)) {
                return kurum;
            }
        }
        throw new IllegalArgumentException("Gecersiz kurum: '" + name + "'");
    }

    @JsonCreator
    public static EvrakKurum fromValue(String kurumkodu) {
        for (EvrakKurum kurum : EvrakKurum.values()) {
            if (Objects.equals(kurum.kurumKodu, kurumkodu)) {
                return kurum;
            }
        }
        throw new IllegalArgumentException("Gecersiz kurumkodu: '" + kurumkodu + "'");
    }
}
