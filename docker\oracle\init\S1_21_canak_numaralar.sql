-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create sequence for CANAK_NUMARALAR if it doesn't exist
DECLARE
  seq_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO seq_exists FROM user_sequences WHERE sequence_name = 'CANAK_NUMARALAR_SEQ';
  IF seq_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE SEQUENCE iym.CANAK_NUMARALAR_SEQ
      START WITH 1
      INCREMENT BY 1
      NOCACHE
      NOCYCLE';
  END IF;
END;
/

-- Create CANAK_NUMARALAR table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'CANAK_NUMARALAR';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.CANAK_NUMARALAR (
      ID NUMBER NOT NULL,
      CANAK_NO VARCHAR2(20 BYTE) NOT NULL,
      <PERSON><PERSON><PERSON>_KOD VARCHAR2(2 BYTE) NOT NULL,
      <PERSON>KLEME_TARIH DATE,
      KUT<PERSON> NUMBER,
      ACIKLAMA VARCHAR2(300 BYTE),
      EKLEYEN_ID NUMBER
    )';
    
    -- Create index
    EXECUTE IMMEDIATE 'CREATE INDEX iym.CANAK_NUMARALAR_IDX ON iym.CANAK_NUMARALAR (CANAK_NO ASC)';
  END IF;
END;
/

-- Insert sample data if table is empty
DECLARE
  row_count NUMBER;
BEGIN
  SELECT COUNT(*) INTO row_count FROM iym.CANAK_NUMARALAR;
  IF row_count = 0 THEN
    -- Make sure we have users in KULLANICILAR table
    DECLARE
      user_count NUMBER;
    BEGIN
      SELECT COUNT(*) INTO user_count FROM iym.KULLANICILAR;
      
      IF user_count > 0 THEN
        -- Get the ID of the admin user
        DECLARE
          admin_id NUMBER;
        BEGIN
          SELECT ID INTO admin_id FROM iym.KULLANICILAR WHERE KULLANICI_ADI = 'admin';
          
          -- Sample data 1
          INSERT INTO iym.CANAK_NUMARALAR (
            ID, CANAK_NO, KURUM_KOD, EKLEME_TARIH, KUTU, ACIKLAMA, EKLEYEN_ID
          ) VALUES (
            iym.CANAK_NUMARALAR_SEQ.NEXTVAL, 'CANAK001', '01', SYSDATE, 1, 'Örnek çanak 1', admin_id
          );
          
          -- Sample data 2
          INSERT INTO iym.CANAK_NUMARALAR (
            ID, CANAK_NO, KURUM_KOD, EKLEME_TARIH, KUTU, ACIKLAMA, EKLEYEN_ID
          ) VALUES (
            iym.CANAK_NUMARALAR_SEQ.NEXTVAL, 'CANAK002', '01', SYSDATE, 1, 'Örnek çanak 2', admin_id
          );
          
          -- Sample data 3
          INSERT INTO iym.CANAK_NUMARALAR (
            ID, CANAK_NO, KURUM_KOD, EKLEME_TARIH, KUTU, ACIKLAMA, EKLEYEN_ID
          ) VALUES (
            iym.CANAK_NUMARALAR_SEQ.NEXTVAL, 'CANAK003', '01', SYSDATE, 1, 'Örnek çanak 3', admin_id
          );
        EXCEPTION
          WHEN NO_DATA_FOUND THEN
            NULL; -- Admin user not found, skip all insertions
        END;
      END IF;
    END;
  END IF;
END;
/

COMMIT;
