# Integration Test Environment Configuration for Backend
# Multi-Database Configuration for Integration Testing

# Oracle Database configuration (Primary) - H2 in-memory for integration testing
spring.datasource.oracle.jdbc-url=jdbc:h2:mem:oracle_integration_testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.oracle.driver-class-name=org.h2.Driver
spring.datasource.oracle.username=sa
spring.datasource.oracle.password=

# PostgreSQL Database configuration (Secondary) - H2 in-memory for integration testing
spring.datasource.postgresql.jdbc-url=jdbc:h2:mem:postgresql_integration_testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.postgresql.driver-class-name=org.h2.Driver
spring.datasource.postgresql.username=sa
spring.datasource.postgresql.password=

# H2 Console for debugging tests (if needed)
spring.h2.console.enabled=true

# Oracle JPA configuration for integration tests
spring.jpa.oracle.hibernate.ddl-auto=create-drop
spring.jpa.oracle.show-sql=false
spring.jpa.oracle.hibernate.format_sql=false

# PostgreSQL JPA configuration for integration tests
spring.jpa.postgresql.hibernate.ddl-auto=create-drop
spring.jpa.postgresql.show-sql=false
spring.jpa.postgresql.hibernate.format_sql=false

# Global JPA configuration for integration tests
spring.jpa.properties.hibernate.hbm2ddl.auto=create-drop
spring.jpa.properties.hibernate.validator.apply_to_ddl=false
spring.jpa.properties.hibernate.temp.use_jdbc_metadata_defaults=false

# Flyway configuration for integration tests - DISABLE FLYWAY
spring.flyway.enabled=false

# Connection pool configuration for integration tests
spring.datasource.hikari.connectionTimeout=20000
spring.datasource.hikari.maximumPoolSize=5

# Logging configuration for integration tests
logging.level.root=WARN
logging.level.org.springframework.web=INFO
logging.level.org.hibernate=WARN
logging.level.iym=INFO
logging.level.com.zaxxer.hikari.HikariConfig=ERROR
logging.level.com.zaxxer.hikari=ERROR

# Swagger configuration for integration tests
springdoc.swagger-ui.enabled=false
springdoc.api-docs.enabled=false

# Application specific properties for integration tests
app.seed-data=false

# JWT Configuration for integration tests
app.jwtSecret=integration-test-secret-key-asdRET567-asdHd-52sdauer-dfgZSDfas5sCd34df-123dFTH56HG-asd45FgbsdDd334-aasd456fdvb
# 30 minutes for integration tests (short for security)
app.jwtExpirationInSec=1800

# CORS Configuration for integration tests
cors.allowed.origins=http://localhost:3000,http://localhost:4000,http://127.0.0.1:3000,http://127.0.0.1:4000

makos.api.base-url=${MAKOS_API_BASE_URL:http://localhost:5000/makosapi}
makos.api.username=${MAKOS_API_USERNAME:}
makos.api.password=${MAKOS_API_PASSWORD:}
makos.api.connect-timeout=${MAKOS_API_CONNECT_TIMEOUT:5000}
makos.api.read-timeout=${MAKOS_API_READ_TIMEOUT:30000}
