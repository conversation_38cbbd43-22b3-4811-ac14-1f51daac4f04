package iym.common.service.db.mk;

import iym.common.model.entity.iym.mk.MahkemeKarar;
import iym.common.model.entity.iym.mk.sorgu.MahkemeKararSorguInfo;
import iym.common.model.entity.iym.mk.sorgu.MahkemeKararSorguParam;
import iym.common.service.db.GenericDbService;

import java.util.List;
import java.util.Optional;


public interface DbMahkemeKararService extends GenericDbService<MahkemeKarar, Long> {

    //Optional<MahkemeKarar> findById(Long evrakId);

    List<MahkemeKarar> findByEvrakId(Long evrakId);

    Optional<MahkemeKarar> findBy(
            String mahkemeIlIlceKodu,
            String mahkemeKodu,
            String mahkemeKararNo,
            String sorusturmaNo
    );

    List<MahkemeKararSorguInfo> mahkemeKararSorgu(String kurumKodu, MahkemeKararSorguParam sorguParam);

    List<MahkemeKararSorguInfo> islenecekKararListesi(String kurumKodu);
}
