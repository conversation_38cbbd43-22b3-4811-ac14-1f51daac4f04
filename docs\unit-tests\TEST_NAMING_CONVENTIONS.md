# Test Naming Conventions and Directory Structure

## Overview
This document establishes naming conventions and directory structure for unit and integration tests across the IYM project.

## Directory Structure

### Backend Module
```
backend/src/test/java/iym/backend/
├── config/                 # Configuration tests (unit)
├── controller/             # Controller unit tests
├── integration/            # All integration tests (new)
├── kullanici/              # User domain unit tests
├── makosclient/            # Makos client unit tests
└── postgresql/             # Legacy - being phased out
```

### Makos Module
```
makos/src/test/java/iym/makos/
├── architecture/           # Architectural tests
├── config/                 # Configuration tests
├── controller/             # Controller unit tests
├── domain/                 # Domain unit tests
├── integration/            # All integration tests
└── mapper/                 # Mapper unit tests
```

## Test Classification Rules

### Unit Tests
- **Location**: Reside in their respective domain directories
- **Annotations**: 
  - `@Test` (JUnit 5)
  - `@ExtendWith(MockitoExtension.class)` for Mockito
  - `@WebMvcTest` for controller layer
  - `@DataJpaTest` for repository layer
- **Characteristics**: 
  - Test single class/method in isolation
  - Use mocks/stubs for dependencies
  - Fast execution (< 100ms per test)
  - No Spring context loaded

### Integration Tests
- **Location**: Must reside in `integration/` directory
- **Annotations**:
  - `@SpringBootTest` (full application context)
  - `@DataJpaTest` (JPA layer with real database)
  - `@WebMvcTest` (web layer with Spring context)
- **Characteristics**:
  - Test multiple components working together
  - Use real dependencies (database, external services)
  - Slower execution (100ms - several seconds)
  - Spring context loaded

## Naming Conventions

### Test Class Names

#### Unit Tests
- Format: `{ClassName}Test`
- Examples:
  - `KullaniciServiceTest` (unit test for KullaniciService)
  - `HealthCheckControllerTest` (unit test for HealthCheckController)
  - `KullaniciMapperTest` (unit test for KullaniciMapper)

#### Integration Tests
- Format: `{ClassName}IntegrationTest`
- Examples:
  - `KullaniciServiceIntegrationTest`
  - `HealthCheckControllerIntegrationTest`
  - `MakosApiServiceIntegrationTest`

### Test Method Names

#### Format
`{methodOrScenario}_{should}_{expectedBehavior}_{when}_{condition}`

#### Examples
- `createKullanici_shouldReturnCreatedUser_whenValidInputProvided`
- `healthCheck_shouldReturnAliveMessage_whenServiceIsHealthy`
- `findByKullaniciAdi_shouldReturnEmptyOptional_whenUserNotFound`

#### Anti-patterns (to avoid)
- ❌ `greetingShouldReturnDefaultMessage` (unclear what "greeting" refers to)
- ❌ `test1`, `test2` (non-descriptive)
- ❌ `shouldWork` (vague)

## Performance Optimizations

### Integration Tests
- **Avoid `@DirtiesContext`** unless absolutely necessary
- **Use `@ActiveProfiles("integration-test")`** for consistent configuration
- **Consider test slicing** (`@WebMvcTest`, `@DataJpaTest`) over full `@SpringBootTest`
- **Use test containers** for database integration tests

### Unit Tests
- **Avoid Spring context** loading entirely
- **Use constructor injection** for testability
- **Mock external dependencies** using Mockito

## Migration Checklist

### Completed ✅
1. Created single integration directory for each module
2. Moved integration tests to appropriate directories
3. Renamed test classes to follow naming conventions
4. Fixed method names in HealthCheckControllerRestTemplateTest
5. Updated package declarations
6. Removed duplicate files

### Current Integration Test Locations

#### Backend Module
- `KullaniciServiceIntegrationTest.java` → `integration/`
- `MakosControllerSecurityIntegrationTest.java` → `integration/`

#### Makos Module
- `HealthCheckControllerRestTemplateTest.java` → `integration/`
- `HibernateSchemaValidationIntegrationTest.java` → `integration/`
- `OracleProductionSqlFilesIntegrationTest.java` → `integration/`
- `SimpleOracleTestContainerIntegrationTest.java` → `integration/`
- `TransactionTestServiceIntegrationTest.java` → `integration/`

## Future Recommendations

1. **Review remaining tests** in controller/ and service/ directories for integration vs unit classification
2. **Establish CI/CD pipeline** rules to run unit tests separately from integration tests
3. **Consider test database** configuration for faster integration test execution
4. **Add performance benchmarks** for critical integration test suites
5. **Document test data setup** strategies for integration tests