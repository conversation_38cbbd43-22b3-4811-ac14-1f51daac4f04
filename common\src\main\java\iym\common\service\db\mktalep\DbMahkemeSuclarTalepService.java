package iym.common.service.db.mktalep;

import iym.common.model.entity.iym.talep.MahkemeSuclarTalep;
import iym.common.service.db.GenericDbService;

import java.util.List;
import java.util.Optional;

/**
 * Service interface for MahkemeSuclarTalep entity
 */
public interface DbMahkemeSuclarTalepService extends GenericDbService<MahkemeSuclarTalep, Long> {

    Optional<MahkemeSuclarTalep> findById(Long id);

    List<MahkemeSuclarTalep> findByMahkemeKararTalepId(Long mahkemeKararTalepId);

    Optional<MahkemeSuclarTalep> findByMahkemeKararTalepIdAndSucTipKodu(Long mahkemeKararTalepId, String sucTipKodu);

}
