package iym.db.jpa.service.impl.mk;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.MahkemeKararItiraz;
import iym.common.service.db.DbMahkemeKararItirazService;
import iym.db.jpa.dao.mk.MahkemeKararItirazRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class DbMahkemeKararItirazServiceImpl extends GenericDbServiceImpl<MahkemeKararItiraz, Long> implements DbMahkemeKararItirazService {

    private final MahkemeKararItirazRepo mahkemeKararItirazRepo;

    @Autowired
    public DbMahkemeKararItirazServiceImpl(MahkemeKararItirazRepo repository) {
        super(repository);
        this.mahkemeKararItirazRepo = repository;
    }


}
