// Backend ile uyumlu KararTuru enum değerleri normalize edilmiş hali
// birden fazla sınıf içinde aynı enum open api generation nedeniyle ayrı ayrı sınıflar olarak generate edildiği için
// bu sınıf ortak kullanım için yatarılmıştır.
// export enum  KararTuruEnum {
//     ILETISIMIN_DENETLENMESI_YENI_KARAR = 'İletişimin Denetlenmesi - Ye<PERSON>',
//     ILETISIMIN_DENETLENMESI_UZATMA_KARARI = 'İletişimin Denetlenmesi - Uza<PERSON>',
//     ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI = 'İletişimin Denetlenmesi - Sonlandırma Kararı',
//     ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME = 'İletişimin Denetlenmes<PERSON> - <PERSON><PERSON><PERSON>',
//     ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME = 'İletiş<PERSON>in Denetlenmesi - Mahkeme Kararı Güncelleme',
//     ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME = 'İletişimin Denetlenmesi - Aidiyat Güncelleme',
//     ILETISIMIN_TESPITI = 'İletişimin Tespiti'
// }

export enum KararTuruEnum  {
    IletisiminDenetlenmesiYeniKarar= 'ILETISIMIN_DENETLENMESI_YENI_KARAR',
    IletisiminDenetlenmesiUzatmaKarari= 'ILETISIMIN_DENETLENMESI_UZATMA_KARARI',
    IletisiminDenetlenmesiSonlandirmaKarari= 'ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI',
    IletisiminDenetlenmesiAidiyatGuncelleme= 'ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME',
    IletisiminDenetlenmesiHedefGuncelleme= 'ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME',
    IletisiminDenetlenmesiMahkemekararGuncelleme= 'ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME',
    IletisiminDenetlenmesiCanakGuncelleme= 'ILETISIMIN_DENETLENMESI_CANAK_GUNCELLEME',
    IletisiminTespiti= 'ILETISIMIN_TESPITI',
    GenelEvrak= 'GENEL_EVRAK',
    IletisiminDenetlenmesiSuctipiGuncelleme= 'ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME'
}
export const KararTuruDisplayName: { [key in KararTuruEnum]: string } = {
    [KararTuruEnum.IletisiminDenetlenmesiYeniKarar]: 'Yeni Karar',
    [KararTuruEnum.IletisiminDenetlenmesiUzatmaKarari]: 'Uzatma Kararı',
    [KararTuruEnum.IletisiminDenetlenmesiSonlandirmaKarari]: 'Sonlandırma Kararı',
    [KararTuruEnum.IletisiminDenetlenmesiAidiyatGuncelleme]: 'Aidiyat Güncelleme',
    [KararTuruEnum.IletisiminDenetlenmesiHedefGuncelleme]: 'Hedef Güncelleme',
    [KararTuruEnum.IletisiminDenetlenmesiMahkemekararGuncelleme]: 'Mahkeme Karar Güncelleme',
    [KararTuruEnum.IletisiminDenetlenmesiCanakGuncelleme]: 'Çanak Güncelleme',
    [KararTuruEnum.IletisiminTespiti]: 'İletişimin Tespiti',
    [KararTuruEnum.GenelEvrak]: 'Genel Evrak',
    [KararTuruEnum.IletisiminDenetlenmesiSuctipiGuncelleme]: 'Suç Tipi Güncelleme',
};
export const KararTuruOptions = Object.values(KararTuruEnum).map(value => ({
    value,
    label: KararTuruDisplayName[value]
}));
export function getKararTuruDisplayName(value: KararTuruEnum): string {
    return KararTuruDisplayName[value] || '';
}
