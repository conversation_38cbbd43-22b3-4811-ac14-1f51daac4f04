# Docker Development Environment for IYM Project

This directory contains Docker-based development environments for the IYM project.

## Available Services

### Self-Hosted GitLab CI/CD Infrastructure (New)

A self-hosted GitLab CI/CD infrastructure is available as a complete replacement for GitHub and GitHub Actions:

```bash
# For Windows
cd docker/gitlab-cicd
.\setup-external-volumes.ps1
docker-compose -f docker-compose.windows.yml up -d
.\setup-runner.ps1

# For Linux
cd docker/gitlab-cicd
./setup-external-volumes.sh
docker-compose -f docker-compose.linux.yml up -d
./setup-runner.sh
```

Access GitLab at http://localhost:8929 with username `root` and the password from:
```bash
docker exec -it docker-gitlab-cicd-web-1 grep 'Password:' /etc/gitlab/initial_root_password
```

See `gitlab-cicd/README.md` for detailed instructions.

### All Project Components

To start all project components (PostgreSQL, Oracle, Backend, Frontend) with a single command:

```bash
cd docker
docker-compose up -d
```

### Oracle Database (Oracle Only)

To start only the Oracle database:

```bash
cd docker/oracle
docker-compose up -d
```

## Adding New Services
****
To add a new service, follow these steps:

1. Create a new directory for the service:
   ```bash
   mkdir docker/new-service
   ```

2. Create a docker-compose.yml file for the service:
   ```bash
   touch docker/new-service/docker-compose.yml
   ```

3. Add required configuration files for the service.

4. Create a README.md file for the service.

## Starting All Services

To start all services with a single command:

```bash
cd docker
docker-compose up -d
```

## Stopping All Services

To stop all services:

```bash
cd docker
docker-compose down
```

## Access Information

- **Frontend**: http://localhost:4200
- **Backend API**: http://localhost:8080
- **Makos API**: http://localhost:5000/makosapi
- **PostgreSQL**: localhost:5432
  - Database: iym_db
  - User: iym
  - Password: iym
- **Oracle**: localhost:1521
  - SID: XE
  - User: iym
  - Password: iym
- **GitLab**: http://localhost:8929
  - User: root
  - Password: See initial_root_password file
- **GitLab Container Registry**: http://localhost:5050

## Monitoring Logs

To monitor service logs:

```bash
docker-compose logs -f [service_name]
```

Example: `docker-compose logs -f backend`
