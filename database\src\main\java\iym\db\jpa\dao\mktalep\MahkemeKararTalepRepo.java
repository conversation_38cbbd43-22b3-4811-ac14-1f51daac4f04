package iym.db.jpa.dao.mktalep;

import iym.common.model.entity.iym.talep.MahkemeKararTalep;
import iym.db.jpa.dao.sorgu.MahkemeKararTalepRepoDynamicQueries;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Repository interface for MahkemeKararTalep entity
 */
@Repository
public interface MahkemeKararTalepRepo extends JpaRepository<MahkemeKararTalep, Long>, MahkemeKararTalepRepoDynamicQueries {

    String mahkemeKararTalepSorguBaseSqlStr = """
            SELECT
                mkt.ID,
                mkt.SORUSTURMA_NO,
                mkt.MAHKEME_KARAR_NO,
                mkt.MAHKEME_KODU,
                ma.MAHKEME_ADI,
                mkt.DURUM,
                mkt.ACIKLAMA,
                mkt.KAYIT_TARIHI,
                mkt.KULLANICI_ID,
                kl.KULLANICI_ADI,
                kl.ADI,
                kl.SOYADI,
                egk.KURUM_KOD,
                egk.KURUM_ADI,
                e.EVRAK_SIRA_NO,
                e.EVRAK_NO,
                e.EVRAK_KONUSU
            FROM
                iym.MAHKEME_KARAR_TALEP mkt
            INNER JOIN iym.MAHKEME_ADI ma ON mkt.MAHKEME_KODU = ma.MAHKEME_KODU
            INNER JOIN iym.KULLANICILAR kl ON mkt.KULLANICI_ID = kl.ID
            INNER JOIN iym.EVRAK_KAYIT e ON mkt.EVRAK_ID = e.ID
            INNER JOIN iym.ILLER i ON mkt.MAHKEME_ILI = i.IL_KOD
            INNER JOIN iym.KULLANICI_KURUM kk ON mkt.KULLANICI_ID = kk.KULLANICI_ID
            INNER JOIN iym.EVRAK_GELEN_KURUMLAR egk ON kk.KURUM_KOD = egk.KURUM_KOD
     """;

    String islenecekEvraklarSqlStr = """
            SELECT mk.* FROM iym.MAHKEME_KARAR_TALEP mkt
            INNER JOIN iym.EVRAK_KAYIT ek ON ek.ID = mkt.EVRAK_ID
            WHERE
                (mkt.DURUM IS NULL
                    OR mkt.DURUM = 'KISMI_IADE')
                AND ek.EVRAK_GELDIGI_KURUM = :kurumKodu

     """;

    List<MahkemeKararTalep> findByEvrakId(Long evrakId);


    @Query(value =islenecekEvraklarSqlStr, nativeQuery = true)
    List<MahkemeKararTalep> islenecekEvrakListesi(@Param("kurumKodu") String kurumKodu);


}
