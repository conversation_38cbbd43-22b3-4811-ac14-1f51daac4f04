import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormArray, FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router } from '@angular/router';

// PrimeNG Imports
import { CardModule } from 'primeng/card';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';

import { SelectModule } from 'primeng/select';
import { DatePickerModule } from 'primeng/datepicker';
import { CheckboxModule } from 'primeng/checkbox';
import { ToastModule } from 'primeng/toast';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { FileUploadModule } from 'primeng/fileupload';
import { TableModule } from 'primeng/table';
import { DialogModule } from 'primeng/dialog';
import { TagModule } from 'primeng/tag';
import { MultiSelectModule } from 'primeng/multiselect';
import { ChipModule } from 'primeng/chip';
import { ConfirmDialog } from 'primeng/confirmdialog';

import { MessageService, ConfirmationService } from 'primeng/api';
import { ErrorHandlingService } from '../shared/services/error-handling.service';
import {
    EvrakDetay,
    Hedef,
    IDHedefDetay,
    IDYeniKararRequest,
    MahkemeKararBilgisi,
    ResponseIllerResponse
} from '../../generated-api';

// Services
import { TalepService } from '../shared/services/talep.service';
import { FileValidatorService } from '../../shared/services/file-validator.service';
import { HttpResponse } from "@angular/common/http";
import KararTuruEnum = IDYeniKararRequest.KararTuruEnum;
import MahkemeKararTipiEnum = MahkemeKararBilgisi.MahkemeKararTipiEnum;
import HedefTipEnum = Hedef.HedefTipEnum;

// Type-safe interfaces for form data
interface HedefFormData {
    hedefNo: string;
    hedefTip: HedefTipEnum;
    hedefAd: string;
    hedefSoyad: string;
    baslamaTarihi: Date;
    sure: number;
    sureTip: IDHedefDetay.SureTipEnum;
    hedefAidiyatKodlari: string[]; // Changed from bimAidiyatKod to hedefAidiyatKodlari array
    canakNo: string;
}

interface TalepFormData {
    // EvrakDetay fields
    evrakNo: string;
    evrakTarihi: Date;
    evrakKurumKodu: string;
    evrakTuru: EvrakDetay.EvrakTuruEnum;
    havaleBirimi: string;
    evrakAciklama: string;
    geldigiIlIlceKodu: string;
    acilmi: boolean;
    evrakKonusu: string;

    // MahkemeKararBilgisi fields
    mahkemeKararTipi: MahkemeKararTipiEnum;

    // MahkemeKararDetay fields
    mahkemeKodu: string;
    mahkemeKararNo: string;
    mahkemeIlIlceKodu: string;
    sorusturmaNo: string;
    mahkemeAciklama: string;

    // ID specific fields
    hedefDetayListesi: HedefFormData[];
    mahkemeAidiyatKodlari: string[];
    mahkemeSucTipiKodlari: string[];
}

@Component({
    selector: 'app-id-yeni-karar',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        CardModule,
        ButtonModule,
        InputTextModule,
        SelectModule,
        DatePickerModule,
        CheckboxModule,
        ToastModule,
        ProgressSpinnerModule,
        FileUploadModule,
        TableModule,
        DialogModule,
        TagModule,
        MultiSelectModule,
        ChipModule,
        ConfirmDialog
    ],
    providers: [ConfirmationService],
    templateUrl: './id-yeni-karar.component.html',
    styleUrls: ['./id-yeni-karar.component.scss']
})
export class IdYeniKararComponent implements OnInit {

    talepForm: FormGroup;
    hedefDialogVisible = false;
    hedefForm: FormGroup;
    editingHedefIndex = -1;
    yukleniyor = false;
    seciliDosya: File | null = null;
    dosyaAdi = '';
    dosyaBoyutu = '';
    dosyaSecildi = false;

    // Dropdown Options
    evrakTuruOptions = [
        { label: 'İletişimin Denetlenmesi', value: EvrakDetay.EvrakTuruEnum.IletisiminDenetlenmesi }
    ];

    mahkemeKararTipOptions = [
        { label: 'Önleyici Hakim Kararı', value: MahkemeKararTipiEnum.OnleyiciHakimKarari },
        { label: 'Adli Hakim Kararı', value: MahkemeKararTipiEnum.AdliHakimKarari },
        { label: 'Adli Yazılı Emir', value: MahkemeKararTipiEnum.AdliYaziliEmir }
    ];

    hedefTipOptions = [
        { label: 'GSM', value: HedefTipEnum.Gsm },
        { label: 'Sabit', value: HedefTipEnum.Sabit },
        { label: 'UYDU', value: HedefTipEnum.Uydu },
        { label: 'Yurt Dışı', value: HedefTipEnum.YurtDisi },
        { label: 'E-posta', value: HedefTipEnum.Eposta },
        { label: 'IMEI', value: HedefTipEnum.Imei },
        { label: 'IMSI', value: HedefTipEnum.Imsi }
    ];

    sureTipiOptions = [
        { label: 'Gün', value: IDHedefDetay.SureTipEnum.Gun },
        { label: 'Ay', value: IDHedefDetay.SureTipEnum.Ay },
        { label: 'Yıl', value: IDHedefDetay.SureTipEnum.Hicbiri }
    ];

    // Dynamic dropdown options from backend
    iller: any[] = [];
    kurumlar: any[] = [];
    mahkemeKodlari: any[] = [];
    sucTipleri: any[] = [];
    sorguTipleri: any[] = [];
    mahkemeKararTipleri: any[] = [];
    tespitTurleri: any[] = [];

    // Loading states
    dropdownLoading = {
        iller: false,
        kurumlar: false,
        mahkemeKodlari: false,
        sucTipleri: false,
        sorguTipleri: false,
        mahkemeKararTipleri: false,
        tespitTurleri: false
    };

    constructor(
        private fb: FormBuilder,
        private talepService: TalepService,
        private messageService: MessageService,
        private confirmationService: ConfirmationService,
        private router: Router,
        private fileValidator: FileValidatorService,
        private errorHandlingService: ErrorHandlingService
    ) {
        this.talepForm = this.createForm();
        this.hedefForm = this.createHedefForm();
    }

    ngOnInit(): void {
        // Component initialization
        this.loadDropdownData();

        // Development mode check for test utilities
        if (this.isDevelopmentMode()) {
            console.log('🧪 Development mode detected - Test utilities available');
            console.log('💡 Use fillTestData() or clearTestData() in console');

            // Expose test methods to global window object
            (window as any).fillTestData = () => this.fillTestData();
            (window as any).clearTestData = () => this.clearTestData();
            (window as any).addTestHedef = (adi?: string, soyadi?: string, hedefNo?: string) => this.addTestHedef(adi, soyadi, hedefNo);
            (window as any).validateTestForm = () => this.validateTestForm();
            (window as any).createTestFile = () => this.createTestFile();

            console.log('✅ Test utilities bound to window object');
        }
    }

    /**
     * Load all dropdown data from backend
     */
    private loadDropdownData(): void {
        this.loadIller();
        this.loadKurumlar();
        this.loadMahkemeKodlari();
        this.loadSucTipleri();
        this.loadSorguTipleri();
        this.loadMahkemeKararTipleri();
        this.loadTespitTurleri();
    }

    private loadIller(): void {
        this.dropdownLoading.iller = true;
        this.talepService.illerGetir().subscribe({
            next: (response: HttpResponse<ResponseIllerResponse>) => {
                if (response.body?.success && response.body?.result?.iller) {
                    this.iller = response.body?.result.iller.map(item => ({
                        label: item.ilAdi + "-" + item.ilceAdi,
                        value: item.ilKod
                    }));
                } else {
                    console.error('İller verisi uygun formatta değil:', response);
                }
                this.dropdownLoading.iller = false;
            },
            error: (error) => {
                this.errorHandlingService.handleDropdownError('İller', () => {
                    this.dropdownLoading.iller = false;
                });
            }
        });
    }

    private loadKurumlar(): void {
        this.dropdownLoading.kurumlar = true;
        this.talepService.kurumlarGetir().subscribe({
            next: (response) => {
                if (response.success && response.result?.kurumlar) {
                    this.kurumlar = response.result.kurumlar.map(item => ({
                        label: item.kurumAdi,
                        value: item.kurumKod
                    }));
                } else {
                    console.error('Kurumlar verisi uygun formatta değil:', response);
                }
                this.dropdownLoading.kurumlar = false;
            },
            error: (error) => {
                this.errorHandlingService.handleDropdownError('Kurumlar', () => {
                    this.dropdownLoading.kurumlar = false;
                });
            }
        });
    }

    private loadMahkemeKodlari(): void {
        this.dropdownLoading.mahkemeKodlari = true;
        this.talepService.mahkemeKodlariSorgula().subscribe({
            next: (response) => {
                if (response.success && response.result?.mahkemeKodListesi) {
                    this.mahkemeKodlari = response.result.mahkemeKodListesi.map(item => ({
                        label: item.mahkemeAdi,
                        value: item.mahkemeKodu
                    }));
                } else {
                    console.error('Mahkeme kodları verisi uygun formatta değil:', response);
                }
                this.dropdownLoading.mahkemeKodlari = false;
            },
            error: (error) => {
                this.errorHandlingService.handleDropdownError('Mahkeme kodları', () => {
                    this.dropdownLoading.mahkemeKodlari = false;
                });
            }
        });
    }

    private loadSucTipleri(): void {
        this.dropdownLoading.sucTipleri = true;
        this.talepService.sucTipleriGetir().subscribe({
            next: (response) => {
                if (response.success && response.result?.sucTipleri) {
                    this.sucTipleri = response.result.sucTipleri.map(item => ({
                        label: item.aciklama,
                        value: item.sucTipiKodu
                    }));
                } else {
                    console.error('Suç tipleri verisi uygun formatta değil:', response);
                }
                this.dropdownLoading.sucTipleri = false;
            },
            error: (error) => {
                this.errorHandlingService.handleDropdownError('Suç tipleri', () => {
                    this.dropdownLoading.sucTipleri = false;
                });
            }
        });
    }

    private loadSorguTipleri(): void {
        this.dropdownLoading.sorguTipleri = true;
        this.talepService.sorguTipleriGetir().subscribe({
            next: (response) => {
                if (response.success && response.result?.sorguTipleri) {
                    this.sorguTipleri = response.result.sorguTipleri.map(item => ({
                        label: item.aciklama,
                        value: item.sorguTipi
                    }));
                } else {
                    console.error('Sorgu tipleri verisi uygun formatta değil:', response);
                }
                this.dropdownLoading.sorguTipleri = false;
            },
            error: (error) => {
                this.errorHandlingService.handleDropdownError('Sorgu tipleri', () => {
                    this.dropdownLoading.sorguTipleri = false;
                });
            }
        });
    }

    private loadMahkemeKararTipleri(): void {
        this.dropdownLoading.mahkemeKararTipleri = true;
        this.talepService.mahkemeKararTipleriGetir().subscribe({
            next: (response) => {
                if (response.success && response.result?.mahkemeKararTipiListesi) {
                    this.mahkemeKararTipleri = response.result.mahkemeKararTipiListesi.map(item => ({
                        label: item.kararTipi,
                        value: item.kararKodu
                    }));
                } else {
                    console.error('Mahkeme karar tipleri verisi uygun formatta değil:', response);
                }
                this.dropdownLoading.mahkemeKararTipleri = false;
            },
            error: (error) => {
                this.errorHandlingService.handleDropdownError('Mahkeme karar tipleri', () => {
                    this.dropdownLoading.mahkemeKararTipleri = false;
                });
            }
        });
    }

    private loadTespitTurleri(): void {
        this.dropdownLoading.tespitTurleri = true;
        this.talepService.tespitTurleriGetir().subscribe({
            next: (response) => {
                if (response.success && response.result?.tespitTurleri) {
                    this.tespitTurleri = response.result.tespitTurleri.map(item => ({
                        label: item.aciklama,
                        value: item.tespitTuru
                    }));
                } else {
                    console.error('Tespit türleri verisi uygun formatta değil:', response);
                }
                this.dropdownLoading.tespitTurleri = false;
            },
            error: (error) => {
                this.errorHandlingService.handleDropdownError('Tespit türleri', () => {
                    this.dropdownLoading.tespitTurleri = false;
                });
            }
        });
    }

    /**
    * Geliştirme modunda olup olmadığını kontrol eder
    * Test verilerini sadece geliştirme modunda kullanabilmek için
    * @returns boolean - Geliştirme modunda ise true, değilse false
    */
    private isDevelopmentMode(): boolean {
        // Geliştirme ortamı kontrolü
        const isLocalhost = window.location.hostname === 'localhost' ||
            window.location.hostname === '127.0.0.1';
        const isDevelopmentPort = window.location.port === '4200' ||
            window.location.port === '4201' ||
            window.location.port === '4202';

        // Geliştirme ortamında olduğumuzu belirten URL parametresi kontrolü
        const urlParams = new URLSearchParams(window.location.search);
        const devMode = urlParams.get('devMode') === 'true';

        return isLocalhost || isDevelopmentPort || devMode;
    }

    // Test data utilities for development
    fillTestData(): void {
        if (!this.isDevelopmentMode()) {
            console.warn('Test data utilities only available in development mode');
            return;
        }

        this.talepForm.patchValue({
            // Evrak Detayları
            evrakNo: '2024-TEST-001',
            evrakTarihi: new Date('2024-01-15'),
            evrakKurumKodu: '02', // EGMIDB kurumu
            evrakTuru: EvrakDetay.EvrakTuruEnum.IletisiminDenetlenmesi,
            havaleBirimi: 'TEST BRM',
            evrakAciklama: 'Test amaçlı yeni karar talebi',
            geldigiIlIlceKodu: '3400', // İSTANBUL
            acilmi: true,
            evrakKonusu: 'Test konusu',

            // Mahkeme Karar Detayları
            mahkemeKararTipi: MahkemeKararTipiEnum.OnleyiciHakimKarari,
            mahkemeKodu: '08030100', // ARTVİN BORÇKA CBS
            mahkemeKararNo: '2024/12345',
            mahkemeIlIlceKodu: '3400', // İSTANBUL
            sorusturmaNo: '2024-SOR-001',
            mahkemeAciklama: 'Test mahkeme kararı',

            // Ek alanlar - EGMIDB için aidiyat kodu Y ile başlamalı ve 8 karakter olmalı
            mahkemeAidiyatKodlari: ['********'], // EGMIDB için geçerli aidiyat kodu
            mahkemeSucTipiKodlari: ['1312'] // Silahli Örgüt (TCK 314) veya Örgütlere Silah Sag. (TCK 315)
        });

        // Add sample targets
        this.addTestHedef();
        this.addTestHedef('Ahmet', 'Test', '2');

        // Create test file
        this.createTestFile();

        // Otomatik olarak form gönderme işlemini başlat
        this.onSubmit();

        console.log('✅ Test data loaded successfully!');
        console.log('📋 Form values:', this.talepForm.value);
    }

    addTestHedef(adi: string = 'Mehmet', soyadi: string = 'Örnek', hedefNo: string = '1'): void {
        const testHedef = this.fb.group({
            hedefNo: [hedefNo, Validators.required],
            hedefTip: [HedefTipEnum.Gsm, Validators.required],
            hedefAd: [adi, Validators.required],
            hedefSoyad: [soyadi, Validators.required],
            baslamaTarihi: [new Date(), Validators.required],
            sure: ['30'],
            sureTip: [IDHedefDetay.SureTipEnum.Gun, Validators.required],
            hedefAidiyatKodlari: [['AIDIYAT1', 'AIDIYAT2'], Validators.required], // Geçerli aidiyat kodları array'i
            canakNo: ['CN-TEST-001']
        });

        this.hedefDetayListesi.push(testHedef);
        console.log(`🎯 Added test hedef: ${adi} ${soyadi}`);
    }

    /**
     * Test amaçlı PDF dosyası oluşturur
     * İletişimin denetlenmesi yeni karar için örnek PDF içeriği oluşturur
     */
    createTestFile(): void {
        // PDF için basit bir ArrayBuffer oluştur (gerçek bir PDF değil, sadece test amaçlı)
        const pdfHeader = new Uint8Array([
            0x25, 0x50, 0x44, 0x46, 0x2d, 0x31, 0x2e, 0x34, // %PDF-1.4
            0x0a, 0x25, 0xf6, 0xe4, 0xfc, 0xdf, 0x0a     // Binary comment
        ]);

        // Basit bir PDF içeriği oluştur
        const pdfContent = new Uint8Array([
            // PDF içeriği (basit bir PDF yapısı)
            0x31, 0x20, 0x30, 0x20, 0x6f, 0x62, 0x6a, 0x0a,  // 1 0 obj
            0x3c, 0x3c, 0x2f, 0x54, 0x79, 0x70, 0x65, 0x2f,  // <</Type/
            0x43, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2f,  // Catalog/
            0x50, 0x61, 0x67, 0x65, 0x73, 0x20, 0x32, 0x20,  // Pages 2
            0x30, 0x20, 0x52, 0x3e, 0x3e, 0x0a, 0x65, 0x6e,  // 0 R>>\nend
            0x64, 0x6f, 0x62, 0x6a, 0x0a                      // obj
        ]);

        // PDF içeriğini birleştir
        const pdfData = new Uint8Array(pdfHeader.length + pdfContent.length);
        pdfData.set(pdfHeader);
        pdfData.set(pdfContent, pdfHeader.length);

        // Blob ve File nesnelerini oluştur
        const blob = new Blob([pdfData], { type: 'application/pdf' });
        this.seciliDosya = new File([blob], 'test-karar.pdf', { type: 'application/pdf' });

        // Dosya seçildi bilgisini güncelle
        this.dosyaSecildi = true;
        this.dosyaAdi = 'test-karar.pdf';
        this.dosyaBoyutu = Math.round(blob.size / 1024) + ' KB';

        console.log('📄 PDF test file created: test-karar.pdf', this.seciliDosya);
    }

    clearTestData(): void {
        if (!this.isDevelopmentMode()) {
            console.warn('Test data utilities only available in development mode');
            return;
        }

        this.talepForm.reset();
        this.hedefDetayListesi.clear();
        this.seciliDosya = null;

        // Reset to defaults
        this.talepForm.patchValue({
            evrakTuru: EvrakDetay.EvrakTuruEnum.IletisiminDenetlenmesi,
            evrakTarihi: new Date()
        });

        console.log('🧹 Test data cleared');
    }

    validateTestForm(): void {
        const isValid = this.talepForm.valid;
        console.log('📊 Form Status:', isValid ? 'Valid' : 'Invalid');
        if (!isValid) {
            console.log('❌ Validation errors:', this.talepForm.errors);
            Object.keys(this.talepForm.controls).forEach(key => {
                const control = this.talepForm.get(key);
                if (control && control.invalid) {
                    console.log(`❌ ${key}:`, control.errors);
                }
            });
        }
    }

    private createForm(): FormGroup {
        return this.fb.group({
            // EvrakDetay fields
            evrakNo: ['', [Validators.required, Validators.maxLength(50)]],
            evrakTarihi: [new Date(), Validators.required],
            evrakKurumKodu: ['', Validators.required],
            evrakTuru: [EvrakDetay.EvrakTuruEnum.IletisiminDenetlenmesi, Validators.required],
            havaleBirimi: ['', [Validators.maxLength(10)]],
            evrakAciklama: [''],
            geldigiIlIlceKodu: ['', Validators.required],
            acilmi: [false],
            evrakKonusu: [''],

            // MahkemeKararBilgisi fields
            mahkemeKararTipi: [null, Validators.required],

            // MahkemeKararDetay fields
            mahkemeKodu: ['', Validators.required],
            mahkemeKararNo: ['', Validators.required],
            mahkemeIlIlceKodu: ['', Validators.required],
            sorusturmaNo: [''],
            mahkemeAciklama: [''],

            // ID specific fields
            hedefDetayListesi: this.fb.array([]),
            mahkemeAidiyatKodlari: [[]],
            mahkemeSucTipiKodlari: [[]]
        });
    }

    private createHedefForm(): FormGroup {
        return this.fb.group({
            hedefNo: ['', Validators.required],
            hedefTip: [null, Validators.required],
            hedefAd: ['', Validators.required],
            hedefSoyad: ['', Validators.required],
            baslamaTarihi: [new Date(), Validators.required],
            sure: [''],
            sureTip: [null, Validators.required],
            hedefAidiyatKodlari: [[], Validators.required],
            canakNo: ['']
        });
    }

    get hedefDetayListesi(): FormArray {
        return this.talepForm.get('hedefDetayListesi') as FormArray;
    }

    get hedefler(): HedefFormData[] {
        return this.hedefDetayListesi.value;
    }

    hedefEkleDialog(): void {
        this.editingHedefIndex = -1;
        this.hedefForm.reset();
        this.hedefForm.patchValue({
            baslamaTarihi: new Date(),
            sureTip: IDHedefDetay.SureTipEnum.Gun
        });

        this.hedefDialogVisible = true;
    }

    hedefDuzenleDialog(index: number): void {
        this.editingHedefIndex = index;
        const hedef: HedefFormData = this.hedefDetayListesi.at(index).value;

        console.log('🔍 Editing hedef data:', hedef);
        console.log('🔍 Original hedefAidiyatKodlari:', hedef.hedefAidiyatKodlari);
        console.log('🔍 Type of hedefAidiyatKodlari:', typeof hedef.hedefAidiyatKodlari);

        // Handle hedefAidiyatKodlari - it might be stored as string or array
        let aidiyatKodlariArray: string[] = [];

        if (Array.isArray(hedef.hedefAidiyatKodlari)) {
            // If it's already an array, use it directly
            aidiyatKodlariArray = hedef.hedefAidiyatKodlari;
            console.log('✅ Using array directly:', aidiyatKodlariArray);
        } else if (typeof hedef.hedefAidiyatKodlari === 'string') {
            // If it's a string, split it into array
            const aidiyatString = hedef.hedefAidiyatKodlari as string;
            aidiyatKodlariArray = aidiyatString
                .split(',')
                .map((kod: string) => kod.trim())
                .filter((kod: string) => kod.length > 0 && kod !== 'null');
            console.log('✅ Converted string to array:', aidiyatKodlariArray);
        } else {
            console.log('⚠️ Unknown format, using empty array');
        }

        console.log('🔍 Final aidiyatKodlariArray:', aidiyatKodlariArray);

        this.hedefForm.patchValue({
            ...hedef,
            hedefAidiyatKodlari: aidiyatKodlariArray,
            baslamaTarihi: new Date(hedef.baslamaTarihi)
        });
        this.hedefDialogVisible = true;
    }

    hedefKaydet(): void {
        if (this.hedefForm.valid) {
            // Ensure hedefAidiyatKodlari is always an array
            const aidiyatKodlariArray = Array.isArray(this.hedefForm.value.hedefAidiyatKodlari) ?
                this.hedefForm.value.hedefAidiyatKodlari :
                [];

            const hedefData: HedefFormData = {
                ...this.hedefForm.value,
                hedefAidiyatKodlari: aidiyatKodlariArray,
                baslamaTarihi: this.hedefForm.value.baslamaTarihi.toISOString()
            };

            if (this.editingHedefIndex >= 0) {
                // Güncelleme
                const hedefGroup = this.hedefDetayListesi.at(this.editingHedefIndex) as FormGroup;
                hedefGroup.patchValue(hedefData);
                this.messageService.add({
                    severity: 'success',
                    summary: 'Başarılı',
                    detail: 'Hedef güncellendi',
                    life: 3000
                });
            } else {
                // Yeni ekleme
                this.hedefDetayListesi.push(this.fb.group({
                    hedefNo: [hedefData.hedefNo, Validators.required],
                    hedefTip: [hedefData.hedefTip, Validators.required],
                    hedefAd: [hedefData.hedefAd, Validators.required],
                    hedefSoyad: [hedefData.hedefSoyad, Validators.required],
                    baslamaTarihi: [hedefData.baslamaTarihi, Validators.required],
                    sure: [hedefData.sure],
                    sureTip: [hedefData.sureTip, Validators.required],
                    hedefAidiyatKodlari: [hedefData.hedefAidiyatKodlari],
                    canakNo: [hedefData.canakNo]
                }));
                this.messageService.add({
                    severity: 'success',
                    summary: 'Başarılı',
                    detail: 'Hedef eklendi',
                    life: 3000
                });
            }

            this.hedefDialogVisible = false;
            this.hedefForm.reset();
        } else {
            this.messageService.add({
                severity: 'warn',
                summary: 'Uyarı',
                detail: 'Lütfen tüm zorunlu alanları doldurun'
            });
        }
    }

    hedefSil(index: number): void {
        this.hedefDetayListesi.removeAt(index);
        this.messageService.add({
            severity: 'info',
            summary: 'Bilgi',
            detail: 'Hedef silindi'
        });
    }

    async onDosyaSecildi(event: any): Promise<void> {
        const dosyalar = event.files;
        if (!dosyalar?.length) return;

        for (let i = 0; i < dosyalar.length; i++) {
            const file = dosyalar[i];

            // Validate PDF file
            const validation = await this.fileValidator.validatePdfFile(file);

            if (validation.valid) {
                this.seciliDosya = file;
                this.dosyaSecildi = true;
                this.dosyaAdi = file.name;
                this.dosyaBoyutu = Math.round(file.size / 1024) + ' KB';

                this.messageService.add({
                    severity: 'info',
                    summary: 'Dosya Seçildi',
                    detail: `${file.name} dosyası doğrulandı ve seçildi`
                });

                // Only take the first valid PDF file
                break;
            } else {
                this.messageService.add({
                    severity: 'error',
                    summary: 'Hata',
                    detail: `${file.name}: ${validation.error}`
                });
            }
        }

        // If no valid file was found
        if (!this.seciliDosya) {
            event.target.value = '';
            this.dosyaSecildi = false;
            this.messageService.add({
                severity: 'error',
                summary: 'Hata',
                detail: 'Lütfen geçerli bir PDF dosyası seçin'
            });
        }
    }

    async onSubmit(): Promise<void> {
        if (this.talepForm.valid && this.hedefDetayListesi.length > 0 && this.seciliDosya) {
            // Validate file again before upload
            const validation = await this.fileValidator.validatePdfFile(this.seciliDosya);
            if (!validation.valid) {
                this.messageService.add({
                    severity: 'error',
                    summary: 'Hata',
                    detail: `Dosya doğrulama hatası: ${validation.error}`
                });
                return;
            }

            this.yukleniyor = true;

            const formData: TalepFormData = this.talepForm.value;

            // Create request object with proper hedefDetayListesi structure using IDHedefDetay interface
            const hedefDetayListesi: IDHedefDetay[] = formData.hedefDetayListesi.map((hedef: HedefFormData) => ({
                hedefNoAdSoyad: {
                    hedef: {
                        hedefNo: hedef.hedefNo,
                        hedefTip: hedef.hedefTip
                    },
                    hedefAd: hedef.hedefAd,
                    hedefSoyad: hedef.hedefSoyad
                },
                baslamaTarihi: hedef.baslamaTarihi instanceof Date
                    ? hedef.baslamaTarihi.toISOString().replace('Z', '')
                    : new Date(hedef.baslamaTarihi).toISOString().replace('Z', ''),
                sure: parseInt(hedef.sure.toString()) || 30,
                sureTip: hedef.sureTip,
                canakNo: hedef.canakNo,
                hedefAidiyatKodlari: hedef.hedefAidiyatKodlari || [] // Added hedefAidiyatKodlari to request
            }));

            const request: IDYeniKararRequest = {
                id: this.generateUUID(),
                kararTuru: KararTuruEnum.IletisiminDenetlenmesiYeniKarar,
                evrakDetay: {
                    evrakNo: formData.evrakNo,
                    evrakTarihi: formData.evrakTarihi instanceof Date
                        ? formData.evrakTarihi.toISOString().replace('Z', '')
                        : new Date(formData.evrakTarihi).toISOString().replace('Z', ''),
                    evrakKurumKodu: formData.evrakKurumKodu,
                    evrakTuru: formData.evrakTuru,
                    havaleBirimi: formData.havaleBirimi,
                    aciklama: formData.evrakAciklama,
                    geldigiIlIlceKodu: formData.geldigiIlIlceKodu,
                    acilmi: formData.acilmi,
                    evrakKonusu: formData.evrakKonusu
                },
                mahkemeKararBilgisi: {
                    mahkemeKararTipi: formData.mahkemeKararTipi,
                    mahkemeKararDetay: {
                        mahkemeKodu: formData.mahkemeKodu,
                        mahkemeKararNo: formData.mahkemeKararNo,
                        mahkemeIlIlceKodu: formData.mahkemeIlIlceKodu,
                        sorusturmaNo: formData.sorusturmaNo,
                        aciklama: formData.mahkemeAciklama
                    }
                },
                hedefDetayListesi: hedefDetayListesi,
                mahkemeAidiyatKodlari: formData.mahkemeAidiyatKodlari || [],
                mahkemeSucTipiKodlari: formData.mahkemeSucTipiKodlari || []
            };

            console.log('📤 Gönderilen istek:', request);
            console.log('📎 Gönderilen dosya:', this.seciliDosya);

            // Send request
            this.talepService.yeniKararIdGonder(request, this.seciliDosya).subscribe({
                next: (response) => {
                    this.yukleniyor = false;
                    console.log('✅ Başarılı yanıt:', response);

                    // New Response wrapper format - extract data from response.result
                    const responseData = response?.result;
                    const btkEvrakId = responseData?.btkEvrakId || 'Belirtilmemiş';

                    this.messageService.add({
                        severity: 'success',
                        summary: 'Başarılı',
                        detail: `ID Yeni Karar talebi başarıyla gönderildi.\nBTK Evrak ID: ${btkEvrakId}`,
                        sticky: true
                    });

                    this.talepForm.reset();
                    this.hedefDetayListesi.clear();
                    this.seciliDosya = null;
                    this.dosyaSecildi = false;
                    this.dosyaAdi = '';
                    this.dosyaBoyutu = '';
                },
                error: (err) => {
                    this.errorHandlingService.handleError(err, () => {
                        this.yukleniyor = false;
                    });
                }
            });
        } else {
            let errorMessage = 'Lütfen kontrol edin: ';
            if (!this.talepForm.valid) errorMessage += 'Form alanları, ';
            if (this.hedefDetayListesi.length === 0) errorMessage += 'En az bir hedef, ';
            if (!this.seciliDosya) errorMessage += 'Dosya seçimi ';

            this.messageService.add({
                severity: 'warn',
                summary: 'Uyarı',
                detail: errorMessage
            });
            this.markFormGroupTouched();
        }
    }

    private markFormGroupTouched(): void {
        Object.keys(this.talepForm.controls).forEach(key => {
            const control = this.talepForm.get(key);
            control?.markAsTouched();
        });
    }

    private generateUUID(): string {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    }

    onReset(): void {
        this.talepForm.reset();
        this.hedefDetayListesi.clear();
        this.seciliDosya = null;


        this.messageService.add({
            severity: 'info',
            summary: 'Form Sıfırlandı',
            detail: 'Tüm alanlar temizlendi'
        });
    }

    isFieldInvalid(fieldName: string): boolean {
        const field = this.talepForm.get(fieldName);
        return !!(field && field.invalid && (field.dirty || field.touched));
    }

    isHedefFieldInvalid(fieldName: string): boolean {
        const field = this.hedefForm.get(fieldName);
        return !!(field && field.invalid && (field.dirty || field.touched));
    }

    getFieldError(fieldName: string): string {
        const field = this.talepForm.get(fieldName);
        if (field?.errors) {
            if (field.errors['required']) {
                return 'Bu alan zorunludur';
            }
            if (field.errors['maxlength']) {
                return `Maksimum ${field.errors['maxlength'].requiredLength} karakter olmalıdır`;
            }
        }
        return '';
    }

    getHedefFieldError(fieldName: string): string {
        const field = this.hedefForm.get(fieldName);
        if (field?.errors) {
            if (field.errors['required']) {
                return 'Bu alan zorunludur';
            }
        }
        return '';
    }

    formatTarih(tarih: string): string {
        return new Date(tarih).toLocaleDateString('tr-TR');
    }

    getHedefTipLabel(value: HedefTipEnum): string {
        const option = this.hedefTipOptions.find(opt => opt.value === value);
        return option ? option.label : value;
    }

    getSureTipiLabel(value: IDHedefDetay.SureTipEnum): string {
        const option = this.sureTipiOptions.find(opt => opt.value === value);
        return option ? option.label : value;
    }

    hedefDialogKapat(): void {
        this.hedefDialogVisible = false;
        this.hedefForm.reset();
        this.editingHedefIndex = -1;

    }











    getHedefAidiyatKodlariArray(aidiyatKodlari: any): string[] {
        if (!aidiyatKodlari) {
            return [];
        }

        if (Array.isArray(aidiyatKodlari)) {
            return aidiyatKodlari.filter((kod: string) => kod && kod.trim().length > 0);
        }

        if (typeof aidiyatKodlari === 'string') {
            return aidiyatKodlari
                .split(',')
                .map((kod: string) => kod.trim())
                .filter((kod: string) => kod.length > 0 && kod !== 'null');
        }

        return [];
    }

    getHedefAidiyatKodlariDisplay(aidiyatKodlari: any): string {
        const array = this.getHedefAidiyatKodlariArray(aidiyatKodlari);
        return array.length > 0 ? array.join(', ') : 'Belirtilmemiş';
    }






}
