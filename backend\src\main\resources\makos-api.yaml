openapi: 3.0.1
info:
  title: MAKOS OpenAPI definition
  description: MAKOS Application
  version: v1.0
servers:
- url: http://localhost:5000/makosapi
  description: Generated server url
security:
- BasicAuth: []
paths:
  /user/update:
    post:
      tags:
      - user-controller
      operationId: updateUser
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/UpdateUserResponse'
  /user/delete:
    post:
      tags:
      - user-controller
      operationId: deleteUser
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteUserRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/DeleteUserResponse'
  /user/deactivate:
    post:
      tags:
      - user-controller
      operationId: deactivateUser
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UsernameRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/DeactivateUserResponse'
  /user/add:
    post:
      tags:
      - user-controller
      operationId: addUser
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddUserRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AddUserResponse'
  /user/activate:
    post:
      tags:
      - user-controller
      operationId: activateUser
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UsernameRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ActivateUserResponse'
  /mahkemeKararTalep/yenikararIT:
    post:
      tags:
      - mahkeme-karar-talep-controller
      operationId: yenikararIT
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
              - mahkemeKararDetay
              - mahkemeKararDosyasi
              type: object
              properties:
                mahkemeKararDosyasi:
                  type: string
                  format: binary
                mahkemeKararDetay:
                  $ref: '#/components/schemas/ITKararRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ITKararResponse'
  /mahkemeKararTalep/yeniKararID:
    post:
      tags:
      - mahkeme-karar-talep-controller
      operationId: yeniKararID
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
              - mahkemeKararDetay
              - mahkemeKararDosyasi
              type: object
              properties:
                mahkemeKararDosyasi:
                  type: string
                  format: binary
                mahkemeKararDetay:
                  $ref: '#/components/schemas/IDYeniKararRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/IDYeniKararResponse'
  /mahkemeKararTalep/uzatmaKarariID:
    post:
      tags:
      - mahkeme-karar-talep-controller
      operationId: uzatmaKarariID
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
              - mahkemeKararDetay
              - mahkemeKararDosyasi
              type: object
              properties:
                mahkemeKararDosyasi:
                  type: string
                  format: binary
                mahkemeKararDetay:
                  $ref: '#/components/schemas/IDUzatmaKarariRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/IDUzatmaKarariResponse'
  /mahkemeKararTalep/talepGuncelle:
    post:
      tags:
      - mahkeme-karar-talep-controller
      operationId: talepGuncelle
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MahkemeKararTalepStateUpdateRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/MahkemeKararTalepStateUpdateResponse'
  /mahkemeKararTalep/sucTipiGuncelle:
    post:
      tags:
      - mahkeme-karar-talep-controller
      operationId: sucTipiGuncelle
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
              - mahkemeKararDetay
              - mahkemeKararDosyasi
              type: object
              properties:
                mahkemeKararDosyasi:
                  type: string
                  format: binary
                mahkemeKararDetay:
                  $ref: '#/components/schemas/IDSucTipiGuncellemeRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/IDSucTipiGuncellemeResponse'
  /mahkemeKararTalep/sonlandirmaKarariID:
    post:
      tags:
      - mahkeme-karar-talep-controller
      operationId: sonlandirmaKarariID
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
              - mahkemeKararDetay
              - mahkemeKararDosyasi
              type: object
              properties:
                mahkemeKararDosyasi:
                  type: string
                  format: binary
                mahkemeKararDetay:
                  $ref: '#/components/schemas/IDSonlandirmaKarariRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/IDSonlandirmaKarariResponse'
  /mahkemeKararTalep/mahkemeKararTalepSorgu:
    post:
      tags:
      - mahkeme-karar-talep-controller
      operationId: mahkemeKararTalepSorgu
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IDMahkemeKararTalepSorgulamaRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/IDMahkemeKararTalepSorgulamaResponse'
  /mahkemeKararTalep/mahkemeKararTalepBilgisi:
    post:
      tags:
      - mahkeme-karar-talep-controller
      operationId: mahkemeKararTalepBilgisi
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MahkemeKararTalepBilgisiRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/MahkemeKararTalepQueryResponse'
  /mahkemeKararTalep/mahkemeBilgisiGuncelle:
    post:
      tags:
      - mahkeme-karar-talep-controller
      operationId: mahkemeBilgisiGuncelle
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
              - mahkemeKararDetay
              - mahkemeKararDosyasi
              type: object
              properties:
                mahkemeKararDosyasi:
                  type: string
                  format: binary
                mahkemeKararDetay:
                  $ref: '#/components/schemas/IDMahkemeKararGuncellemeRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/IDMahkemeKararGuncellemeResponse'
  /mahkemeKararTalep/islenecekKararListele:
    post:
      tags:
      - mahkeme-karar-talep-controller
      operationId: islenecekKararListele
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IDMahkemeKararTalepIslenecekRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/IDMahkemeKararTalepIslenecekResponse'
  /mahkemeKararTalep/hedefBilgisiGuncelle:
    post:
      tags:
      - mahkeme-karar-talep-controller
      operationId: hedefBilgisiGuncelle
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
              - mahkemeKararDetay
              - mahkemeKararDosyasi
              type: object
              properties:
                mahkemeKararDosyasi:
                  type: string
                  format: binary
                mahkemeKararDetay:
                  $ref: '#/components/schemas/IDHedefGuncellemeRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/IDHedefGuncellemeResponse'
  /mahkemeKararTalep/aidiyatBilgisiGuncelle:
    post:
      tags:
      - mahkeme-karar-talep-controller
      operationId: aidiyatBilgisiGuncelle
      requestBody:
        content:
          multipart/form-data:
            schema:
              required:
              - mahkemeKararDetay
              - mahkemeKararDosyasi
              type: object
              properties:
                mahkemeKararDosyasi:
                  type: string
                  format: binary
                mahkemeKararDetay:
                  $ref: '#/components/schemas/IDAidiyatBilgisiGuncellemeRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/IDAidiyatBilgisiGuncellemeResponse'
  /mahkemeKararIslem/mahkemeKararTalepIslemSorguID:
    post:
      tags:
      - mahkeme-karar-islem-controller
      operationId: mahkemeKararTalepIslemSorgu
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IDMahkemeKararTalepSorgulamaRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/IDMahkemeKararTalepSorgulamaResponse'
  /mahkemeKararIslem/mahkemeKararIslemBilgisi:
    post:
      tags:
      - mahkeme-karar-islem-controller
      operationId: mahkemeKararTalepBilgisi_1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MahkemeKararTalepBilgisiRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/MahkemeKararTalepQueryResponse'
  /iletisiminTespiti/mahkemeKararSorgu:
    post:
      tags:
      - iletisimin-tespiti-controller
      operationId: mahkemeKararTalepSorgu_1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IDMahkemeKararSorgulamaRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/IDMahkemeKararSorgulamaResponse'
  /iletisiminTespiti/kararAtama:
    post:
      tags:
      - iletisimin-tespiti-controller
      operationId: mahkemeKararAtama
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MahkemeKararTalepByKararIdRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/MahkemeKararTalepQueryResponse'
  /iletisiminTespiti/islenecekKararListesi:
    post:
      tags:
      - iletisimin-tespiti-controller
      operationId: islenecekKararListesi
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IDMahkemeKararIslenecekRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/IDMahkemeKararSorgulamaResponse'
  /iletisiminTespiti/evrakDurumSorgusu:
    post:
      tags:
      - iletisimin-tespiti-controller
      operationId: evrakDurumSorgu
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IDEvrakDurumSorgulamaRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/IDEvrakDurumSorgulamaResponse'
  /iletisiminDenetlenmesiBtk/islenecekIDEvrakListesi:
    post:
      tags:
      - iletisimin-denetlenmesi-btk-controller
      operationId: islenecekIDEvrakListesi
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IDIslenecekEvrakListesiRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/IDIslenecekEvrakListesiResponse'
  /iletisiminDenetlenmesiBtk/idMahkemeKararSorgu:
    post:
      tags:
      - iletisimin-denetlenmesi-btk-controller
      operationId: idMahkemeKararSorgu
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IDMahkemeKararSorgulamaRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/IDMahkemeKararSorgulamaResponse'
  /iletisiminDenetlenmesiBtk/idEvrakTanimlama:
    post:
      tags:
      - iletisimin-denetlenmesi-btk-controller
      operationId: idEvrakTanimlama
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IDMahkemeKararAtamaRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/IDMahkemeKararOnaylamaResponse'
  /iletisiminDenetlenmesiBtk/idEvrakOnaylama:
    post:
      tags:
      - iletisimin-denetlenmesi-btk-controller
      operationId: idEvrakOnaylama
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IDMahkemeKararAtamaRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/IDMahkemeKararOnaylamaResponse'
  /iletisiminDenetlenmesiBtk/idEvrakAtama:
    post:
      tags:
      - iletisimin-denetlenmesi-btk-controller
      operationId: idEvrakAtama
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IDEvrakAtamaRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/IDEvrakAtamaResponse'
  /iletisiminDenetlenmesiBtk/idEvrakAtamaKaldir:
    post:
      tags:
      - iletisimin-denetlenmesi-btk-controller
      operationId: idEvrakAtamaKaldir
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IDEvrakAtamaRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/IDEvrakAtamaResponse'
  /iletisiminDenetlenmesiBtk/idEvrakAtamaHistory:
    post:
      tags:
      - iletisimin-denetlenmesi-btk-controller
      operationId: idEvrakAtamaHistory
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IDEvrakAtamaHistoryRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/IDEvrakAtamaHistoryResponse'
  /iletisiminDenetlenmesi/mahkemeKararSorgu:
    post:
      tags:
      - iletisimin-denetlenmesi-controller
      operationId: mahkemeKararTalepSorgu_2
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IDMahkemeKararSorgulamaRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/IDMahkemeKararSorgulamaResponse'
  /iletisiminDenetlenmesi/mahkemeKararBilgisi:
    post:
      tags:
      - iletisimin-denetlenmesi-controller
      operationId: mahkemeKararTalepBilgisi_2
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MahkemeKararTalepBilgisiRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/MahkemeKararTalepQueryResponse'
  /iletisiminDenetlenmesi/kararOnaylama:
    post:
      tags:
      - iletisimin-denetlenmesi-controller
      operationId: mahkemeKararOnaylama
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IDMahkemeKararAtamaRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/IDMahkemeKararOnaylamaResponse'
  /iletisiminDenetlenmesi/kararAtama:
    post:
      tags:
      - iletisimin-denetlenmesi-controller
      operationId: mahkemeKararAtama_1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IDMahkemeKararAtamaRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/IDMahkemeKararAtamaResponse'
  /iletisiminDenetlenmesi/islenecekKararListesi:
    post:
      tags:
      - iletisimin-denetlenmesi-controller
      operationId: islenecekKararListesi_1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IDMahkemeKararIslenecekRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/IDMahkemeKararSorgulamaResponse'
  /evrak/mahkemeKararSorgu:
    post:
      tags:
      - evrak-controller
      operationId: evrakDurumSorgu_1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IDEvrakDurumSorgulamaRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/IDEvrakDurumSorgulamaResponse'
  /auth/register:
    post:
      tags:
      - auth-controller
      operationId: register
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegisterRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/RegisterResponse'
  /auth/login:
    post:
      tags:
      - auth-controller
      operationId: login
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/LoginResponse'
  /auth/changePassword:
    post:
      tags:
      - auth-controller
      operationId: changePassword
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChangePasswordRequest'
        required: true
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/ChangePasswordResponse'
  /user:
    get:
      tags:
      - user-controller
      operationId: getUser
      parameters:
      - name: username
        in: query
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/GetUserResponse'
  /user/{id}:
    get:
      tags:
      - user-controller
      operationId: getUserById
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/GetUserByIdResponse'
  /user/getUsersForAdmin:
    get:
      tags:
      - user-controller
      operationId: getUsersForAdmin
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/UsersListResponse'
  /user/getAllUsers:
    get:
      tags:
      - user-controller
      operationId: getAllUsers
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/UsersListResponse'
  /iymDomainSorgu/tespitTurleri:
    get:
      tags:
      - iym-domain-sorgu-controller
      operationId: tespitTurleri
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/TespitTuruListResponse'
  /iymDomainSorgu/sucTipleri:
    get:
      tags:
      - iym-domain-sorgu-controller
      operationId: sucTipleri
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/SucTipleriResponse'
  /iymDomainSorgu/sorguTipleri:
    get:
      tags:
      - iym-domain-sorgu-controller
      operationId: sorguTipleri
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/SorguTipiListResponse'
  /iymDomainSorgu/mahkemeKodlari:
    get:
      tags:
      - iym-domain-sorgu-controller
      operationId: mahkemeKodlari
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/MahkemeKodlariResponse'
  /iymDomainSorgu/mahkemeKararTipleri:
    get:
      tags:
      - iym-domain-sorgu-controller
      operationId: mahkemeKararTipleri
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/MahkemeKararTipleriResponse'
  /iymDomainSorgu/kurumlar:
    get:
      tags:
      - iym-domain-sorgu-controller
      operationId: kurumlar
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/EvrakGelenKurumlarResponse'
  /iymDomainSorgu/iller:
    get:
      tags:
      - iym-domain-sorgu-controller
      operationId: iller
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/IllerResponse'
  /iymDomainSorgu/aidiyatlar:
    get:
      tags:
      - iym-domain-sorgu-controller
      operationId: aidiyatlar
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/AidiyatResponse'
  /check/healthCheck:
    get:
      tags:
      - health-check-controller
      operationId: healthCheck
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/HealthCheckResponse'
  /check/healthCheckQueryAdmin:
    get:
      tags:
      - health-check-controller
      operationId: healthCheckQueryAdmin
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/HealthCheckResponse'
  /check/healthCheckAuthorized:
    get:
      tags:
      - health-check-controller
      operationId: healthCheckAuthorized
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/HealthCheckResponse'
  /check/healthCheckAdmin:
    get:
      tags:
      - health-check-controller
      operationId: healthCheckAdmin
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/HealthCheckResponse'
  /audit/getMakosUserAuditLogsByUsername:
    get:
      tags:
      - audit-controller
      operationId: getMakosUserAuditLogsByUsername
      parameters:
      - name: username
        in: query
        required: true
        schema:
          type: string
      - name: page
        in: query
        required: false
        schema:
          type: integer
          format: int32
          default: 0
      - name: count
        in: query
        required: false
        schema:
          type: integer
          format: int32
          default: 200
      - name: sortBy
        in: query
        required: false
        schema:
          type: string
          default: requestTime
      - name: sortDirection
        in: query
        required: false
        schema:
          type: string
          default: desc
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/GetMakosUserAuditLogsByUsernameResponse'
  /audit/getMakosUserAuditLogsByType:
    get:
      tags:
      - audit-controller
      operationId: getMakosUserAuditLogsByType
      parameters:
      - name: auditType
        in: query
        required: true
        schema:
          type: string
      - name: page
        in: query
        required: false
        schema:
          type: integer
          format: int32
          default: 0
      - name: count
        in: query
        required: false
        schema:
          type: integer
          format: int32
          default: 200
      - name: sortBy
        in: query
        required: false
        schema:
          type: string
          default: requestTime
      - name: sortDirection
        in: query
        required: false
        schema:
          type: string
          default: desc
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/GetMakosUserAuditLogsByTypeResponse'
  /audit/getMakosUserAuditLogList:
    get:
      tags:
      - audit-controller
      operationId: getMakosUserAuditLogList
      parameters:
      - name: page
        in: query
        required: false
        schema:
          type: integer
          format: int32
          default: 0
      - name: count
        in: query
        required: false
        schema:
          type: integer
          format: int32
          default: 200
      - name: sortBy
        in: query
        required: false
        schema:
          type: string
          default: requestTime
      - name: sortDirection
        in: query
        required: false
        schema:
          type: string
          default: desc
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/GetMakosUserAuditLogListResponse'
  /audit/getMakosUserAuditLog/{id}:
    get:
      tags:
      - audit-controller
      operationId: getMakosUserAuditLog
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                $ref: '#/components/schemas/GetMakosUserAuditLogResponse'
components:
  schemas:
    MakosUser:
      required:
      - role
      - status
      - username
      type: object
      properties:
        id:
          type: integer
          format: int64
        username:
          maxLength: 100
          minLength: 4
          type: string
        status:
          type: string
          enum:
          - PASSIVE
          - ACTIVE
          - LOCKED
        role:
          type: string
          description: MAKOS kullanıcı rol türleri
          enum:
          - ROLE_ADMIN
          - ROLE_QUERY_ADMIN
          - ROLE_KURUM_TEMSILCISI
          - ROLE_KURUM_KULLANICI
        kurum:
          type: string
          description: Kullanıcı kurumu
          enum:
          - EMNIYET
          - MIT
          - JANDARMA
          - BTK
          - ADLI
          - EMNIYET_SIBER
          - IDB
        newPassword:
          type: string
    UpdateUserRequest:
      required:
      - id
      - user
      type: object
      properties:
        id:
          type: integer
          format: int64
        user:
          $ref: '#/components/schemas/MakosUser'
    ApiResponse:
      required:
      - responseCode
      type: object
      properties:
        responseCode:
          type: string
          enum:
          - SUCCESS
          - INVALID_REQUEST
          - FAILED
        responseMessage:
          type: string
    UpdateUserResponse:
      required:
      - response
      type: object
      properties:
        response:
          $ref: '#/components/schemas/ApiResponse'
        id:
          type: integer
          format: int64
    DeleteUserRequest:
      required:
      - userId
      type: object
      properties:
        userId:
          type: integer
          format: int64
    DeleteUserResponse:
      required:
      - response
      type: object
      properties:
        response:
          $ref: '#/components/schemas/ApiResponse'
    UsernameRequest:
      required:
      - username
      type: object
      properties:
        username:
          type: string
    DeactivateUserResponse:
      required:
      - response
      type: object
      properties:
        response:
          $ref: '#/components/schemas/ApiResponse'
    AddUserRequest:
      required:
      - id
      - user
      type: object
      properties:
        id:
          type: integer
          format: int64
        user:
          $ref: '#/components/schemas/MakosUser'
    AddUserResponse:
      required:
      - response
      type: object
      properties:
        response:
          $ref: '#/components/schemas/ApiResponse'
        id:
          type: integer
          format: int64
    ActivateUserResponse:
      required:
      - response
      type: object
      properties:
        response:
          $ref: '#/components/schemas/ApiResponse'
    EvrakDetay:
      required:
      - evrakKurumKodu
      - evrakNo
      - evrakTarihi
      - evrakTuru
      - geldigiIlIlceKodu
      type: object
      properties:
        evrakNo:
          maxLength: 50
          minLength: 0
          type: string
        evrakTarihi:
          type: string
          format: date-time
        evrakKurumKodu:
          type: string
        evrakTuru:
          type: string
          description: Evrak türü
          enum:
          - ILETISIMIN_TESPITI
          - ILETISIMIN_DENETLENMESI
          - GENEL_EVRAK
        havaleBirimi:
          maxLength: 10
          type: string
        aciklama:
          type: string
        geldigiIlIlceKodu:
          type: string
        acilmi:
          type: boolean
        evrakKonusu:
          type: string
    Hedef:
      required:
      - hedefNo
      - hedefTip
      type: object
      properties:
        hedefNo:
          type: string
        hedefTip:
          type: string
          description: Hedef tipi enumu
          enum:
          - GSM
          - SABIT
          - UYDU
          - YURT_DISI
          - UMTH_MSISDN
          - UMTH_USERNAME
          - UMTH_IP
          - UMTH_PINCODE
          - EPOSTA
          - IP_TAKIP
          - URL_WEB_ADRESI_TAKIP
          - ADSL_ABONE_TAKIP
          - GPRS
          - IP_ENGELLEME
          - DOMAIN_ENGELLEME
          - IMEI
          - IMSI
          - GPRS_IMSI
          - XDSL_MSISDN
          - XDSL_TEMOSNO
          - XDSL_USERNAME
          - XDSL_IP
          - GPRS_GSM
          - GPRS_IMEI
          - GPRS_YURT_DISI
          - TRUNK
          - GSM_YER_TESPITI
          - GSM_YER_TESPITI_SONLANDIRMA
          - YURTDISI_YER_TESPITI
          - YURTDISI_YER_TESPITI_SONLANDIRMA
      description: Değişiklik yapılacak hedefin hedefNo ve hedefTip bilgileri
    ITHedefDetay:
      required:
      - baslamaTarihi
      - bitisTarihi
      - hedef
      - sorguTipi
      - tespitTuru
      type: object
      properties:
        sorguTipi:
          type: string
          description: Sorgu tipi enumu
          enum:
          - TELEFON_GORUSME
          - IMEI_GORUSME
          - IMEI_KULLANAN_NUMARA
        hedef:
          $ref: '#/components/schemas/Hedef'
        karsiHedef:
          $ref: '#/components/schemas/Hedef'
        baslamaTarihi:
          type: string
          format: date-time
        bitisTarihi:
          type: string
          format: date-time
        tespitTuru:
          type: string
        tespitTuruDetay:
          type: string
        aciklama:
          type: string
    ITKararRequest:
      required:
      - evrakDetay
      - hedefDetayListesi
      - id
      - kararTuru
      - mahkemeKararBilgisi
      type: object
      properties:
        id:
          type: string
          format: uuid
        kararTuru:
          type: string
          description: Mahkeme karar türü
          enum:
          - ILETISIMIN_DENETLENMESI_YENI_KARAR
          - ILETISIMIN_DENETLENMESI_UZATMA_KARARI
          - ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI
          - ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_CANAK_GUNCELLEME
          - ILETISIMIN_TESPITI
          - GENEL_EVRAK
          - ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME
        evrakDetay:
          $ref: '#/components/schemas/EvrakDetay'
        mahkemeKararBilgisi:
          $ref: '#/components/schemas/MahkemeKararBilgisi'
        hedefDetayListesi:
          maxItems: 2147483647
          minItems: 1
          type: array
          items:
            $ref: '#/components/schemas/ITHedefDetay'
      description: IT Mahkeme Karar Detaylari
    MahkemeKararBilgisi:
      required:
      - mahkemeKararDetay
      - mahkemeKararTipi
      type: object
      properties:
        mahkemeKararTipi:
          type: string
          description: Mahkeme karar tipi
          enum:
          - ONLEYICI_HAKIM_KARARI
          - SINYAL_BILGI_DEGERLENDIRME_KARARI
          - ABONE_KUTUK_BILGILERI_KARARI
          - ONLEYICI_YAZILI_EMIR
          - ADLI_HAKIM_KARARI
          - ADLI_HAKIM_HTS_KARARI
          - ADLI_YAZILI_EMIR
          - ADLI_KHK_YAZILI_EMIR
          - ADLI_SAVCILIK_HTS_KARARI
          - HEDEF_AD_SOYAD_DEGISTIRME
          - HEDEF_BILGI_DEGISTIRME
          - MAHKEME_KODU_DEGISTIRME
          - MAHKEME_KARAR_BILGI_DEGISTIRME
          - MAHKEME_AIDIYAT_DEGISTIRME
          - ONLEYICI_SONLANDIRMA
          - ADLI_SONLANDIRMA
          - ADLI_SAVCILIK_SONLANDIRMA
          - ADLI_SAVCILIK_YER_TESPITI_SONLANDIRMA
          - ADLI_KHK_SONLANDIRMA
          - ADLI_ASKERI_HAKIM_KARARI
          - ADLI_ASKERI_SONLANDIRMA
          - ADLI_ASKERI_SAVCILIK_SONLANDIRMA
          - CANAK_NUMARA_DEGISTIRME
          - ADLI_ASKERI_YER_TESPITI_SONLANDIRMA
          - MAHKEME_SUCTIPI_DEGISTIRME
        mahkemeKararDetay:
          $ref: '#/components/schemas/MahkemeKararDetay'
      description: Mahkeme karar bilgileri
    MahkemeKararDetay:
      required:
      - mahkemeIlIlceKodu
      - mahkemeKodu
      type: object
      properties:
        mahkemeKodu:
          type: string
        mahkemeIlIlceKodu:
          type: string
        mahkemeKararNo:
          type: string
        sorusturmaNo:
          type: string
        aciklama:
          type: string
      description: Aidiyat değişikliği yapılacak mahkeme karar bilgileri
    ITKararResponse:
      required:
      - evrakId
      - requestId
      - response
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: '#/components/schemas/MakosApiResponse'
        evrakId:
          type: integer
          format: int64
    MakosApiResponse:
      required:
      - responseCode
      type: object
      properties:
        responseCode:
          type: string
          enum:
          - SUCCESS
          - INVALID_REQUEST
          - FAILED
        responseMessage:
          type: string
    HedefWithAdSoyad:
      required:
      - hedef
      - hedefAd
      - hedefSoyad
      type: object
      properties:
        hedef:
          $ref: '#/components/schemas/Hedef'
        hedefAd:
          type: string
        hedefSoyad:
          type: string
        tcKimlikNo:
          type: string
        acilmi:
          type: boolean
    IDHedefDetay:
      required:
      - baslamaTarihi
      - hedefNoAdSoyad
      - sure
      - sureTip
      type: object
      properties:
        hedefNoAdSoyad:
          $ref: '#/components/schemas/HedefWithAdSoyad'
        baslamaTarihi:
          type: string
          format: date-time
        sureTip:
          type: string
          description: Süre tipi enumu
          enum:
          - GUN
          - AY
          - HICBIRI
        sure:
          type: integer
          format: int32
        ilgiliMahkemeKararDetayi:
          $ref: '#/components/schemas/MahkemeKararDetay'
        uzatmaSayisi:
          type: integer
          description: Uzatma Sayisi. Sadece uzatma kararlarinda gerekli
          format: int32
        hedefAidiyatKodlari:
          type: array
          items:
            type: string
        canakNo:
          type: string
          description: Canak numarası. Sadece yeni kararda girilebilir. Zorunlu olmayan
            alan
    IDYeniKararRequest:
      required:
      - evrakDetay
      - hedefDetayListesi
      - id
      - kararTuru
      - mahkemeKararBilgisi
      type: object
      properties:
        id:
          type: string
          format: uuid
        kararTuru:
          type: string
          description: Mahkeme karar türü
          enum:
          - ILETISIMIN_DENETLENMESI_YENI_KARAR
          - ILETISIMIN_DENETLENMESI_UZATMA_KARARI
          - ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI
          - ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_CANAK_GUNCELLEME
          - ILETISIMIN_TESPITI
          - GENEL_EVRAK
          - ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME
        evrakDetay:
          $ref: '#/components/schemas/EvrakDetay'
        mahkemeKararBilgisi:
          $ref: '#/components/schemas/MahkemeKararBilgisi'
        hedefDetayListesi:
          maxItems: 2147483647
          minItems: 1
          type: array
          items:
            $ref: '#/components/schemas/IDHedefDetay'
        mahkemeAidiyatKodlari:
          type: array
          items:
            type: string
        mahkemeSucTipiKodlari:
          type: array
          items:
            type: string
      description: ID Mahkeme Karar Detaylari
    IDYeniKararResponse:
      required:
      - btkEvrakId
      - requestId
      - response
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: '#/components/schemas/MakosApiResponse'
        btkEvrakId:
          type: integer
          format: int64
    IDUzatmaKarariRequest:
      required:
      - evrakDetay
      - hedefDetayListesi
      - id
      - kararTuru
      - mahkemeKararBilgisi
      type: object
      properties:
        id:
          type: string
          format: uuid
        kararTuru:
          type: string
          description: Mahkeme karar türü
          enum:
          - ILETISIMIN_DENETLENMESI_YENI_KARAR
          - ILETISIMIN_DENETLENMESI_UZATMA_KARARI
          - ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI
          - ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_CANAK_GUNCELLEME
          - ILETISIMIN_TESPITI
          - GENEL_EVRAK
          - ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME
        evrakDetay:
          $ref: '#/components/schemas/EvrakDetay'
        mahkemeKararBilgisi:
          $ref: '#/components/schemas/MahkemeKararBilgisi'
        hedefDetayListesi:
          maxItems: 2147483647
          minItems: 1
          type: array
          items:
            $ref: '#/components/schemas/IDHedefDetay'
        mahkemeAidiyatKodlari:
          type: array
          items:
            type: string
        mahkemeSucTipiKodlari:
          type: array
          items:
            type: string
      description: ID Mahkeme Karar Detaylari
    IDUzatmaKarariResponse:
      required:
      - evrakId
      - requestId
      - response
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: '#/components/schemas/MakosApiResponse'
        evrakId:
          type: integer
          format: int64
    MahkemeKararTalepStateUpdateRequest:
      required:
      - id
      - mahkemeKararTalepId
      - talepGuncellemeTuru
      type: object
      properties:
        id:
          type: string
          format: uuid
        talepGuncellemeTuru:
          type: string
          description: Talep güncelleme türü enumu
          enum:
          - ONAYLA
          - ARSIV
          - SIL
        mahkemeKararTalepId:
          type: integer
          format: int64
    MahkemeKararTalepStateUpdateResponse:
      required:
      - requestId
      - response
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: '#/components/schemas/MakosApiResponse'
        success:
          type: boolean
    IDSucTipiGuncellemeRequest:
      required:
      - evrakDetay
      - id
      - kararTuru
      - mahkemeKararBilgisi
      - sucTipiGuncellemeKararDetayListesi
      type: object
      properties:
        id:
          type: string
          format: uuid
        kararTuru:
          type: string
          description: Mahkeme karar türü
          enum:
          - ILETISIMIN_DENETLENMESI_YENI_KARAR
          - ILETISIMIN_DENETLENMESI_UZATMA_KARARI
          - ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI
          - ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_CANAK_GUNCELLEME
          - ILETISIMIN_TESPITI
          - GENEL_EVRAK
          - ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME
        evrakDetay:
          $ref: '#/components/schemas/EvrakDetay'
        mahkemeKararBilgisi:
          $ref: '#/components/schemas/MahkemeKararBilgisi'
        sucTipiGuncellemeKararDetayListesi:
          maxItems: 2147483647
          minItems: 1
          type: array
          description: Güncelleme yapılacak mahkeme karar bilgisi ve karara ait güncellenecek
            aidiyat bilgileri
          items:
            $ref: '#/components/schemas/SucTipiGuncellemeKararDetay'
      description: Mahkeme Karar Detaylari
    SucTipiGuncellemeDetay:
      required:
      - guncellemeTip
      - sucTipiKodu
      type: object
      properties:
        guncellemeTip:
          type: string
          description: Güncelleme tipi enumu
          enum:
          - EKLE
          - CIKAR
        sucTipiKodu:
          type: string
    SucTipiGuncellemeKararDetay:
      required:
      - mahkemeKararDetay
      - sucTipiGuncellemeDetayListesi
      type: object
      properties:
        mahkemeKararDetay:
          $ref: '#/components/schemas/MahkemeKararDetay'
        sucTipiGuncellemeDetayListesi:
          maxItems: 2147483647
          minItems: 1
          type: array
          items:
            $ref: '#/components/schemas/SucTipiGuncellemeDetay'
      description: Güncelleme yapılacak mahkeme karar bilgisi ve karara ait güncellenecek
        aidiyat bilgileri
    IDSucTipiGuncellemeResponse:
      required:
      - evrakId
      - requestId
      - response
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: '#/components/schemas/MakosApiResponse'
        evrakId:
          type: integer
          format: int64
    IDSonlandirmaKarariRequest:
      required:
      - evrakDetay
      - hedefDetayListesi
      - id
      - kararTuru
      - mahkemeKararBilgisi
      type: object
      properties:
        id:
          type: string
          format: uuid
        kararTuru:
          type: string
          description: Mahkeme karar türü
          enum:
          - ILETISIMIN_DENETLENMESI_YENI_KARAR
          - ILETISIMIN_DENETLENMESI_UZATMA_KARARI
          - ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI
          - ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_CANAK_GUNCELLEME
          - ILETISIMIN_TESPITI
          - GENEL_EVRAK
          - ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME
        evrakDetay:
          $ref: '#/components/schemas/EvrakDetay'
        mahkemeKararBilgisi:
          $ref: '#/components/schemas/MahkemeKararBilgisi'
        hedefDetayListesi:
          maxItems: 2147483647
          minItems: 1
          type: array
          items:
            $ref: '#/components/schemas/IDHedefDetay'
        mahkemeAidiyatKodlari:
          type: array
          items:
            type: string
        mahkemeSucTipiKodlari:
          type: array
          items:
            type: string
      description: ID Mahkeme Karar Detaylari
    IDSonlandirmaKarariResponse:
      required:
      - evrakId
      - requestId
      - response
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: '#/components/schemas/MakosApiResponse'
        evrakId:
          type: integer
          format: int64
    IDMahkemeKararTalepSorgulamaRequest:
      required:
      - id
      - sorguParam
      type: object
      properties:
        id:
          type: string
          format: uuid
        sorguParam:
          $ref: '#/components/schemas/MahkemeKararTalepSorguParam'
    MahkemeKararTalepSorguParam:
      type: object
      properties:
        sorusturmaNo:
          type: string
        mahkemeKararNo:
          type: string
        mahkemeKodu:
          type: string
        durum:
          type: string
        aciklama:
          type: string
        kayitTarihi:
          type: string
          format: date-time
        kaydedenKullaniciId:
          type: integer
          format: int64
        evrakSiraNo:
          type: string
    IDMahkemeKararTalepSorgulamaResponse:
      required:
      - requestId
      - response
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: '#/components/schemas/MakosApiResponse'
        mahkemeKararTalepSorguViewListesi:
          type: array
          items:
            $ref: '#/components/schemas/MahkemeKararTalepSorguView'
    MahkemeKararTalepSorguView:
      type: object
      properties:
        mahkemeKararTalepId:
          type: integer
          description: Mahkeme Karar Talep Id
          format: int64
        evrakId:
          type: integer
          description: Evrak Id
          format: int64
        evrakSiraNo:
          type: string
          description: Evrak Sıra No
        evrakNo:
          type: string
          description: Evrak No
        kurumEvrakNo:
          type: string
          description: Kurum Evrak No
        kurumEvrakTarihi:
          type: string
          description: Kurum Evrak Tarihi
          format: date-time
        kararKayitTarihi:
          type: string
          description: Karar Kayıt Tarihi
          format: date-time
        mahkemeIlIlceKodu:
          type: string
          description: Mahkeme İl/İlçe Kodu
        mahkemeIlIlceAdi:
          type: string
          description: Mahkeme İl/İlçe Adı
        kaydedenKullaniciId:
          type: integer
          description: Kaydeden Kullanıcı Id
          format: int64
        kaydedenKullaniciAdi:
          type: string
          description: Kaydeden Kullanıcı Adı
        adi:
          type: string
          description: Adı
        soyadi:
          type: string
          description: Soyadı
        kaydedenAdiSoyadi:
          type: string
          description: Kaydeden Adı/Soyadı
        sorusturmaNo:
          type: string
          description: Soruşturma No
        mahkemeKararNo:
          type: string
          description: Mahkeme Karar No
        aciklama:
          type: string
          description: Açıklama
        durumu:
          type: string
          description: Durumu
        mahkemeKodu:
          type: string
          description: Mahkeme Kodu
        mahkemeAdi:
          type: string
          description: Mahkeme Adı
        kurumKodu:
          type: string
          description: Kurum Kodu
        kurumAdi:
          type: string
          description: Kurum Adı
        evrakKonusu:
          type: string
          description: Evrak Konusu
      description: Mahkeme Karar Talep Listeleme  Bilgisi
    MahkemeKararTalepBilgisiRequest:
      required:
      - id
      - mahkemeKararId
      type: object
      properties:
        id:
          type: string
          format: uuid
        mahkemeKararId:
          type: integer
          format: int64
    DetayMahkemeKararInfo:
      type: object
      properties:
        mahkemeKararId:
          type: integer
          format: int64
        mahkemeKodu:
          type: string
        mahkemeAdi:
          type: string
        mahkemeIlIlceKodu:
          type: string
        ilIlceAdi:
          type: string
        mahkemeKararNo:
          type: string
        sorusturmaNo:
          type: string
    EvrakDetayInfo:
      type: object
      properties:
        evrakNo:
          type: string
        evrakTarihi:
          type: string
          format: date-time
        evrakKurumKodu:
          type: string
        evrakKurumAdi:
          type: string
        evrakTuru:
          type: string
          description: Evrak türü
          enum:
          - ILETISIMIN_TESPITI
          - ILETISIMIN_DENETLENMESI
          - GENEL_EVRAK
        geldigiIlIlceKodu:
          type: string
        geldigiIlIlceAdi:
          type: string
        acilmi:
          type: boolean
        evrakKonusu:
          type: string
        aciklama:
          type: string
        durumu:
          type: string
    HedefGuncellemeAlanInfo:
      required:
      - hedefGuncellemeAlanTuru
      type: object
      properties:
        hedefGuncellemeAlanTuru:
          type: string
          enum:
          - AD
          - SOYAD
          - TCKIMlIKNO
          - CANAK_NO
        yeniDegeri:
          type: string
    HedefGuncellemeInfo:
      type: object
      properties:
        detayMahkemeKararInfo:
          $ref: '#/components/schemas/DetayMahkemeKararInfo'
        hedeflerDetayList:
          type: array
          items:
            $ref: '#/components/schemas/HedeflerDetayDTO'
    HedeflerDetayDTO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        hedefNo:
          type: string
        hedefTipi:
          type: string
          description: Hedef tipi enumu
          enum:
          - GSM
          - SABIT
          - UYDU
          - YURT_DISI
          - UMTH_MSISDN
          - UMTH_USERNAME
          - UMTH_IP
          - UMTH_PINCODE
          - EPOSTA
          - IP_TAKIP
          - URL_WEB_ADRESI_TAKIP
          - ADSL_ABONE_TAKIP
          - GPRS
          - IP_ENGELLEME
          - DOMAIN_ENGELLEME
          - IMEI
          - IMSI
          - GPRS_IMSI
          - XDSL_MSISDN
          - XDSL_TEMOSNO
          - XDSL_USERNAME
          - XDSL_IP
          - GPRS_GSM
          - GPRS_IMEI
          - GPRS_YURT_DISI
          - TRUNK
          - GSM_YER_TESPITI
          - GSM_YER_TESPITI_SONLANDIRMA
          - YURTDISI_YER_TESPITI
          - YURTDISI_YER_TESPITI_SONLANDIRMA
        hedefAdi:
          type: string
        hedefSoyadi:
          type: string
        mahkemeKararId:
          type: integer
          format: int64
        detayMahkemeKararId:
          type: integer
          format: int64
        iliskiliHedefId:
          type: integer
          format: int64
        kayitTarihi:
          type: string
          format: date-time
        durumu:
          type: string
        canakNo:
          type: string
        tcKimlikNo:
          type: string
        updateColumnNames:
          type: string
        adiGuncellemeAlani:
          $ref: '#/components/schemas/HedefGuncellemeAlanInfo'
        soyadiGuncellemeAlani:
          $ref: '#/components/schemas/HedefGuncellemeAlanInfo'
        tcknGuncellemeAlani:
          $ref: '#/components/schemas/HedefGuncellemeAlanInfo'
        canakNoGuncellemeAlani:
          $ref: '#/components/schemas/HedefGuncellemeAlanInfo'
    IDAidiyatGuncellemeKararDetay:
      type: object
      properties:
        aidiyatGuncellemeListesi:
          type: array
          items:
            $ref: '#/components/schemas/MahkemeKararAidiyatGuncellemeInfo'
    IDHedefAidiyatInfo:
      type: object
      properties:
        id:
          type: integer
          format: int64
        hedefId:
          type: integer
          format: int64
        aidiyatKod:
          type: string
        tarih:
          type: string
          format: date-time
        durumu:
          type: string
    IDHedefGuncellemeKararDetay:
      type: object
      properties:
        guncellemeListesi:
          type: array
          items:
            $ref: '#/components/schemas/HedefGuncellemeInfo'
    IDHedefInfo:
      type: object
      properties:
        id:
          type: integer
          format: int64
        mahkemeKararId:
          type: integer
          format: int64
        hedefNo:
          type: string
        hedefTip:
          type: string
          description: Hedef tipi enumu
          enum:
          - GSM
          - SABIT
          - UYDU
          - YURT_DISI
          - UMTH_MSISDN
          - UMTH_USERNAME
          - UMTH_IP
          - UMTH_PINCODE
          - EPOSTA
          - IP_TAKIP
          - URL_WEB_ADRESI_TAKIP
          - ADSL_ABONE_TAKIP
          - GPRS
          - IP_ENGELLEME
          - DOMAIN_ENGELLEME
          - IMEI
          - IMSI
          - GPRS_IMSI
          - XDSL_MSISDN
          - XDSL_TEMOSNO
          - XDSL_USERNAME
          - XDSL_IP
          - GPRS_GSM
          - GPRS_IMEI
          - GPRS_YURT_DISI
          - TRUNK
          - GSM_YER_TESPITI
          - GSM_YER_TESPITI_SONLANDIRMA
          - YURTDISI_YER_TESPITI
          - YURTDISI_YER_TESPITI_SONLANDIRMA
        hedefAdi:
          type: string
        hedefSoyadi:
          type: string
        tcKimlikNo:
          type: string
        durumu:
          type: string
        acilmi:
          type: string
        aciklama:
          type: string
        kayitTarihi:
          type: string
          format: date-time
        tanimlamaTarihi:
          type: string
          format: date-time
    IDKararSucTipiGuncellemeInfo:
      type: object
      properties:
        detayMahkemeKararInfo:
          $ref: '#/components/schemas/DetayMahkemeKararInfo'
        sucTipiGuncellemeListesi:
          type: array
          items:
            $ref: '#/components/schemas/MahkemeSucTipiGuncellemeDTO'
    IDKarariAidiyatInfo:
      type: object
      properties:
        id:
          type: integer
          format: int64
        mahkemeKararId:
          type: integer
          format: int64
        aidiyatKod:
          type: string
        durumu:
          type: string
    IDKarariSucTipiInfo:
      type: object
      properties:
        id:
          type: integer
          format: int64
        mahkemeKararId:
          type: integer
          format: int64
        sucTipiKodu:
          type: string
        sucTipi:
          $ref: '#/components/schemas/SucTipiInfo'
        durumu:
          type: string
    IDMahkemeBilgiGuncellemeKararDetay:
      type: object
      properties:
        guncellemeListesi:
          type: array
          items:
            $ref: '#/components/schemas/MahkemeKararBilgisiGuncellemeInfo'
    IDMahkemeKarariInfo:
      type: object
      properties:
        mahkemeKararId:
          type: integer
          format: int64
        kararTuru:
          type: string
          description: Mahkeme karar türü
          enum:
          - ILETISIMIN_DENETLENMESI_YENI_KARAR
          - ILETISIMIN_DENETLENMESI_UZATMA_KARARI
          - ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI
          - ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_CANAK_GUNCELLEME
          - ILETISIMIN_TESPITI
          - GENEL_EVRAK
          - ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME
        evrakDetay:
          $ref: '#/components/schemas/EvrakDetayInfo'
        mahkemeKararBilgisi:
          $ref: '#/components/schemas/MahkemeKararBilgisiInfo'
        dosyaAdi:
          type: string
        durumu:
          type: string
        idhedefGuncellemeKararDetay:
          $ref: '#/components/schemas/IDHedefGuncellemeKararDetay'
        idaidiyatGuncellemeKararDetay:
          $ref: '#/components/schemas/IDAidiyatGuncellemeKararDetay'
        idsonlandirmaKararDetay:
          $ref: '#/components/schemas/IDSonlandirmaKararDetay'
        iduzatmaKararDetay:
          $ref: '#/components/schemas/IDUzatmaKararDetay'
        idsucTipiGuncellemeKararDetay:
          $ref: '#/components/schemas/IDSucTipiGuncellemeKararDetay'
        idyeniKararDetay:
          $ref: '#/components/schemas/IDYeniKararDetay'
        idmahkemeBilgiGuncellemeKararDetay:
          $ref: '#/components/schemas/IDMahkemeBilgiGuncellemeKararDetay'
    IDSonlandirmaKararDetay:
      type: object
      properties:
        hedefListesi:
          type: array
          items:
            $ref: '#/components/schemas/SonlandirmaKarariHedefInfo'
        aidiyatListesi:
          type: array
          items:
            $ref: '#/components/schemas/IDKarariAidiyatInfo'
        sucTipleri:
          type: array
          items:
            $ref: '#/components/schemas/IDKarariSucTipiInfo'
    IDSucTipiGuncellemeKararDetay:
      type: object
      properties:
        sucTipiGuncellemeListesi:
          type: array
          items:
            $ref: '#/components/schemas/IDKararSucTipiGuncellemeInfo'
    IDUzatmaKararDetay:
      type: object
      properties:
        hedefListesi:
          type: array
          items:
            $ref: '#/components/schemas/UzatmaKarariHedefInfo'
        aidiyatListesi:
          type: array
          items:
            $ref: '#/components/schemas/IDKarariAidiyatInfo'
        sucTipleri:
          type: array
          items:
            $ref: '#/components/schemas/IDKarariSucTipiInfo'
    IDYeniKararDetay:
      type: object
      properties:
        hedefListesi:
          type: array
          items:
            $ref: '#/components/schemas/YeniIDKarariHedefInfo'
        mahkemeAidiyatlari:
          type: array
          items:
            $ref: '#/components/schemas/IDKarariAidiyatInfo'
        sucTipleri:
          type: array
          items:
            $ref: '#/components/schemas/IDKarariSucTipiInfo'
    MahkemeAidiyatDetayDTO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        iliskiliMahkemeKararId:
          type: integer
          format: int64
        mahkemeKararId:
          type: integer
          format: int64
        mahkemeAidiyatKoduEkle:
          type: string
        mahkemeAidiyatKoduCikar:
          type: string
        tarih:
          type: string
          format: date-time
        durum:
          type: string
        detayMahkemeKararId:
          type: integer
          format: int64
    MahkemeKararAidiyatGuncellemeInfo:
      type: object
      properties:
        detayMahkemeKararInfo:
          $ref: '#/components/schemas/DetayMahkemeKararInfo'
        aidiyatGuncellemeListesi:
          type: array
          items:
            $ref: '#/components/schemas/MahkemeAidiyatDetayDTO'
    MahkemeKararBilgisiGuncellemeInfo:
      type: object
      properties:
        detayMahkemeKararInfo:
          $ref: '#/components/schemas/DetayMahkemeKararInfo'
        mahkemeKararGuncellemeDTO:
          $ref: '#/components/schemas/MahkemeKararGuncellemeDTO'
    MahkemeKararBilgisiInfo:
      type: object
      properties:
        mahkemeKodu:
          type: string
        mahkemeIlIlceKodu:
          type: string
        mahkemeKararNo:
          type: string
        sorusturmaNo:
          type: string
        kararBaslamaTarihi:
          type: string
          format: date-time
        kararBitisTarihi:
          type: string
          format: date-time
        aciklama:
          type: string
        durumu:
          type: string
    MahkemeKararGuncellemeAlanInfo:
      required:
      - mahkemeKararGuncellemeAlanTuru
      type: object
      properties:
        mahkemeKararGuncellemeAlanTuru:
          type: string
          enum:
          - MAHKEME_KODU
          - SORUSTURMA_NO
          - MAHKEME_KARAR_NO
        yeniDegeri:
          type: string
    MahkemeKararGuncellemeDTO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        detayMahkemeKararId:
          type: integer
          format: int64
        durumu:
          type: string
        guncellemeAlanListesi:
          type: array
          items:
            $ref: '#/components/schemas/MahkemeKararGuncellemeAlanInfo'
    MahkemeKararTalepQueryResponse:
      required:
      - requestId
      - response
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: '#/components/schemas/MakosApiResponse'
        idmahkemeKarariInfo:
          $ref: '#/components/schemas/IDMahkemeKarariInfo'
    MahkemeSucTipiGuncellemeDTO:
      type: object
      properties:
        id:
          type: integer
          format: int64
        iliskiliMahkemeKararId:
          type: integer
          format: int64
        detayMahkemeKararId:
          type: integer
          format: int64
        mahkemeKararId:
          type: integer
          format: int64
        mahkemeSucTipiKoduEkle:
          type: string
        mahkemeSucTipiKoduCikar:
          type: string
        tarih:
          type: string
          format: date-time
        durum:
          type: string
    SonlandirmaKarariHedefInfo:
      type: object
      properties:
        hedefBilgisi:
          $ref: '#/components/schemas/IDHedefInfo'
        iliskiliMahkemeKararInfo:
          $ref: '#/components/schemas/DetayMahkemeKararInfo'
        kapatmaTarihi:
          type: string
          format: date-time
        kapatmaMahkemeKararId:
          type: integer
          format: int64
        baslamaTarihi:
          type: string
          format: date-time
        hedefAidiyatListesi:
          type: array
          items:
            $ref: '#/components/schemas/IDHedefAidiyatInfo'
    SucTipiInfo:
      type: object
      properties:
        sucTipiKodu:
          type: string
        sucTipAdi:
          type: string
    UzatmaKarariHedefInfo:
      type: object
      properties:
        hedefBilgisi:
          $ref: '#/components/schemas/IDHedefInfo'
        baslamaTarihi:
          type: string
          format: date-time
        sureTip:
          type: string
          description: Süre tipi enumu
          enum:
          - GUN
          - AY
          - HICBIRI
        sure:
          type: integer
          format: int32
        uzatmaSayisi:
          type: integer
          format: int32
        iliskiliMahkemeKararInfo:
          $ref: '#/components/schemas/DetayMahkemeKararInfo'
        hedefAidiyatListesi:
          type: array
          items:
            $ref: '#/components/schemas/IDHedefAidiyatInfo'
        sucTipleri:
          type: array
          items:
            $ref: '#/components/schemas/IDKarariSucTipiInfo'
        canakNo:
          type: string
    YeniIDKarariHedefInfo:
      type: object
      properties:
        hedefBilgileri:
          $ref: '#/components/schemas/IDHedefInfo'
        baslamaTarihi:
          type: string
          format: date-time
        sureTip:
          type: string
          description: Süre tipi enumu
          enum:
          - GUN
          - AY
          - HICBIRI
        sure:
          type: integer
          format: int32
        hedefAidiyatListesi:
          type: array
          items:
            $ref: '#/components/schemas/IDHedefAidiyatInfo'
        canakNo:
          type: string
    IDMahkemeKararGuncellemeRequest:
      required:
      - evrakDetay
      - id
      - kararTuru
      - mahkemeKararBilgisi
      - mahkemeKararGuncellemeDetayListesi
      type: object
      properties:
        id:
          type: string
          format: uuid
        kararTuru:
          type: string
          description: Mahkeme karar türü
          enum:
          - ILETISIMIN_DENETLENMESI_YENI_KARAR
          - ILETISIMIN_DENETLENMESI_UZATMA_KARARI
          - ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI
          - ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_CANAK_GUNCELLEME
          - ILETISIMIN_TESPITI
          - GENEL_EVRAK
          - ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME
        evrakDetay:
          $ref: '#/components/schemas/EvrakDetay'
        mahkemeKararBilgisi:
          $ref: '#/components/schemas/MahkemeKararBilgisi'
        mahkemeKararGuncellemeDetayListesi:
          maxItems: 2147483647
          minItems: 1
          type: array
          description: Güncelleme yapılacak mahkeme karar bilgisi ve karara ait güncellenecek
            yeni kod/il bilgileri
          items:
            $ref: '#/components/schemas/MahkemeKararGuncellemeDetay'
      description: Mahkeme Karar Detaylari
    MahkemeKararGuncellemeBilgi:
      required:
      - mahkemeKararGuncellemeAlanTuru
      type: object
      properties:
        mahkemeKararGuncellemeAlanTuru:
          type: string
          enum:
          - MAHKEME_KODU
          - SORUSTURMA_NO
          - MAHKEME_KARAR_NO
        yeniDegeri:
          type: string
    MahkemeKararGuncellemeDetay:
      required:
      - mahkemeKararDetay
      - mahkemeKararGuncellemeBilgiListesi
      type: object
      properties:
        mahkemeKararDetay:
          $ref: '#/components/schemas/MahkemeKararDetay'
        mahkemeKararGuncellemeBilgiListesi:
          maxItems: 2147483647
          minItems: 1
          type: array
          items:
            $ref: '#/components/schemas/MahkemeKararGuncellemeBilgi'
      description: Güncelleme yapılacak mahkeme karar bilgisi ve karara ait güncellenecek
        yeni kod/il bilgileri
    IDMahkemeKararGuncellemeResponse:
      required:
      - evrakId
      - requestId
      - response
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: '#/components/schemas/MakosApiResponse'
        evrakId:
          type: integer
          format: int64
    IDMahkemeKararTalepIslenecekRequest:
      required:
      - id
      type: object
      properties:
        id:
          type: string
          format: uuid
    IDMahkemeKararTalepIslenecekResponse:
      required:
      - requestId
      - response
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: '#/components/schemas/MakosApiResponse'
        islenecekKararlar:
          type: array
          items:
            $ref: '#/components/schemas/MahkemeKararTalepIslenecekView'
    MahkemeKararTalepIslenecekView:
      type: object
      properties:
        mahkemeKararTalepId:
          type: integer
          description: Mahkeme Karar Talep Id
          format: int64
        evrakId:
          type: integer
          description: Evrak Id
          format: int64
        evrakSiraNo:
          type: string
          description: Evrak Sıra No
        kurumEvrakNo:
          type: string
          description: Kurum Evrak No
        kurumEvrakTarihi:
          type: string
          description: Kurum Evrak Tarihi
          format: date-time
        kararKayitTarihi:
          type: string
          description: Karar Kayıt Tarihi
          format: date-time
        mahkemeIlIlceKodu:
          type: string
          description: Mahkeme İl/İlçe Kodu
        mahkemeIlIlceAdi:
          type: string
          description: Mahkeme İl/İlçe Adı
        kaydedenKullaniciId:
          type: integer
          description: Kaydeden Kullanıcı Id
          format: int64
        kaydedenKullaniciAdi:
          type: string
          description: Kaydeden KullanıcıAdi
        kaydedenAdiSoyadi:
          type: string
          description: Kaydeden Adı/Soyadı
        sorusturmaNo:
          type: string
          description: Soruşturma No
        mahkemeKararNo:
          type: string
          description: Mahkeme Karar No
        aciklama:
          type: string
          description: Açıklama
        durumu:
          type: string
          description: Durumu
      description: Mahkeme Karar Talep Listeleme  Bilgisi
    HedefGuncellemeBilgi:
      required:
      - hedefGuncellemeAlan
      type: object
      properties:
        hedefGuncellemeAlan:
          type: string
          enum:
          - AD
          - SOYAD
          - TCKIMlIKNO
          - CANAK_NO
        yeniDegeri:
          type: string
    HedefGuncellemeDetay:
      required:
      - hedef
      - hedefGuncellemeBilgiListesi
      type: object
      properties:
        hedef:
          $ref: '#/components/schemas/Hedef'
        hedefGuncellemeBilgiListesi:
          maxItems: 2147483647
          minItems: 1
          type: array
          items:
            $ref: '#/components/schemas/HedefGuncellemeBilgi'
    HedefGuncellemeKararDetay:
      required:
      - hedefGuncellemeDetayListesi
      - mahkemeKararDetay
      type: object
      properties:
        mahkemeKararDetay:
          $ref: '#/components/schemas/MahkemeKararDetay'
        hedefGuncellemeDetayListesi:
          maxItems: 2147483647
          minItems: 1
          type: array
          items:
            $ref: '#/components/schemas/HedefGuncellemeDetay'
      description: "Güncelleme yapılacak hedefler için mahkeme karar bilgisi ve karara\
        \ ait güncellenecek ad, soyad bilgileri"
    IDHedefGuncellemeRequest:
      required:
      - evrakDetay
      - hedefGuncellemeKararDetayListesi
      - id
      - kararTuru
      - mahkemeKararBilgisi
      type: object
      properties:
        id:
          type: string
          format: uuid
        kararTuru:
          type: string
          description: Mahkeme karar türü
          enum:
          - ILETISIMIN_DENETLENMESI_YENI_KARAR
          - ILETISIMIN_DENETLENMESI_UZATMA_KARARI
          - ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI
          - ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_CANAK_GUNCELLEME
          - ILETISIMIN_TESPITI
          - GENEL_EVRAK
          - ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME
        evrakDetay:
          $ref: '#/components/schemas/EvrakDetay'
        mahkemeKararBilgisi:
          $ref: '#/components/schemas/MahkemeKararBilgisi'
        hedefGuncellemeKararDetayListesi:
          maxItems: 2147483647
          minItems: 1
          type: array
          description: "Güncelleme yapılacak hedefler için mahkeme karar bilgisi ve\
            \ karara ait güncellenecek ad, soyad bilgileri"
          items:
            $ref: '#/components/schemas/HedefGuncellemeKararDetay'
      description: Mahkeme Karar Detaylari
    IDHedefGuncellemeResponse:
      required:
      - evrakId
      - requestId
      - response
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: '#/components/schemas/MakosApiResponse'
        evrakId:
          type: integer
          format: int64
    AidiyatGuncellemeDetay:
      required:
      - aidiyatKodu
      - guncellemeTip
      type: object
      properties:
        guncellemeTip:
          type: string
          description: Güncelleme tipi enumu
          enum:
          - EKLE
          - CIKAR
        aidiyatKodu:
          type: string
    AidiyatGuncellemeKararDetay:
      required:
      - aidiyatGuncellemeDetayListesi
      - mahkemeKararDetay
      type: object
      properties:
        mahkemeKararDetay:
          $ref: '#/components/schemas/MahkemeKararDetay'
        aidiyatGuncellemeDetayListesi:
          maxItems: 2147483647
          minItems: 1
          type: array
          items:
            $ref: '#/components/schemas/AidiyatGuncellemeDetay'
      description: Güncelleme yapılacak mahkeme karar bilgisi ve karara ait güncellenecek
        aidiyat bilgileri
    IDAidiyatBilgisiGuncellemeRequest:
      required:
      - aidiyatGuncellemeKararDetayListesi
      - evrakDetay
      - id
      - kararTuru
      - mahkemeKararBilgisi
      type: object
      properties:
        id:
          type: string
          format: uuid
        kararTuru:
          type: string
          description: Mahkeme karar türü
          enum:
          - ILETISIMIN_DENETLENMESI_YENI_KARAR
          - ILETISIMIN_DENETLENMESI_UZATMA_KARARI
          - ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI
          - ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_CANAK_GUNCELLEME
          - ILETISIMIN_TESPITI
          - GENEL_EVRAK
          - ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME
        evrakDetay:
          $ref: '#/components/schemas/EvrakDetay'
        mahkemeKararBilgisi:
          $ref: '#/components/schemas/MahkemeKararBilgisi'
        aidiyatGuncellemeKararDetayListesi:
          maxItems: 2147483647
          minItems: 1
          type: array
          description: Güncelleme yapılacak mahkeme karar bilgisi ve karara ait güncellenecek
            aidiyat bilgileri
          items:
            $ref: '#/components/schemas/AidiyatGuncellemeKararDetay'
      description: Mahkeme Karar Detaylari
    IDAidiyatBilgisiGuncellemeResponse:
      required:
      - evrakId
      - requestId
      - response
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: '#/components/schemas/MakosApiResponse'
        evrakId:
          type: integer
          format: int64
    IDMahkemeKararSorgulamaRequest:
      required:
      - id
      - sorguParam
      type: object
      properties:
        id:
          type: string
          format: uuid
        sorguParam:
          $ref: '#/components/schemas/MahkemeKararSorguParam'
    MahkemeKararSorguParam:
      type: object
      properties:
        evrakSiraNo:
          type: string
        evrakNo:
          type: string
        sorusturmaNo:
          type: string
        mahkemeKararNo:
          type: string
        mahkemeKodu:
          type: string
        evrakIlKodu:
          type: string
        mahkemeIlKodu:
          type: string
        durum:
          type: string
        aciklama:
          type: string
        kayitTarihi:
          type: string
          format: date-time
    IDMahkemeKararSorgulamaResponse:
      required:
      - requestId
      - response
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: '#/components/schemas/MakosApiResponse'
        kararlar:
          type: array
          items:
            $ref: '#/components/schemas/MahkemeKararSorguView'
    MahkemeKararSorguView:
      type: object
      properties:
        mahkemeKararTalepId:
          type: integer
          description: Mahkeme Karar Talep Id
          format: int64
        evrakId:
          type: integer
          description: Evrak Id
          format: int64
        evrakSiraNo:
          type: string
          description: Evrak Sıra No
        kurumEvrakNo:
          type: string
          description: Kurum Evrak No
        kurumEvrakTarihi:
          type: string
          description: Kurum Evrak Tarihi
          format: date-time
        kararKayitTarihi:
          type: string
          description: Kurum Evrak No
          format: date-time
        mahkemeIlIlceKodu:
          type: string
          description: Mahkeme İl/İlçe Kodu
        mahkemeIlIlceAdi:
          type: string
          description: Mahkeme İl/İlçe Adı
        kaydedenKullaniciId:
          type: integer
          description: Kaydeden Kullanıcı Id
          format: int64
        kaydedenKullaniciAdi:
          type: string
          description: Kaydeden KullanıcıAdi
        kaydedenAdiSoyadi:
          type: string
          description: Kaydeden Adı/Soyadı
        sorusturmaNo:
          type: string
          description: Soruşturma No
        mahkemeKararNo:
          type: string
          description: Mahkeme Karar No
        aciklama:
          type: string
          description: Açıklama
        durumu:
          type: string
          description: Durumu
      description: Mahkeme Karar Talep Listeleme  Bilgisi
    MahkemeKararTalepByKararIdRequest:
      required:
      - id
      - mahkemeKararTalepIslemId
      type: object
      properties:
        id:
          type: string
          format: uuid
        mahkemeKararTalepIslemId:
          type: integer
          format: int64
    IDMahkemeKararIslenecekRequest:
      required:
      - id
      type: object
      properties:
        id:
          type: string
          format: uuid
    IDEvrakDurumSorgulamaRequest:
      required:
      - id
      type: object
      properties:
        id:
          type: string
          format: uuid
        kurumEvrakNo:
          type: string
        evrakSiraNo:
          type: string
        evrakId:
          type: integer
          format: int64
    IDEvrakDurumSorgulamaResponse:
      required:
      - requestId
      - response
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: '#/components/schemas/MakosApiResponse'
        evrakListesi:
          type: array
          items:
            $ref: '#/components/schemas/IdEvrakDurumSorguView'
    IdEvrakDurumSorguView:
      type: object
      properties:
        evrakId:
          type: integer
          description: Evrak Id
          format: int64
        evrakSiraNo:
          type: string
          description: Evrak Sıra No
        kurumEvrakNo:
          type: string
          description: Kurum Evrak No
        girisTarihi:
          type: string
          description: Evrak Giriş Tarihi
          format: date-time
        onayTarihi:
          type: string
          description: Evrak OnayTarihi
          format: date-time
        durumu:
          type: string
          description: Durumu
      description: 'ID '
    IDIslenecekEvrakListesiRequest:
      required:
      - id
      type: object
      properties:
        id:
          type: string
          format: uuid
        kararTuru:
          type: string
          description: ID karar türü
          enum:
          - ILETISIMIN_DENETLENMESI_YENI_KARAR
          - ILETISIMIN_DENETLENMESI_UZATMA_KARARI
          - ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI
          - ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME
          - ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME
        atananKullaniciId:
          type: integer
          format: int64
        gorevTipi:
          type: string
        tanimlama:
          type: boolean
        onaylama:
          type: boolean
        nobetci:
          type: boolean
    IDIslenecekEvrakDTO:
      type: object
      properties:
        evrakId:
          type: integer
          description: Evrak Id
          format: int64
        evrakSiraNo:
          type: string
          description: Evrak Sıra No
        evrakNo:
          type: string
          description: Kurum Evrak No
        evrakGirişTarihi:
          type: string
          description: Evrak Giriş Tarihi
          format: date-time
        evrakTarihi:
          type: string
          description: Kurum Evrak Tarihi
          format: date-time
        evrakIlIlceKodu:
          type: string
          description: Evrak İl/İlçe Kodu
        evraklIlceAdi:
          type: string
          description: Evrak İl/İlçe Adı
        evrakKurumKodu:
          type: string
          description: Evrak Kurum Kodu
        evrakKurumAdı:
          type: string
          description: Evrak Kurum Adı
        acil:
          type: boolean
          description: Kurum Evrak No
        aciklama:
          type: string
          description: Açıklama
        mahkemeKararTalepId:
          type: integer
          description: Mahkeme Karar Talep Id
          format: int64
        atayanKullaniciId:
          type: integer
          description: Atayan Kullanıcı Id
          format: int64
        atayanAdiSoyadi:
          type: string
          description: Atayan KullanıcıAdi
        atananKullaniciId:
          type: integer
          description: Atanan Kullanıcı Id
          format: int64
        atananAdiSoyadi:
          type: string
          description: Atanan KullanıcıAdi
        sorusturmaNo:
          type: string
          description: Soruşturma No
        mahkemeKararNo:
          type: string
          description: Mahkeme Karar No
        mahkemeKodu:
          type: string
          description: Mahkeme Kodu
        mahkemeAdi:
          type: string
          description: Mahkeme Adı
      description: İşlenecek Denetleme Evrak Bilgisi
    IDIslenecekEvrakListesiResponse:
      required:
      - requestId
      - response
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: '#/components/schemas/MakosApiResponse'
        islenecekEvrakListesi:
          type: array
          items:
            $ref: '#/components/schemas/IDIslenecekEvrakDTO'
    IDMahkemeKararAtamaRequest:
      required:
      - atananKullaniciId
      - evrakId
      - id
      type: object
      properties:
        evrakId:
          type: integer
          format: int64
        atananKullaniciId:
          type: integer
          format: int64
        id:
          type: string
          format: uuid
    IDMahkemeKararOnaylamaResponse:
      required:
      - evrakId
      - requestId
      - response
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: '#/components/schemas/MakosApiResponse'
        evrakId:
          type: integer
          format: int64
        aciklama:
          type: string
    IDEvrakAtamaRequest:
      required:
      - atananKullaniciId
      - atayanKullaniciId
      - evrakId
      - id
      - sebebi
      type: object
      properties:
        id:
          type: string
          format: uuid
        evrakId:
          type: integer
          format: int64
        atananKullaniciId:
          type: integer
          format: int64
        atayanKullaniciId:
          type: integer
          format: int64
        aciklama:
          type: string
        sebebi:
          type: string
    IDEvrakAtamaResponse:
      required:
      - evrakId
      - requestId
      - response
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: '#/components/schemas/MakosApiResponse'
        evrakId:
          type: integer
          format: int64
        aciklama:
          type: string
    IDEvrakAtamaHistoryRequest:
      required:
      - evrakId
      - id
      type: object
      properties:
        id:
          type: string
          format: uuid
        evrakId:
          type: integer
          format: int64
    IDEvrakAtamaDTO:
      type: object
      properties:
        evrakId:
          type: integer
          format: int64
        evrakSiraNo:
          type: string
        evrakNo:
          type: string
        sorusturmaNo:
          type: string
        mahkemeKararNo:
          type: string
        atayanKullaniciId:
          type: integer
          format: int64
        atayanAdiSoyadi:
          type: string
        atananKullaniciId:
          type: integer
          format: int64
        atananAdiSoyadi:
          type: string
        aciklama:
          type: string
        sebebi:
          type: string
        durum:
          type: string
      description: Evrak Atama History Bilgisi
    IDEvrakAtamaHistoryResponse:
      required:
      - requestId
      - response
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: '#/components/schemas/MakosApiResponse'
        atamaListesi:
          type: array
          items:
            $ref: '#/components/schemas/IDEvrakAtamaDTO'
    IDMahkemeKararAtamaResponse:
      required:
      - evrakId
      - requestId
      - response
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: '#/components/schemas/MakosApiResponse'
        evrakId:
          type: integer
          format: int64
        aciklama:
          type: string
    RegisterRequest:
      required:
      - kurum
      - password
      - role
      - userName
      type: object
      properties:
        userName:
          type: string
        password:
          type: string
        role:
          type: string
          description: MAKOS kullanıcı rol türleri
          enum:
          - ROLE_ADMIN
          - ROLE_QUERY_ADMIN
          - ROLE_KURUM_TEMSILCISI
          - ROLE_KURUM_KULLANICI
        kurum:
          type: string
          description: Kullanıcı kurumu
          enum:
          - EMNIYET
          - MIT
          - JANDARMA
          - BTK
          - ADLI
          - EMNIYET_SIBER
          - IDB
    RegisterResponse:
      required:
      - response
      type: object
      properties:
        response:
          $ref: '#/components/schemas/ApiResponse'
    LoginRequest:
      required:
      - password
      - username
      type: object
      properties:
        username:
          type: string
        password:
          type: string
    LoginResponse:
      required:
      - response
      type: object
      properties:
        response:
          $ref: '#/components/schemas/ApiResponse'
        token:
          type: string
        userId:
          type: integer
          format: int64
        username:
          type: string
        actingUserName:
          type: string
        roles:
          uniqueItems: true
          type: array
          items:
            type: string
        kurum:
          type: string
          description: Kullanıcı kurumu
          enum:
          - EMNIYET
          - MIT
          - JANDARMA
          - BTK
          - ADLI
          - EMNIYET_SIBER
          - IDB
    ChangePasswordRequest:
      required:
      - confirmPassword
      - currentPassword
      - newPassword
      type: object
      properties:
        currentPassword:
          type: string
        newPassword:
          type: string
        confirmPassword:
          type: string
    ChangePasswordResponse:
      required:
      - response
      type: object
      properties:
        response:
          $ref: '#/components/schemas/ApiResponse'
    GetUserResponse:
      required:
      - response
      type: object
      properties:
        response:
          $ref: '#/components/schemas/ApiResponse'
        user:
          $ref: '#/components/schemas/MakosUser'
    GetUserByIdResponse:
      required:
      - response
      type: object
      properties:
        response:
          $ref: '#/components/schemas/ApiResponse'
        user:
          $ref: '#/components/schemas/MakosUser'
    UsersListResponse:
      required:
      - response
      type: object
      properties:
        response:
          $ref: '#/components/schemas/ApiResponse'
        users:
          type: array
          items:
            $ref: '#/components/schemas/MakosUser'
    TespitTuruDTO:
      type: object
      properties:
        tespitTuru:
          type: integer
          format: int64
        aciklama:
          type: string
      description: Tespit Turu
    TespitTuruListResponse:
      required:
      - requestId
      - response
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: '#/components/schemas/MakosApiResponse'
        tespitTurleri:
          type: array
          items:
            $ref: '#/components/schemas/TespitTuruDTO'
    SucTipiDTO:
      type: object
      properties:
        sucTipiKodu:
          type: string
        aciklama:
          type: string
        mahkemeKaraTipiKodu:
          type: string
        durum:
          type: string
      description: Suc tipi
    SucTipleriResponse:
      required:
      - requestId
      - response
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: '#/components/schemas/MakosApiResponse'
        sucTipleri:
          type: array
          items:
            $ref: '#/components/schemas/SucTipiDTO'
    SorguTipiDTO:
      type: object
      properties:
        sorguTipi:
          type: integer
          format: int64
        aciklama:
          type: string
      description: Sorgu Tipi
    SorguTipiListResponse:
      required:
      - requestId
      - response
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: '#/components/schemas/MakosApiResponse'
        sorguTipleri:
          type: array
          items:
            $ref: '#/components/schemas/SorguTipiDTO'
    MahkemeKodlariResponse:
      required:
      - requestId
      - response
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: '#/components/schemas/MakosApiResponse'
        mahkemeKodListesi:
          type: array
          items:
            $ref: '#/components/schemas/MahkemeKoduDTO'
    MahkemeKoduDTO:
      type: object
      properties:
        mahkemeKodu:
          type: string
        mahkemeAdi:
          type: string
      description: Mahkeme Bilgi
    MahkemeKararTipiDTO:
      type: object
      properties:
        kararKodu:
          type: integer
          format: int64
        kararTipi:
          type: string
        kararTuru:
          type: string
        sonlandirma:
          type: integer
          format: int64
      description: Mahkeme Karar Tipi
    MahkemeKararTipleriResponse:
      required:
      - requestId
      - response
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: '#/components/schemas/MakosApiResponse'
        mahkemeKararTipiListesi:
          type: array
          items:
            $ref: '#/components/schemas/MahkemeKararTipiDTO'
    EvrakGelenKurumlarDTO:
      required:
      - kurumAdi
      - kurumKod
      type: object
      properties:
        id:
          type: integer
          description: Evrak gelen kurum ID
          format: int64
          example: 1
        kurumKod:
          maxLength: 10
          minLength: 0
          type: string
          description: Kurum kodu
          example: "01"
        kurumAdi:
          maxLength: 50
          minLength: 0
          type: string
          description: Kurum adı
          example: ADALET BAKANLIĞI
        kurum:
          maxLength: 64
          minLength: 0
          type: string
          description: Kurum
          example: ADALET BAKANLIĞI
        idx:
          type: integer
          description: Sıra numarası
          format: int64
          example: 1
      description: Evrak gelen kurumlar bilgilerini içerir
    EvrakGelenKurumlarResponse:
      required:
      - requestId
      - response
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: '#/components/schemas/MakosApiResponse'
        kurumlar:
          type: array
          items:
            $ref: '#/components/schemas/EvrakGelenKurumlarDTO'
    IllerDTO:
      required:
      - ilKod
      type: object
      properties:
        ilKod:
          maxLength: 4
          minLength: 0
          type: string
          description: İl kodu
          example: "0600"
        ilAdi:
          maxLength: 50
          minLength: 0
          type: string
          description: İl adı
          example: ANKARA
        ilceAdi:
          maxLength: 50
          minLength: 0
          type: string
          description: İlçe adı
          example: MERKEZ
      description: İl ve ilçe bilgilerini içerir
    IllerResponse:
      required:
      - requestId
      - response
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: '#/components/schemas/MakosApiResponse'
        iller:
          type: array
          items:
            $ref: '#/components/schemas/IllerDTO'
    AidiyatResponse:
      required:
      - requestId
      - response
      type: object
      properties:
        requestId:
          type: string
          format: uuid
        response:
          $ref: '#/components/schemas/MakosApiResponse'
        aidiyatlar:
          type: array
          items:
            $ref: '#/components/schemas/MKTalepAidiyatDTO'
    MKTalepAidiyatDTO:
      required:
      - aidiyatKod
      - mahkemeKararTalepId
      type: object
      properties:
        id:
          type: integer
          description: Mahkeme aidiyat talep ID
          format: int64
        mahkemeKararTalepId:
          type: integer
          description: İlişkili mahkeme karar talep ID
          format: int64
        aidiyatKod:
          maxLength: 25
          minLength: 0
          type: string
          description: Aidiyat kodu
          example: "02"
        durumu:
          maxLength: 10
          minLength: 0
          type: string
          description: Durum
          example: AKTIF
      description: Mahkeme Aidiyat Talep bilgilerini içerir
    HealthCheckResponse:
      required:
      - response
      type: object
      properties:
        response:
          $ref: '#/components/schemas/ApiResponse'
    GetMakosUserAuditLogsByUsernameResponse:
      required:
      - response
      type: object
      properties:
        response:
          $ref: '#/components/schemas/ApiResponse'
        auditLogs:
          $ref: '#/components/schemas/PageMakosUserAuditLog'
    MakosUserAuditLog:
      type: object
      properties:
        id:
          type: integer
          format: int64
        userAuditType:
          type: string
          description: MAKOS kullanıcı denetim türleri
          enum:
          - LOGIN
          - LOGOUT
          - IMPERSONATE_LOGIN
          - CHANGE_PASSWORD
          - GET_USERS_FOR_ADMIN
          - ACTIVATE
          - DEACTIVATE
          - ADD
          - UPDATE
          - DELETE
          - REGISTER
        username:
          type: string
        actingUsername:
          type: string
        userIp:
          type: string
        adminOperatedUsername:
          type: string
        requestTime:
          type: string
          format: date-time
        responseTime:
          type: string
          format: date-time
        responseCode:
          type: integer
          format: int32
    PageMakosUserAuditLog:
      type: object
      properties:
        totalPages:
          type: integer
          format: int32
        totalElements:
          type: integer
          format: int64
        first:
          type: boolean
        last:
          type: boolean
        size:
          type: integer
          format: int32
        content:
          type: array
          items:
            $ref: '#/components/schemas/MakosUserAuditLog'
        number:
          type: integer
          format: int32
        sort:
          $ref: '#/components/schemas/SortObject'
        numberOfElements:
          type: integer
          format: int32
        pageable:
          $ref: '#/components/schemas/PageableObject'
        empty:
          type: boolean
    PageableObject:
      type: object
      properties:
        offset:
          type: integer
          format: int64
        sort:
          $ref: '#/components/schemas/SortObject'
        pageNumber:
          type: integer
          format: int32
        pageSize:
          type: integer
          format: int32
        unpaged:
          type: boolean
        paged:
          type: boolean
    SortObject:
      type: object
      properties:
        empty:
          type: boolean
        sorted:
          type: boolean
        unsorted:
          type: boolean
    GetMakosUserAuditLogsByTypeResponse:
      required:
      - response
      type: object
      properties:
        response:
          $ref: '#/components/schemas/ApiResponse'
        auditLogs:
          $ref: '#/components/schemas/PageMakosUserAuditLog'
    GetMakosUserAuditLogListResponse:
      required:
      - response
      type: object
      properties:
        response:
          $ref: '#/components/schemas/ApiResponse'
        auditLogs:
          $ref: '#/components/schemas/PageMakosUserAuditLog'
    GetMakosUserAuditLogResponse:
      required:
      - response
      type: object
      properties:
        response:
          $ref: '#/components/schemas/ApiResponse'
        auditLog:
          $ref: '#/components/schemas/MakosUserAuditLog'
  securitySchemes:
    BasicAuth:
      type: http
      scheme: basic
