/**
 * IYM Backend OpenAPI definition
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { SorguTipiListResponse } from './sorguTipiListResponse';
import { ResponseIDYeniKararResponseException } from './responseIDYeniKararResponseException';


export interface ResponseSorguTipiListResponse { 
    resultCode?: ResponseSorguTipiListResponse.ResultCodeEnum;
    resultDetails?: string;
    exception?: ResponseIDYeniKararResponseException;
    result?: SorguTipiListResponse;
    success?: boolean;
}
export namespace ResponseSorguTipiListResponse {
    export const ResultCodeEnum = {
        Success: 'SUCCESS',
        Failed: 'FAILED',
        Rejected: 'REJECTED'
    } as const;
    export type ResultCodeEnum = typeof ResultCodeEnum[keyof typeof ResultCodeEnum];
}


