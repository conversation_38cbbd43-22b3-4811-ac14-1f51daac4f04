package iym.backend.kullanici.mapper;

import iym.backend.kullanici.dto.KullaniciDto;
import iym.backend.kullanici.entity.Kullanici;
import iym.backend.kullanici.enums.enumKullaniciStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;

import java.util.Collections;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Unit test for KullaniciMapper
 */
class KullaniciMapperTest {

    private final KullaniciMapper kullaniciMapper = Mappers.getMapper(KullaniciMapper.class);

    private Kullanici kullanici;
    private KullaniciDto kullaniciDto;

    @BeforeEach
    void setUp() {
        kullanici = new Kullanici();
        kullanici.setId(1L);
        kullanici.setKullaniciAdi("testuser");
        kullanici.setEmail("<EMAIL>");
        kullanici.setAd("Test");
        kullanici.setSoyad("User");
        kullanici.setTcno("12345678901");
        kullanici.setParola("encodedPassword123");
        kullanici.setStatus(enumKullaniciStatus.AKTIF);
        kullanici.setKullaniciKullaniciGruplar(Collections.emptyList());

        kullaniciDto = KullaniciDto.builder()
                .id(1L)
                .kullaniciAdi("testuser")
                .email("<EMAIL>")
                .ad("Test")
                .soyad("User")
                .tcno("12345678901")
                .parola("plainPassword123")
                .status("AKTIF")
                .kullaniciGrupIdList(Collections.emptyList())
                .kullaniciYetkiIdList(Collections.emptyList())
                .build();
    }

    @Test
    void toDto_shouldMapEntityToDto() {
        // When
        KullaniciDto result = kullaniciMapper.toDto(kullanici);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(kullanici.getId());
        assertThat(result.getKullaniciAdi()).isEqualTo(kullanici.getKullaniciAdi());
        assertThat(result.getEmail()).isEqualTo(kullanici.getEmail());
        assertThat(result.getAd()).isEqualTo(kullanici.getAd());
        assertThat(result.getSoyad()).isEqualTo(kullanici.getSoyad());
        assertThat(result.getTcno()).isEqualTo(kullanici.getTcno());
        assertThat(result.getStatus()).isEqualTo(kullanici.getStatus().name());
        assertThat(result.getKullaniciGrupIdList()).isEmpty();
        assertThat(result.getKullaniciYetkiIdList()).isEmpty();
    }

    @Test
    void toDto_shouldNotExposeParolaValue() {
        // Given - entity has an encoded password
        kullanici.setParola("$2a$10$encodedPasswordHash");

        // When
        KullaniciDto result = kullaniciMapper.toDto(kullanici);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getParola()).isNull(); // Password should not be exposed in DTO
    }

    @Test
    void toDto_shouldReturnNullWhenEntityIsNull() {
        // When
        KullaniciDto result = kullaniciMapper.toDto(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toEntity_shouldMapDtoToEntity() {
        // When
        Kullanici result = kullaniciMapper.toEntity(kullaniciDto);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(kullaniciDto.getId());
        assertThat(result.getKullaniciAdi()).isEqualTo(kullaniciDto.getKullaniciAdi());
        assertThat(result.getEmail()).isEqualTo(kullaniciDto.getEmail());
        assertThat(result.getAd()).isEqualTo(kullaniciDto.getAd());
        assertThat(result.getSoyad()).isEqualTo(kullaniciDto.getSoyad());
        assertThat(result.getTcno()).isEqualTo(kullaniciDto.getTcno());
        assertThat(result.getParola()).isEqualTo(kullaniciDto.getParola()); // Password should be mapped from DTO to entity
        // Note: status mapping is handled by service layer, not mapper
    }

    @Test
    void toEntity_shouldReturnNullWhenDtoIsNull() {
        // When
        Kullanici result = kullaniciMapper.toEntity(null);

        // Then
        assertThat(result).isNull();
    }

    @Test
    void toEntity_shouldIgnoreAuditFields() {
        // When
        Kullanici result = kullaniciMapper.toEntity(kullaniciDto);

        // Then
        assertThat(result).isNotNull();
        assertThat(result.getCreatedAt()).isNull();
        assertThat(result.getCreatedBy()).isNull();
        assertThat(result.getUpdatedAt()).isNull();
        assertThat(result.getUpdatedBy()).isNull();
        assertThat(result.getDeletedAt()).isNull();
        assertThat(result.getDeletedBy()).isNull();
        // isDeleted has a default value of false in BaseEntity
        assertThat(result.getIsDeleted()).isFalse();
    }

    @Test
    void toEntity_shouldIgnoreUserDetailsFields() {
        // When
        Kullanici result = kullaniciMapper.toEntity(kullaniciDto);

        // Then
        assertThat(result).isNotNull();
        // getUsername() returns kullaniciAdi, so it should match the mapped value
        assertThat(result.getUsername()).isEqualTo(kullaniciDto.getKullaniciAdi());
        // getPassword() returns parola, so it should match the mapped value
        assertThat(result.getPassword()).isEqualTo(kullaniciDto.getParola());
        assertThat(result.getRoles()).isEmpty(); // Should be empty since no groups are mapped
        assertThat(result.getNewPassword()).isNull(); // Should be null as it's not mapped
    }

    @Test
    void passwordSecurityValidation_entityToDtoShouldNeverExposePassword() {
        // Given - various password scenarios
        String[] testPasswords = {
            "plainPassword",
            "$2a$10$encodedBcryptHash",
            "simplePassword123",
            "",
            null
        };

        for (String password : testPasswords) {
            // Given
            kullanici.setParola(password);

            // When
            KullaniciDto result = kullaniciMapper.toDto(kullanici);

            // Then
            assertThat(result.getParola())
                .as("Password should never be exposed in DTO regardless of input: '%s'", password)
                .isNull();
        }
    }
}
