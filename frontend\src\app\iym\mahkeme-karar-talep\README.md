# Mahkeme Karar Talep Generic Component

Bu component, MAKOS modülündeki tüm mahkeme karar talep işlemleri için ortak bir giriş ekranı sağlar. Ortak alanlar için tek bir tab ve her mahkeme karar türü için özel alanlar için dinamik tablar sunar.

## Özellikler

- **Ortak <PERSON>**: Tüm mahkeme karar türlerinde ortak olan `EvrakDetay` ve `MahkemeKararBilgisi` alanları için tek bir form
- **Dinamik Tablar**: Seçilen mahkeme karar türüne göre otomatik olarak açılan özel alan tabları
- **Test Verisi Desteği**: Her mahkeme karar türü için ayrı `fillTestData` metotları
- **Validasyon**: Zorunlu alanlar için client-side validasyon
- **Dosya <PERSON>ükleme**: Mahkeme karar dosyası yükleme desteği

## Desteklenen Mahkeme Karar Türleri

1. **ID Yeni Karar** (`ID_YENI_KARAR`)
2. **ID Uzatma Kararı** (`ID_UZATMA_KARARI`)
3. **ID Sonlandırma Kararı** (`ID_SONLANDIRMA_KARARI`)
4. **ID Hedef Güncelleme** (`ID_HEDEF_GUNCELLEME`)
5. **ID Mahkeme Karar Güncelleme** (`ID_MAHKEME_KARAR_GUNCELLEME`)
6. **ID Aidiyat Güncelleme** (`ID_AIDIYAT_GUNCELLEME`)
7. **IT Karar** (`IT_KARAR`)

## Ortak Alanlar

### Evrak Detayları
- `evrakNo` (zorunlu)
- `evrakTarihi` (zorunlu)
- `evrakKurumKodu` (zorunlu)
- `evrakTuru` (zorunlu)
- `geldigiIlIlceKodu` (zorunlu)
- `havaleBirimi`
- `evrakKonusu`
- `evrakAciklama`
- `acilmi`

### Mahkeme Karar Detayları
- `mahkemeKararTipi` (zorunlu)
- `mahkemeKodu` (zorunlu)
- `mahkemeKararNo` (zorunlu)
- `mahkemeIlIlceKodu` (zorunlu)
- `sorusturmaNo`
- `mahkemeAciklama`

## Özel Alanlar

### ID Yeni Karar
- `hedefDetayListesi`
- `mahkemeAidiyatKodlari`
- `mahkemeSucTipiKodlari`

### ID Uzatma Kararı
- `hedefDetayListesi`

### ID Sonlandırma Kararı
- `hedefDetayListesi`

### ID Hedef Güncelleme
- `hedefDetayListesi`

### ID Mahkeme Karar Güncelleme
- `mahkemeAidiyatKodlari`
- `mahkemeSucTipiKodlari`

### ID Aidiyat Güncelleme
- `mahkemeAidiyatKodlari`
- `mahkemeSucTipiKodlari`

### IT Karar
- `hedefDetayListesi`

## Kullanım

### Route
```
/iym/mahkeme-karar-talep
```

### Component Yapısı
```typescript
// Component başlatma
constructor(
  private fb: FormBuilder,
  private makosService: MakosControllerService,
  private messageService: MessageService
) {}
```

### Form Yapısı
- `commonForm`: Ortak alanlar için ana form
- `kararTuru`: Mahkeme karar türü seçimi
- Dinamik olarak oluşturulan özel alan form grupları

## Test Verisi

### Ortak Test Verisi
Tüm mahkeme karar türleri için ortak alanlarda `fillCommonTestData()` metodu kullanılır.

### Özel Test Verileri
Her mahkeme karar türü için ayrı test verisi doldurma butonları sağlanmıştır:
- `fillIDYeniKararTestData()`
- `fillIDUzatmaKarariTestData()`
- `fillIDSonlandirmaKarariTestData()`
- `fillIDHedefGuncellemeTestData()`
- `fillIDMahkemeKararGuncellemeTestData()`
- `fillIDAidiyatGuncellemeTestData()`
- `fillITKararTestData()`

## API Entegrasyonu

Component, `MakosControllerService` üzerinden aşağıdaki API endpoint'lerini kullanır:

- `yeniKararID()` - ID Yeni Karar
- `uzatmaKarariID()` - ID Uzatma Kararı
- `sonlandirmaKarariID()` - ID Sonlandırma Kararı
- `hedefBilgisiGuncelle()` - ID Hedef Güncelleme
- `mahkemeBilgisiGuncelle()` - ID Mahkeme Karar Güncelleme
- `aidiyatBilgisiGuncelle()` - ID Aidiyat Güncelleme
- `itKarar()` - IT Karar

## Validasyon Kuralları

### Ortak Alanlar
- Tüm zorunlu alanlar için required validasyonu
- Tarih formatı kontrolü
- Sayısal alanlar için numeric validasyonu

### Özel Alanlar
- Liste alanları için array validasyonu
- Dosya yükleme için dosya tipi ve boyutu kontrolü

## Styling

Component, PrimeNG teması kullanır ve responsive tasarıma sahiptir:
- Desktop: 2 sütunlu grid layout
- Tablet: 1-2 sütunlu esnek grid
- Mobile: Tek sütunlu layout

## Geliştirme Notları

### Eklenen Dosyalar
- `mahkeme-karar-talep.component.ts`
- `mahkeme-karar-talep.component.html`
- `mahkeme-karar-talep.component.scss`
- `mahkeme-karar-talep.module.ts`
- `README.md`

### Route Güncellemesi
```typescript
// app.routes.ts
{ path: 'iym/mahkeme-karar-talep', component: MahkemeKararTalepComponent, canActivate: [AuthGuard] }
```

## Gelecek Geliştirmeler

- Mahkeme ve kurum kodları için autocomplete desteği
- Özel alanlar için dynamic form generation
- Form kaydetme ve yükleme özelliği
- Excel import/export desteği