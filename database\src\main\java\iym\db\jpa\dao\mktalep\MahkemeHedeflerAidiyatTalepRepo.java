package iym.db.jpa.dao.mktalep;

import iym.common.model.entity.iym.talep.MahkemeHedeflerAidiyatTalep;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for MahkemeHedeflerAidiyatTalep entity
 */
@Repository
public interface MahkemeHedeflerAidiyatTalepRepo extends JpaRepository<MahkemeHedeflerAidiyatTalep, Long> {

    List<MahkemeHedeflerAidiyatTalep> findByHedefId(Long hedefId);
    
    List<MahkemeHedeflerAidiyatTalep> findByMahkemeKararId(Long mahkemeKararId);
    
    List<MahkemeHedeflerAidiyatTalep> findByAidiyatKod(String aidiyatKod);
    
    List<MahkemeHedeflerAidiyatTalep> findByDurumu(String durumu);
    
    List<MahkemeHedeflerAidiyatTalep> findByKullaniciId(Long kullaniciId);
    
    List<MahkemeHedeflerAidiyatTalep> findByTarihBetween(LocalDateTime startDate, LocalDateTime endDate);
    
    Optional<MahkemeHedeflerAidiyatTalep> findByHedefIdAndAidiyatKodAndMahkemeKararId(
            Long hedefId, 
            String aidiyatKod, 
            Long mahkemeKararId);
    
    List<MahkemeHedeflerAidiyatTalep> findByHedefIdAndMahkemeKararId(Long hedefId, Long mahkemeKararId);
    
    List<MahkemeHedeflerAidiyatTalep> findByHedefIdAndAidiyatKod(Long hedefId, String aidiyatKod);
    
    List<MahkemeHedeflerAidiyatTalep> findByMahkemeKararIdAndAidiyatKod(Long mahkemeKararId, String aidiyatKod);
}
