package iym.db.jpa.dao.mkislem;

import iym.common.model.entity.iym.mkislem.HedeflerIslem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Repository interface for HedeflerIslem entity
 */
@Repository
public interface HedeflerIslemRepo extends JpaRepository<HedeflerIslem, Long> {

    List<HedeflerIslem> findByMahkemeKararIslemId(Long mahkemeKararIslemId);

}
