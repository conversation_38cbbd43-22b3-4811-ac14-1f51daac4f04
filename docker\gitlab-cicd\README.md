# GitLab CI/CD Tam Otomatik Kurulum (2024)

Bu dizin GitLab CI/CD'yi Docker ile **tamamen otomatik** kurmak için gerekli script ve konfigürasyon dosyalarını içerir.

## 🚀 Hızlı Başlangıç (Tek Komut!)

```powershell
# GitLab CI/CD dizinine gidin
cd C:\temp\iym\docker\gitlab-cicd

# Tek komutla tüm sistemi kurun
.\setup-gitlab-complete.ps1
```

**Bu kadar!** Script otomatik olarak:
- GitLab ve Runner'ı başlatır
- Root password'ü alır ve kaydeder
- API ile token oluşturur
- Runner'ı register eder
- Proje kurulum talimatları verir
- Tüm bilgileri `.secrets/` dizininde saklar

## ✨ Yeni Özellikler (2024)

- **🤖 Tamamen Otomatik**: Manuel token alma gereksiz!
- **🔗 GitLab API Entegrasyonu**: Otomatik token oluşturma
- **🔐 Akıllı Secrets Yönetimi**: Tüm önemli bilgiler `.secrets/` dizininde
- **📱 Güncel Yöntemler**: GitLab 15.6+ yeni token sistemi
- **📦 Tek Script**: Tüm işlevler birleştirildi
- **💾 Kalıcı Bilgiler**: Terminal kapansa da bilgiler kayıtlı

## 📋 Sistem Gereksinimleri

- Windows 11 with [Docker Desktop](https://www.docker.com/products/docker-desktop/)
- En az 8 GB RAM (GitLab kaynak yoğun)
- PowerShell 5.1+ (Windows ile birlikte gelir)

## 🎯 Özellikler

- **Source Code Hosting** (GitHub alternatifi)
- **CI/CD Pipelines** via `.gitlab-ci.yml` (GitHub Actions alternatifi)
- **Built-in Docker Container Registry**
- **Built-in Package Registry** (Maven, npm, Docker)
- **Unlimited private repositories and pipelines**

## 📁 Kaydedilen Bilgiler (.secrets dizini)

Script çalıştıktan sonra `.secrets/` dizininde şu bilgiler saklanır:

```
.secrets/
├── initial_root_password      # GitLab root password
├── personal_access_token      # GitLab API token
├── runner_token              # GitLab Runner token
├── runner_info.json          # Runner detay bilgileri
├── project_info.json         # Proje URL'leri ve bilgileri
└── last_push.json           # Son push bilgileri
```

Bu bilgileri daha sonra kullanmak için:

```powershell
# Root password'ü al
Get-Content .secrets\initial_root_password

# Proje bilgilerini gör
Get-Content .secrets\project_info.json | ConvertFrom-Json

# Runner token'ı al
Get-Content .secrets\runner_token
```

## 🔧 Script Parametreleri

```powershell
# Özelleştirilmiş kurulum
.\setup-gitlab-complete.ps1 -ProjectName "my-project" -GitLabUrl "http://localhost:8929" -SkipVolumeSetup
```

**Parametreler:**
- `-ProjectName`: Proje adı (varsayılan: "iym")
- `-GitLabUrl`: GitLab URL'si (varsayılan: "http://localhost:8929")
- `-RegistryUrl`: Docker Registry URL'si (varsayılan: "http://localhost:5050")
- `-ProjectPath`: Proje dizini (varsayılan: mevcut dizin)
- `-SkipVolumeSetup`: External volume kurulumunu atla

## 🔧 Manuel Kurulum (Eski Yöntem - Gerekirse)

### 1. GitLab ve Runner Başlatma

```powershell
docker-compose up -d
```

### 2. Root Password Alma

```powershell
docker exec -it docker-gitlab-cicd-web-1 grep 'Password:' /etc/gitlab/initial_root_password
```

### 3. Runner Register Etme

```powershell
# GitLab UI'dan token al (Admin Area > Runners)
docker exec -it docker-gitlab-cicd-runner-1 gitlab-runner register
```

## 📋 Örnek `.gitlab-ci.yml`

Script otomatik olarak `gitlab-ci.yml.example` dosyasını `.gitlab-ci.yml` olarak kopyalar:

```yaml
stages:
  - build
  - test
  - docker

variables:
  MAVEN_OPTS: "-Dmaven.repo.local=.m2/repository"

cache:
  paths:
    - .m2/repository
    - frontend/node_modules

build-backend:
  stage: build
  image: maven:3.9.6-eclipse-temurin-17
  script:
    - mvn clean install -DskipTests
  artifacts:
    paths:
      - backend/target/*.jar
      - makos/target/*.jar

build-frontend:
  stage: build
  image: node:18
  script:
    - cd frontend
    - npm install
    - npm run build --prod
  artifacts:
    paths:
      - frontend/dist

test-backend:
  stage: test
  image: maven:3.9.6-eclipse-temurin-17
  script:
    - mvn test -Dspring.profiles.active=test -Dtest="!*IntegrationTest" -DfailIfNoTests=false
  artifacts:
    paths:
      - "**/target/surefire-reports/*.xml"
    reports:
      junit: "**/target/surefire-reports/*.xml"

docker-build-backend:
  stage: docker
  image: docker:20
  services:
    - docker:dind
  script:
    - export VERSION=$(mvn help:evaluate -Dexpression=project.version -q -DforceStdout)
    - docker build -t localhost:5050/iym/backend:$VERSION -f backend/Dockerfile .
    - docker login -u gitlab-ci-token -p $CI_JOB_TOKEN localhost:5050
    - docker push localhost:5050/iym/backend:$VERSION

docker-build-makos:
  stage: docker
  image: docker:20
  services:
    - docker:dind
  script:
    - export VERSION=$(mvn help:evaluate -Dexpression=project.version -q -DforceStdout)
    - docker build -t localhost:5050/iym/makos:$VERSION -f makos/Dockerfile .
    - docker login -u gitlab-ci-token -p $CI_JOB_TOKEN localhost:5050
    - docker push localhost:5050/iym/makos:$VERSION

docker-build-frontend:
  stage: docker
  image: docker:20
  services:
    - docker:dind
  script:
    - export VERSION=$(mvn help:evaluate -Dexpression=project.version -q -DforceStdout)
    - docker build -t localhost:5050/iym/frontend:$VERSION -f frontend/Dockerfile frontend/
    - docker login -u gitlab-ci-token -p $CI_JOB_TOKEN localhost:5050
    - docker push localhost:5050/iym/frontend:$VERSION
```

## Accessing the Container Registry

The GitLab Container Registry is available at `http://localhost:5050`. You can push and pull Docker images using:

```bash
# Login to the registry
docker login localhost:5050

# Push an image
docker push localhost:5050/iym/backend:latest

# Pull an image
docker pull localhost:5050/iym/backend:latest
```

## 🔗 Önemli URL'ler

Script çalıştıktan sonra şu URL'leri kullanabilirsiniz:

- **GitLab Web UI**: http://localhost:8929
- **Docker Registry**: http://localhost:5050
- **Proje URL**: http://localhost:8929/root/iym
- **Pipeline URL**: http://localhost:8929/root/iym/-/pipelines

## 🛠️ Bakım İşlemleri

### GitLab'ı Durdurma

```powershell
docker-compose down
```

### GitLab'ı Yeniden Başlatma

```powershell
docker-compose up -d
```

### Backup Alma

```powershell
docker exec -it docker-gitlab-cicd-web-1 gitlab-backup create
```

### GitLab Güncelleme

```powershell
docker-compose pull
docker-compose down
docker-compose up -d
```

## 🆘 Sorun Giderme

### Script Çalışmıyor
1. Docker Desktop'ın çalıştığından emin olun
2. PowerShell'i yönetici olarak çalıştırın
3. `.secrets/` dizinini kontrol edin

### GitLab Açılmıyor
1. Container'ların çalıştığını kontrol edin: `docker ps`
2. GitLab loglarını kontrol edin: `docker logs gitlab-cicd-web-1`
3. 3-5 dakika bekleyin (GitLab yavaş başlar)

### Runner Register Olmuyor
1. `.secrets/runner_token` dosyasını kontrol edin
2. GitLab'da Admin Area > Runners'ı kontrol edin
3. Script'i tekrar çalıştırın

## 📞 Destek

Sorun yaşarsanız:
1. `.secrets/` dizinindeki dosyaları kontrol edin
2. Docker loglarını inceleyin
3. Script'i tekrar çalıştırın (idempotent - güvenle tekrar çalıştırılabilir)