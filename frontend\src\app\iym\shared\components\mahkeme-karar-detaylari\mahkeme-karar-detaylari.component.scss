.mahkeme-karar-detaylari {
  .ng-invalid.ng-dirty {
    border-color: #ef4444;
    box-shadow: 0 0 0 1px #ef4444;
  }

  .text-red-500 {
    color: #ef4444;
  }

  .text-gray-700 {
    color: #374151;
  }

  .text-gray-500 {
    color: #6b7280;
  }

  .font-medium {
    font-weight: 500;
  }

  .text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .mb-1 {
    margin-bottom: 0.25rem;
  }

  .mb-2 {
    margin-bottom: 0.5rem;
  }

  .gap-4 {
    gap: 1rem;
  }

  .w-full {
    width: 100%;
  }

  .grid {
    display: grid;
  }

  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .flex {
    display: flex;
  }

  .flex-col {
    flex-direction: column;
  }

  @media (min-width: 768px) {
    .md\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .md\:col-span-2 {
      grid-column: span 2 / span 2;
    }
  }
}
