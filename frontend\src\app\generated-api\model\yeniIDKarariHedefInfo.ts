/**
 * IYM Backend OpenAPI definition
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { IDHedefAidiyatInfo } from './iDHedefAidiyatInfo';
import { IDHedefInfo } from './iDHedefInfo';


export interface YeniIDKarariHedefInfo { 
    hedefBilgileri?: IDHedefInfo;
    baslamaTarihi?: string;
    sureTip?: YeniIDKarariHedefInfo.SureTipEnum;
    sure?: number;
    hedefAidiyatListesi?: Array<IDHedefAidiyatInfo>;
    canakNo?: string;
}
export namespace YeniIDKarariHedefInfo {
    export const SureTipEnum = {
        Gun: 'GUN',
        Ay: 'AY',
        Hicbiri: 'HICBIRI'
    } as const;
    export type SureTipEnum = typeof SureTipEnum[keyof typeof SureTipEnum];
}


