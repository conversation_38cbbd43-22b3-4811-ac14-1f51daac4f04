<p-dialog 
  header="JSON İçe/Dışa Aktarma" 
  [(visible)]="visible" 
  (onHide)="onDialogHide()"
  [modal]="true" 
  [style]="{width: '90vw', maxHeight: '90vh'}" 
  [draggable]="false" 
  [resizable]="false"
  styleClass="json-dialog"
>
  <div class="json-content">
    <p-tabView (onChange)="onTabChange($event)">
      <!-- Export Tab -->
      <p-tabPanel header="📤 Dışa Aktar">
        <div class="export-section">
          <div class="mb-4">
            <h4 class="text-lg font-semibold text-gray-800 mb-2">Form Verilerini JSON Olarak Dışa Aktar</h4>
            <p class="text-sm text-gray-600 mb-4">
              Mevcut form verilerinizi JSON formatında dışa aktarabilir, daha sonra aynı veya farklı bir talepte kullanabilirsiniz.
            </p>
            
            <div class="bg-blue-50 p-3 rounded mb-4">
              <div class="flex items-center gap-2 text-sm text-blue-800">
                <i class="pi pi-info-circle"></i>
                <span><strong>Karar Türü:</strong> {{ getKararTuruLabel(kararTuru!) }}</span>
              </div>
              <div class="flex items-center gap-2 text-sm text-blue-800 mt-1">
                <i class="pi pi-file"></i>
                <span><strong>Dosya Boyutu:</strong> {{ getJsonSize() }}</span>
              </div>
            </div>
          </div>

          <div class="mb-4">
            <label class="text-sm font-medium text-gray-700 mb-2 block">JSON Verisi:</label>
            <textarea 
              [(ngModel)]="exportedJson"
              readonly
              rows="15"
              class="w-full p-3 border border-gray-300 rounded font-mono text-sm bg-gray-50"
              placeholder="JSON verisi burada görünecek..."
            ></textarea>
          </div>

          <div class="flex gap-3">
            <p-button 
              label="Panoya Kopyala" 
              icon="pi pi-copy" 
              severity="info"
              (onClick)="copyToClipboard()"
              [disabled]="!exportedJson"
            >
            </p-button>
            <p-button 
              label="Dosya Olarak İndir" 
              icon="pi pi-download" 
              severity="success"
              (onClick)="downloadJson()"
              [disabled]="!exportedJson"
            >
            </p-button>
          </div>
        </div>
      </p-tabPanel>

      <!-- Import Tab -->
      <p-tabPanel header="📥 İçe Aktar">
        <div class="import-section">
          <div class="mb-4">
            <h4 class="text-lg font-semibold text-gray-800 mb-2">JSON Verisini İçe Aktar</h4>
            <p class="text-sm text-gray-600 mb-4">
              Daha önce dışa aktardığınız JSON verisini buraya yapıştırarak form alanlarını otomatik olarak doldurabilirsiniz.
            </p>
            
            <div class="bg-yellow-50 p-3 rounded mb-4">
              <div class="flex items-center gap-2 text-sm text-yellow-800">
                <i class="pi pi-exclamation-triangle"></i>
                <span><strong>Uyarı:</strong> İçe aktarma işlemi mevcut form verilerinin üzerine yazacaktır.</span>
              </div>
            </div>
          </div>

          <!-- File Upload -->
          <div class="mb-4">
            <label class="text-sm font-medium text-gray-700 mb-2 block">Dosyadan Yükle:</label>
            <p-fileUpload 
              mode="basic" 
              accept=".json"
              [maxFileSize]="1000000"
              (onSelect)="onFileSelect($event)"
              chooseLabel="JSON Dosyası Seç"
              chooseIcon="pi pi-upload"
              class="w-full"
            >
            </p-fileUpload>
          </div>

          <div class="mb-4">
            <div class="flex justify-between items-center mb-2">
              <label class="text-sm font-medium text-gray-700">JSON Verisi:</label>
              <p-button 
                label="Formatla" 
                icon="pi pi-code" 
                size="small"
                severity="secondary"
                (onClick)="formatJson()"
                [disabled]="!importJson.trim()"
              >
              </p-button>
            </div>
            <textarea 
              [(ngModel)]="importJson"
              (ngModelChange)="onImportJsonChange()"
              rows="15"
              class="w-full p-3 border rounded font-mono text-sm"
              [class.border-red-300]="!isValidJson"
              [class.border-green-300]="isValidJson && importJson.trim()"
              [class.border-gray-300]="!importJson.trim()"
              placeholder="JSON verisini buraya yapıştırın..."
            ></textarea>
            
            <!-- Validation Messages -->
            <div *ngIf="importError" class="mt-2">
              <div class="flex items-center gap-2 text-sm" 
                   [class.text-red-600]="!isValidJson"
                   [class.text-yellow-600]="isValidJson && importError">
                <i class="pi" [class.pi-times-circle]="!isValidJson" [class.pi-exclamation-triangle]="isValidJson && importError"></i>
                <span>{{ importError }}</span>
              </div>
            </div>
            
            <div *ngIf="isValidJson && importJson.trim() && !importError" class="mt-2">
              <div class="flex items-center gap-2 text-sm text-green-600">
                <i class="pi pi-check-circle"></i>
                <span>Geçerli JSON formatı</span>
              </div>
            </div>
          </div>

          <div class="flex gap-3">
            <p-button 
              label="Temizle" 
              icon="pi pi-trash" 
              severity="secondary"
              (onClick)="clearImport()"
              [disabled]="!importJson.trim()"
            >
            </p-button>
            <p-button 
              label="İçe Aktar" 
              icon="pi pi-upload" 
              severity="success"
              (onClick)="importFromJson()"
              [disabled]="!isValidJson || !importJson.trim()"
            >
            </p-button>
          </div>
        </div>
      </p-tabPanel>
    </p-tabView>
  </div>

  <ng-template pTemplate="footer">
    <div class="flex justify-end">
      <p-button 
        label="Kapat" 
        severity="secondary" 
        icon="pi pi-times"
        (onClick)="onDialogHide()"
      >
      </p-button>
    </div>
  </ng-template>
</p-dialog>
