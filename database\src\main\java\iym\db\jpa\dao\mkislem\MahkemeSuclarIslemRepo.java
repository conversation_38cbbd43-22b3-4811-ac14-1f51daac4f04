package iym.db.jpa.dao.mkislem;

import iym.common.model.entity.iym.mkislem.MahkemeSuclarIslem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface MahkemeSuclarIslemRepo extends JpaRepository<MahkemeSuclarIslem, Long> {


    List<MahkemeSuclarIslem> findByMahkemeKararIslemId(Long mahkemeKararIslemId);

    Optional<MahkemeSuclarIslem> findByMahkemeKararIslemIdAndSucTipKodu(Long mahkemeKararIslemId, String sucTipKodu);

}
