<div class="p-4">
  <!-- Başlık -->
  <div class="flex justify-between items-center mb-4">
    <h2 class="text-2xl font-bold text-gray-800">
      <i class="pi pi-search mr-2"></i>
      E<PERSON><PERSON>
    </h2>
    <div class="flex gap-2">
      <p-button
        icon="pi pi-file-excel"
        label="Excel"
        severity="success"
        size="small"
        (onClick)="excelAktar()"
        [disabled]="evraklar.length === 0">
      </p-button>
      <p-button
        icon="pi pi-file-pdf"
        label="PDF"
        severity="danger"
        size="small"
        (onClick)="pdfAktar()"
        [disabled]="evraklar.length === 0">
      </p-button>
    </div>
  </div>

  <!-- <PERSON><PERSON> Filtreleri -->
  <p-card header="Arama Filtreleri" class="mb-4">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">

      <!-- Evrak No -->
      <div class="flex flex-col">
        <label for="evrakNo" class="text-sm font-medium text-gray-700 mb-1">
          Evrak No
        </label>
        <input
          pInputText
          id="evrakNo"
          [(ngModel)]="aramaFiltresi.evrakNo"
          placeholder="Evrak numarası giriniz"
          class="w-full">
      </div>

      <!-- Başlangıç Tarihi -->
      <div class="flex flex-col">
        <label for="baslangicTarihi" class="text-sm font-medium text-gray-700 mb-1">
          Başlangıç Tarihi
        </label>
        <p-datepicker
          [(ngModel)]="aramaFiltresi.baslangicTarihi"
          dateFormat="dd/mm/yy"
          placeholder="Başlangıç tarihi seçiniz"
          icon="pi pi-calendar" iconDisplay="input"
          inputId="baslangicTarihi"
          class="w-full">
        </p-datepicker>
      </div>

      <!-- Bitiş Tarihi -->
      <div class="flex flex-col">
        <label for="bitisTarihi" class="text-sm font-medium text-gray-700 mb-1">
          Bitiş Tarihi
        </label>
        <p-datepicker
          [(ngModel)]="aramaFiltresi.bitisTarihi"
          dateFormat="dd/mm/yy"
          placeholder="Bitiş tarihi seçiniz"
          icon="pi pi-calendar" iconDisplay="input"
          inputId="bitisTarihi"
          class="w-full">
        </p-datepicker>
      </div>

      <!-- Mahkeme Kodu -->
      <div class="flex flex-col">
        <label for="mahkemeKodu" class="text-sm font-medium text-gray-700 mb-1">
          Mahkeme
        </label>
        <p-select
          [(ngModel)]="aramaFiltresi.mahkemeKodu"
          [options]="mahkemeKodlari"
          optionLabel="label"
          optionValue="value"
          placeholder="Mahkeme seçiniz"
          inputId="mahkemeKodu"
          class="w-full">
        </p-select>
      </div>

      <!-- Evrak Tipi -->
      <div class="flex flex-col">
        <label for="evrakTipi" class="text-sm font-medium text-gray-700 mb-1">
          Evrak Tipi
        </label>
        <p-select
          [(ngModel)]="aramaFiltresi.evrakTipi"
          [options]="evrakTipleri"
          optionLabel="label"
          optionValue="value"
          placeholder="Evrak tipi seçiniz"
          inputId="evrakTipi"
          class="w-full">
        </p-select>
      </div>

      <!-- Karar Tipi -->
      <div class="flex flex-col">
        <label for="kararTipi" class="text-sm font-medium text-gray-700 mb-1">
          Karar Tipi
        </label>
        <p-select
          [(ngModel)]="aramaFiltresi.kararTipi"
          [options]="kararTipleri"
          optionLabel="label"
          optionValue="value"
          placeholder="Karar tipi seçiniz"
          inputId="kararTipi"
          class="w-full">
        </p-select>
      </div>

      <!-- Hedef No -->
      <div class="flex flex-col">
        <label for="hedefNo" class="text-sm font-medium text-gray-700 mb-1">
          Hedef No
        </label>
        <input
          pInputText
          id="hedefNo"
          [(ngModel)]="aramaFiltresi.hedefNo"
          placeholder="Hedef numarası giriniz"
          class="w-full">
      </div>

      <!-- Boş alan -->
      <div class="flex flex-col justify-end">
        <div class="flex gap-2">
          <p-button
            icon="pi pi-search"
            label="Ara"
            (onClick)="evrakAra()"
            [loading]="yukleniyor"
            class="flex-1">
          </p-button>
          <p-button
            icon="pi pi-times"
            label="Temizle"
            severity="secondary"
            (onClick)="filtreleriTemizle()"
            class="flex-1">
          </p-button>
        </div>
      </div>
    </div>
  </p-card>

  <!-- Sonuçlar Tablosu -->
  <p-card header="Arama Sonuçları" class="mb-4">
    <p-table
      [value]="evraklar"
      [loading]="yukleniyor"
      [paginator]="true"
      [rows]="10"
      [rowsPerPageOptions]="[10, 25, 50]"
      [showCurrentPageReport]="true"
      currentPageReportTemplate="{first} - {last} / {totalRecords} kayıt"
      [globalFilterFields]="['evrakNo', 'evrakTipi', 'mahkemeKodu']"
      responsiveLayout="scroll"
      styleClass="p-datatable-sm">

      <ng-template pTemplate="header">
        <tr>
          <th pSortableColumn="evrakNo">
            Evrak No
            <p-sortIcon field="evrakNo"></p-sortIcon>
          </th>
          <th pSortableColumn="evrakTarihi">
            Evrak Tarihi
            <p-sortIcon field="evrakTarihi"></p-sortIcon>
          </th>
          <th pSortableColumn="evrakTipi">
            Evrak Tipi
            <p-sortIcon field="evrakTipi"></p-sortIcon>
          </th>
          <th pSortableColumn="mahkemeKodu">
            Mahkeme Kodu
            <p-sortIcon field="mahkemeKodu"></p-sortIcon>
          </th>
          <th pSortableColumn="kararTipi">
            Karar Tipi
            <p-sortIcon field="kararTipi"></p-sortIcon>
          </th>
          <th pSortableColumn="durumu">
            Durum
            <p-sortIcon field="durumu"></p-sortIcon>
          </th>
          <th>İşlemler</th>
        </tr>
      </ng-template>

      <ng-template pTemplate="body" let-evrak>
        <tr>
          <td>
            <span class="font-mono text-sm">{{ evrak.evrakNo }}</span>
          </td>
          <td>{{ evrak.evrakTarihi }}</td>
          <td>
            <p-tag
              [value]="evrakTipiKisaAd(evrak.evrakTipi)"
              [severity]="evrak.evrakTipi === 'İletişimin Denetlenmesi' ? 'info' : 'warn'">
            </p-tag>
          </td>
          <td>
            <span class="font-mono text-sm">{{ evrak.mahkemeKodu }}</span>
          </td>
          <td>{{ evrak.kararTipi }}</td>
          <td>
            <p-tag
              [value]="evrak.durumu"
              [severity]="durumSeviyesiGetir(evrak.durumu)">
            </p-tag>
          </td>
          <td>
            <p-button
              icon="pi pi-eye"
              size="small"
              severity="info"
              (onClick)="evrakDetayGoster(evrak)"
              pTooltip="Detay Görüntüle"
              tooltipPosition="top">
            </p-button>
          </td>
        </tr>
      </ng-template>

      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="7" class="text-center py-8">
            <i class="pi pi-info-circle text-4xl text-gray-400 mb-2"></i>
            <p class="text-gray-500">Arama kriterlerinize uygun evrak bulunamadı.</p>
          </td>
        </tr>
      </ng-template>
    </p-table>
  </p-card>

  <!-- Toast Mesajları -->
  <p-toast></p-toast>

  <!-- Loading Spinner -->
  <div *ngIf="yukleniyor" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <p-progressSpinner></p-progressSpinner>
  </div>
</div>

<!-- Evrak Detay Dialog -->
<p-dialog
  header="Evrak Detayı"
  [(visible)]="detayDialogGoruntule"
  [modal]="true"
  [style]="{width: '80vw', maxWidth: '800px'}"
  [closable]="true"
  (onHide)="detayDialogKapat()">

  <div *ngIf="evrakDetayi" class="space-y-4">
    <!-- Genel Bilgiler -->
    <div class="grid grid-cols-2 gap-4">
      <div>
        <label class="text-sm font-medium text-gray-700">Evrak No:</label>
        <p class="font-mono">{{ evrakDetayi.evrakNo }}</p>
      </div>
      <div>
        <label class="text-sm font-medium text-gray-700">Evrak Tarihi:</label>
        <p>{{ evrakDetayi.evrakTarihi }}</p>
      </div>
      <div>
        <label class="text-sm font-medium text-gray-700">Evrak Tipi:</label>
        <p>{{ evrakDetayi.evrakTipi }}</p>
      </div>
      <div>
        <label class="text-sm font-medium text-gray-700">Geldiği Kurum:</label>
        <p>{{ evrakDetayi.evrakGeldigiKurum }}</p>
      </div>
    </div>

    <p-divider></p-divider>

    <!-- Mahkeme Bilgileri -->
    <div *ngIf="evrakDetayi.mahkemeKarar">
      <h4 class="text-lg font-semibold mb-3">Mahkeme Kararı</h4>
      <div class="grid grid-cols-2 gap-4">
        <div>
          <label class="text-sm font-medium text-gray-700">Karar Tipi:</label>
          <p>{{ evrakDetayi.mahkemeKarar.kararTip }}</p>
        </div>
        <div>
          <label class="text-sm font-medium text-gray-700">Mahkeme Kodu:</label>
          <p class="font-mono">{{ evrakDetayi.mahkemeKarar.mahkemeKodu }}</p>
        </div>
        <div>
          <label class="text-sm font-medium text-gray-700">Karar No:</label>
          <p>{{ evrakDetayi.mahkemeKarar.mahkemeKararNo }}</p>
        </div>
        <div>
          <label class="text-sm font-medium text-gray-700">Mahkeme İli:</label>
          <p>{{ evrakDetayi.mahkemeKarar.mahkemeIli }}</p>
        </div>
      </div>
    </div>

    <!-- Hedefler -->
    <div *ngIf="mahkemeKararHedefleriVar">
      <p-divider></p-divider>
      <h4 class="text-lg font-semibold mb-3">Hedefler</h4>
      <div class="space-y-3">
        <div *ngFor="let hedef of evrakDetayi.mahkemeKarar?.hedefler" class="border rounded p-3">
          <div class="grid grid-cols-2 gap-2 text-sm">
            <div>
              <span class="font-medium">Hedef No:</span> {{ hedef.hedefNo }}
            </div>
            <div>
              <span class="font-medium">Hedef Tipi:</span> {{ hedef.hedefTip }}
            </div>
            <div>
              <span class="font-medium">Başlama Tarihi:</span> {{ hedef.baslamaTarihi }}
            </div>
            <div>
              <span class="font-medium">Süre Tipi:</span> {{ hedef.sureTipi }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <ng-template pTemplate="footer">
    <p-button
      label="Kapat"
      icon="pi pi-times"
      (onClick)="detayDialogKapat()"
      severity="secondary">
    </p-button>
  </ng-template>
</p-dialog>
