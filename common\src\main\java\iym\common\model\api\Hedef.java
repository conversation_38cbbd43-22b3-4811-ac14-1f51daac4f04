package iym.common.model.api;

import iym.common.enums.HedefTip;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
@ToString
@EqualsAndHashCode
public class Hedef {

    @NotNull
    private String hedefNo;

    @NotNull
    private HedefTip hedefTip;

}
