/**
 * IYM Backend OpenAPI definition
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface AidiyatGuncellemeDetay { 
    guncellemeTip: AidiyatGuncellemeDetay.GuncellemeTipEnum;
    aidiyatKodu: string;
}
export namespace AidiyatGuncellemeDetay {
    export const GuncellemeTipEnum = {
        Ekle: 'EKLE',
        Cikar: 'CIKAR'
    } as const;
    export type GuncellemeTipEnum = typeof GuncellemeTipEnum[keyof typeof GuncellemeTipEnum];
}


