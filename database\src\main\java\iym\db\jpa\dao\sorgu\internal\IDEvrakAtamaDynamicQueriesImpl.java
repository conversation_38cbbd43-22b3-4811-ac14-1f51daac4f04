package iym.db.jpa.dao.sorgu.internal;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;

import java.util.*;

public class IDEvrakAtamaDynamicQueriesImpl implements IDEvrakAtamaDynamicQueries {

    @PersistenceContext
    private EntityManager entityManager;

    /*
    * Asagidaki sorgulardaki MAHKEME_KARAR_ATAMA'daki mahkeme_karar_id ile MAHKEME_KARAR_TALEP'teki id ayi degildir
    * Bu yuzden ikisi arasinda join islemi yapilmiyor.
    * Bu sebepten dolayi evrak_id uzerinden join islemleri yapilmaktadir.
    * Eski sistemdeki bir evraka ait birden fazla mahmeke_karar_talebi bu tur islemlerde hataya sebep olmaktadir.
    * */

    private static final String ID_EVRAKATAMA_HISTORY_SORGU_BASESQL_STR = """
            SELECT
                ek.ID,
                ek.EVRAK_SIRA_NO,
                ek.EVRAK_NO,
                ek.ACILMI,
                ek.SORUSTURMA_NO ,
                ek.MAHKEME_KARAR_NO,
                k_gonderen.ID AS ATAYAN_ID,
                TRIM(NVL(k_gonderen.ADI, '') || ' ' || NVL(k_gonderen.SOYADI , '')) AS ATAYAN_ADI,
                k_gonderilen.ID AS ATANAN_ID,
                TRIM(NVL(k_gonderilen.ADI, '') || ' ' || NVL(k_gonderilen.SOYADI , '')) AS ATANAN_ADI
            FROM  iym.MAHKEME_KARAR_ATAMA mka
            INNER JOIN iym.EVRAK_KAYIT ek ON ek.ID = mka.EVRAK_ID
            INNER JOIN iym.KULLANICILAR k_gonderen ON k_gonderen.ID = mka.GONDEREN_ID
            INNER JOIN iym.KULLANICILAR k_gonderilen ON k_gonderilen.ID = mka.GONDERILEN_ID
          """;


    @Override
    public List<IdEvrakAtamaInfo> getEvrakAtamaHistory(Long evrakId){
        List<IdEvrakAtamaInfo> result = null;

        StringBuilder sql = new StringBuilder(ID_EVRAKATAMA_HISTORY_SORGU_BASESQL_STR);

        Map<String, Object> parameters = new HashMap<>();

        if (evrakId != null) {
            sql.append(" AND mka.EVRAK_ID = :evrakID");
            parameters.put("evrakID", evrakId);
        }

        Query query = entityManager.createNativeQuery(sql.toString(), "IdEvrakAtamaInfoMapping");

        parameters.forEach(query::setParameter);

        result = query.getResultList();

        if(result == null){
            result = new ArrayList<>();
        }
        return result;

    }





}
