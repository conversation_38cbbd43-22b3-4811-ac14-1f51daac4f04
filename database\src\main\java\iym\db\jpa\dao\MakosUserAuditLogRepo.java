package iym.db.jpa.dao;

import iym.common.enums.IletisimTespitiKararTuru;
import iym.common.enums.MakosUserAuditType;
import iym.common.model.entity.makos.MakosUserAuditLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Repository interface for MakosUserAuditLog entity
 * Provides data access methods for MAKOS user audit logging
 */
@Repository
public interface MakosUserAuditLogRepo extends JpaRepository<MakosUserAuditLog, Long>, JpaSpecificationExecutor<MakosUserAuditLog> {

    /**
     * Find audit logs by user audit type
     * @param userAuditType the type of audit operation
     * @return list of audit logs
     */
    List<MakosUserAuditLog> findByUserAuditType(MakosUserAuditType userAuditType);

    /**
     * Find audit logs by username
     * @param username the username to search for
     * @return list of audit logs
     */
    List<MakosUserAuditLog> findByUsername(String username);

    /**
     * Find audit logs by acting username
     * @param actingUsername the acting username to search for
     * @return list of audit logs
     */
    List<MakosUserAuditLog> findByActingUsername(String actingUsername);

    /**
     * Find audit logs by admin operated username
     * @param adminOperatedUsername the username that was operated on by admin
     * @return list of audit logs
     */
    List<MakosUserAuditLog> findByAdminOperatedUsername(String adminOperatedUsername);

    /**
     * Find audit logs by user IP
     * @param userIp the IP address to search for
     * @return list of audit logs
     */
    List<MakosUserAuditLog> findByUserIp(String userIp);

    /**
     * Find audit logs within a time range
     * @param startTime start of the time range
     * @param endTime end of the time range
     * @return list of audit logs
     */
    List<MakosUserAuditLog> findByRequestTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * Find audit logs by username and audit type
     * @param username the username to search for
     * @param userAuditType the type of audit operation
     * @return list of audit logs
     */
    List<MakosUserAuditLog> findByUsernameAndUserAuditType(String username, MakosUserAuditType userAuditType);

    /**
     * Find audit logs by username within a time range
     * @param username the username to search for
     * @param startTime start of the time range
     * @param endTime end of the time range
     * @return list of audit logs
     */
    List<MakosUserAuditLog> findByUsernameAndRequestTimeBetween(String username, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * Find audit logs ordered by request time descending
     * @return list of audit logs ordered by most recent first
     */
    List<MakosUserAuditLog> findAllByOrderByRequestTimeDesc();

    /**
     * Find audit logs by username ordered by request time descending
     * @param username the username to search for
     * @return list of audit logs ordered by most recent first
     */
    List<MakosUserAuditLog> findByUsernameOrderByRequestTimeDesc(String username);
}
