package iym.backend.makosclient.service;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import iym.makos.api.client.gen.model.MahkemeKararTalepSorguView;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

/**
 * Test class to verify LocalDateTime deserialization with raw timestamps
 * This test ensures that the Jackson configuration can handle raw timestamp numbers
 * that are returned by the MAKOS API instead of formatted date-time strings
 * 
 * This is now a pure unit test that doesn't depend on external services
 */
class MakosApiServiceLocalDateTimeTest {
    
    private ObjectMapper objectMapper;
    
    @BeforeEach
    void setUp() {
        // Create ObjectMapper with the same configuration as RestUtils
        objectMapper = new ObjectMapper();
        
        // Enable features for better deserialization
        objectMapper.enable(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY);
        objectMapper.enable(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT);
        
        // Create a custom module for LocalDateTime handling
        SimpleModule localDateTimeModule = new SimpleModule();
        
        // Custom deserializer that only accepts string values for LocalDateTime
        localDateTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(DateTimeFormatter.ISO_LOCAL_DATE_TIME) {
            @Override
            public LocalDateTime deserialize(com.fasterxml.jackson.core.JsonParser p, com.fasterxml.jackson.databind.DeserializationContext ctxt) throws java.io.IOException {
                if (p.getCurrentToken().isNumeric()) {
                    // Reject numeric values - only string deserialization is allowed
                    throw new java.io.IOException("LocalDateTime deserialization from numeric values is not allowed. Expected string format (ISO_LOCAL_DATE_TIME) but received numeric value: " + p.getText());
                } else {
                    // Handle formatted string
                    return super.deserialize(p, ctxt);
                }
            }
        });
        
        // Register the module
        objectMapper.registerModule(localDateTimeModule);
    }

    @Test
    void testLocalDateTimeDeserializationWithFormattedString() throws Exception {
        // Test JSON with formatted date-time string
        String jsonWithFormattedString = """
                {
                    "mahkemeKararTalepId": 1,
                    "evrakId": 100,
                    "evrakSiraNo": "2023-001",
                    "kurumEvrakNo": "E-2023-12345",
                    "kurumEvrakTarihi": "2023-05-15T10:30:00",
                    "kararKayitTarihi": "2023-05-15T10:30:00",
                    "mahkemeIlIlceKodu": "0600",
                    "mahkemeIlIlceAdi": "ANKARA",
                    "kaydedenKullaniciId": 200,
                    "kaydedenKullaniciAdi": "testuser",
                    "kaydedenAdiSoyadi": "Test User",
                    "sorusturmaNo": "2023/456",
                    "mahkemeKararNo": "2023/123",
                    "aciklama": "Test açıklama",
                    "durumu": "AKTIF"
                }
                """;

        // Deserialize the JSON
        MahkemeKararTalepSorguView result = objectMapper.readValue(jsonWithFormattedString, MahkemeKararTalepSorguView.class);

        // Verify the result
        assertThat(result).isNotNull();
        assertThat(result.getMahkemeKararTalepId()).isEqualTo(1L);
        assertThat(result.getEvrakId()).isEqualTo(100L);
        assertThat(result.getKararKayitTarihi()).isNotNull();

        // Verify that the formatted string was correctly converted to LocalDateTime
        LocalDateTime expectedDateTime = LocalDateTime.of(2023, 5, 15, 10, 30, 0);
        assertThat(result.getKararKayitTarihi()).isEqualTo(expectedDateTime);
    }

    @Test
    void testLocalDateTimeDeserializationWithRawTimestampShouldFail() {
        // Test JSON with raw timestamp (milliseconds since epoch)
        // 1684143000000 = 2023-05-15T10:30:00 in milliseconds
        String jsonWithRawTimestamp = """
                {
                    "mahkemeKararTalepId": 1,
                    "evrakId": 100,
                    "evrakSiraNo": "2023-001",
                    "kurumEvrakNo": "E-2023-12345",
                    "kurumEvrakTarihi": 1684143000000,
                    "kararKayitTarihi": 1684143000000,
                    "mahkemeIlIlceKodu": "0600",
                    "mahkemeIlIlceAdi": "ANKARA",
                    "kaydedenKullaniciId": 200,
                    "kaydedenKullaniciAdi": "testuser",
                    "kaydedenAdiSoyadi": "Test User",
                    "sorusturmaNo": "2023/456",
                    "mahkemeKararNo": "2023/123",
                    "aciklama": "Test açıklama",
                    "durumu": "AKTIF"
                }
                """;

        // Deserialize the JSON should throw an exception
        assertThatThrownBy(() -> {
            objectMapper.readValue(jsonWithRawTimestamp, MahkemeKararTalepSorguView.class);
        })
        .isInstanceOf(com.fasterxml.jackson.databind.JsonMappingException.class)
        .hasMessageContaining("LocalDateTime deserialization from numeric values is not allowed");
    }

    @Test
    void testLocalDateTimeDeserializationWithNullValue() throws Exception {
        // Test JSON with null date value
        String jsonWithNullDate = """
                {
                    "mahkemeKararTalepId": 1,
                    "evrakId": 100,
                    "evrakSiraNo": "2023-001",
                    "kurumEvrakNo": "E-2023-12345",
                    "kurumEvrakTarihi": null,
                    "kararKayitTarihi": null,
                    "mahkemeIlIlceKodu": "0600",
                    "mahkemeIlIlceAdi": "ANKARA",
                    "kaydedenKullaniciId": 200,
                    "kaydedenKullaniciAdi": "testuser",
                    "kaydedenAdiSoyadi": "Test User",
                    "sorusturmaNo": "2023/456",
                    "mahkemeKararNo": "2023/123",
                    "aciklama": "Test açıklama",
                    "durumu": "AKTIF"
                }
                """;

        // Deserialize the JSON
        MahkemeKararTalepSorguView result = objectMapper.readValue(jsonWithNullDate, MahkemeKararTalepSorguView.class);

        // Verify the result
        assertThat(result).isNotNull();
        assertThat(result.getMahkemeKararTalepId()).isEqualTo(1L);
        assertThat(result.getEvrakId()).isEqualTo(100L);
        assertThat(result.getKararKayitTarihi()).isNull();
        assertThat(result.getKurumEvrakTarihi()).isNull();
    }
}
