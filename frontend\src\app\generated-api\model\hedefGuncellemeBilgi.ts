/**
 * IYM Backend OpenAPI definition
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface HedefGuncellemeBilgi { 
    hedefGuncellemeAlan: HedefGuncellemeBilgi.HedefGuncellemeAlanEnum;
    yeniDegeri?: string;
}
export namespace HedefGuncellemeBilgi {
    export const HedefGuncellemeAlanEnum = {
        Ad: 'AD',
        Soyad: 'SOYAD',
        TckimlIkno: 'TCKIMlIKNO',
        CanakNo: 'CANAK_NO'
    } as const;
    export type HedefGuncellemeAlanEnum = typeof HedefGuncellemeAlanEnum[keyof typeof HedefGuncellemeAlanEnum];
}


