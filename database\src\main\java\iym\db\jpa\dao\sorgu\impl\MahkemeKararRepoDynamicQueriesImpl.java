package iym.db.jpa.dao.sorgu.impl;

import iym.common.model.entity.iym.mk.sorgu.MahkemeKararSorguInfo;
import iym.common.model.entity.iym.mk.sorgu.MahkemeKararSorguParam;
import iym.common.util.DateTimeUtils;
import iym.db.jpa.dao.sorgu.MahkemeKararRepoDynamicQueries;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MahkemeKararRepoDynamicQueriesImpl implements MahkemeKararRepoDynamicQueries {

    @PersistenceContext
    private EntityManager entityManager;


    private static final String MAHKEME_KARAR_SORGU_BASESQL_STR = """
                SELECT
                    mkt.ID,
                    mkt.SORUSTURMA_NO,
                    mkt.MAHKEME_KARAR_NO,
                    mkt.MAHKEME_KODU,
                    ma.MAHKEME_ADI,
                    mkt.DURUM,
                    mkt.ACIKLAMA,
                    mkt.KAYIT_TARIHI,
                    mkt.KULLANICI_ID,
                    kl.KULLANICI_ADI,
                    kl.ADI,
                    kl.SOYADI,
                    egk.KURUM_KOD,
                    egk.KURUM_ADI,
                    e.ID as EVRAK_ID,
                    e.EVRAK_SIRA_NO,
                    e.EVRAK_NO,
                    e.EVRAK_KONUSU
                FROM
                    iym.MAHKEME_KARAR mkt
                INNER JOIN iym.MAHKEME_ADI ma ON mkt.MAHKEME_KODU = ma.MAHKEME_KODU
                INNER JOIN iym.KULLANICILAR kl ON mkt.KULLANICI_ID = kl.ID
                INNER JOIN iym.EVRAK_KAYIT e ON mkt.EVRAK_ID = e.ID
                INNER JOIN iym.ILLER i ON mkt.MAHKEME_ILI = i.IL_KOD
                INNER JOIN iym.KULLANICI_KURUM kk ON mkt.KULLANICI_ID = kk.KULLANICI_ID
                INNER JOIN iym.EVRAK_GELEN_KURUMLAR egk ON kk.KURUM_KOD = egk.KURUM_KOD
                WHERE 1=1
            """;


    @Override
    public List<MahkemeKararSorguInfo> mahkemeKararSorgu(String kurumKodu, MahkemeKararSorguParam param) {

        StringBuilder sql = new StringBuilder(MAHKEME_KARAR_SORGU_BASESQL_STR);

        Map<String, Object> parameters = new HashMap<>();
        // Add kurumKodu filter for security - users should only see their institution's data
        if (kurumKodu != null && !kurumKodu.isEmpty()) {
            sql.append(" AND egk.KURUM_KOD = :kurumKodu");
            parameters.put("kurumKodu", kurumKodu);
        }

        if (param.getSorusturmaNo() != null && !param.getSorusturmaNo().isEmpty()) {
            sql.append(" AND mkt.SORUSTURMA_NO = :sorusturmaNo");
            parameters.put("sorusturmaNo", param.getSorusturmaNo());
        }

        if (param.getMahkemeKararNo() != null && !param.getMahkemeKararNo().isEmpty()) {
            sql.append(" AND mkt.MAHKEME_KARAR_NO = :mahkemeKararNo");
            parameters.put("mahkemeKararNo", param.getMahkemeKararNo());
        }

        if (param.getMahkemeKodu() != null && !param.getMahkemeKodu().isEmpty()) {
            sql.append(" AND mkt.MAHKEME_KODU = :mahkemeKodu");
            parameters.put("mahkemeKodu", param.getMahkemeKodu());
        }

        if (param.getDurum() != null && !param.getDurum().isEmpty()) {
            sql.append(" AND mkt.DURUM = :durum");
            parameters.put("durum", param.getDurum());
        }

        if (param.getAciklama() != null && !param.getAciklama().isEmpty()) {
            // Use UPPER() instead of LOWER() for better Turkish character handling
            // Oracle's UPPER() function handles Turkish characters (İ, Ğ, Ü, Ö, Ş, Ç) more reliably than LOWER()
            // This ensures proper case-insensitive search for Turkish text without character conversion issues
            // Note: CONCAT(CONCAT('%', :aciklama), '%') is used because Oracle CONCAT only accepts 2 parameters
            sql.append(" AND UPPER(mkt.ACIKLAMA) LIKE UPPER(CONCAT(CONCAT('%', :aciklama), '%'))");
            parameters.put("aciklama", param.getAciklama());
        }

        if (param.getKayitTarihi() != null) {
            LocalDateTime startOfDay = DateTimeUtils.truncateTime(param.getKayitTarihi());
            LocalDateTime nextDayStart = DateTimeUtils.addDays(startOfDay, 1);

            sql.append(" AND mkt.KAYIT_TARIHI >= :kayitTarihiStart");
            sql.append(" AND mkt.KAYIT_TARIHI < :kayitTarihiEnd");

            Timestamp kayitTarihiStart = Timestamp.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
            Timestamp kayitTarihiEnd = Timestamp.from(nextDayStart.atZone(ZoneId.systemDefault()).toInstant());
            parameters.put("kayitTarihiStart", kayitTarihiStart);
            parameters.put("kayitTarihiEnd", kayitTarihiEnd);
        }

        if (param.getEvrakSiraNo() != null && !param.getEvrakSiraNo().isEmpty()) {
            sql.append(" AND e.EVRAK_SIRA_NO = :evrakSiraNo");
            parameters.put("evrakSiraNo", param.getEvrakSiraNo());
        }

        Query query = entityManager.createNativeQuery(sql.toString(), "MahkemeKararSorguInfoMapping");

        parameters.forEach(query::setParameter);

        return query.getResultList();
    }

    @Override
    public List<MahkemeKararSorguInfo> islenecekKararListesi(String kurumKodu) {
        List<MahkemeKararSorguInfo> list = new ArrayList<>();

        return list;
    }
}
