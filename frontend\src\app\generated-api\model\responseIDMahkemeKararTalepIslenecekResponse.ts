/**
 * IYM Backend OpenAPI definition
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { IDMahkemeKararTalepIslenecekResponse } from './iDMahkemeKararTalepIslenecekResponse';
import { ResponseIDYeniKararResponseException } from './responseIDYeniKararResponseException';


export interface ResponseIDMahkemeKararTalepIslenecekResponse { 
    resultCode?: ResponseIDMahkemeKararTalepIslenecekResponse.ResultCodeEnum;
    resultDetails?: string;
    exception?: ResponseIDYeniKararResponseException;
    result?: IDMahkemeKararTalepIslenecekResponse;
    success?: boolean;
}
export namespace ResponseIDMahkemeKararTalepIslenecekResponse {
    export const ResultCodeEnum = {
        Success: 'SUCCESS',
        Failed: 'FAILED',
        Rejected: 'REJECTED'
    } as const;
    export type ResultCodeEnum = typeof ResultCodeEnum[keyof typeof ResultCodeEnum];
}


