package iym.db.jpa.service.impl.mkislem;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.mkislem.MahkemeAidiyatIslem;
import iym.common.service.db.mkislem.DbMahkemeAidiyatIslemService;
import iym.db.jpa.dao.mkislem.MahkemeAidiyatIslemRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Service implementation for MahkemeKararAidiyatIslem entity
 */
@Service
public class DbMahkemeAidiyatIslemServiceImpl extends GenericDbServiceImpl<MahkemeAidiyatIslem, Long> implements DbMahkemeAidiyatIslemService {

    private final MahkemeAidiyatIslemRepo mahkemeAidiyatIslemRepo;

    @Autowired
    public DbMahkemeAidiyatIslemServiceImpl(MahkemeAidiyatIslemRepo repo) {
        super(repo);
        this.mahkemeAidiyatIslemRepo = repo;
    }

    @Override
    @Transactional(readOnly = true)
    public List<MahkemeAidiyatIslem> findByMahkemeKararIslemId(Long mahkemeKararIslemId) {
        return mahkemeAidiyatIslemRepo.findByMahkemeKararIslemId(mahkemeKararIslemId);
    }



}
