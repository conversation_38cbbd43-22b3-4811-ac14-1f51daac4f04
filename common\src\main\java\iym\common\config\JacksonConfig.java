package iym.common.config;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import iym.common.util.LocalDateTimeDeserializationUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

/**
 * Global Jackson configuration for IYM modules
 * Ensures proper LocalDateTime serialization
 * Enforces write-dates-as-timestamps=false to prevent array serialization
 * Enforces string-only LocalDateTime deserialization to reject numeric timestamps
 */
@Configuration
@Slf4j
public class JacksonConfig {

    @Bean
    @Primary
    public ObjectMapper objectMapper(@Autowired(required = false) Jackson2ObjectMapperBuilder builder) {
        // If Jackson2ObjectMapperBuilder is not available (e.g., in test contexts),
        // create our own instance similar to JsonUtils.getMapper()
        if (builder == null) {
            log.debug("Jackson2ObjectMapperBuilder not available, creating custom ObjectMapper");
            return createCustomObjectMapper();
        }

        ObjectMapper objectMapper = builder
                .modules(new JavaTimeModule())
                .featuresToEnable(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES)
                .featuresToEnable(MapperFeature.ACCEPT_CASE_INSENSITIVE_ENUMS)
                .featuresToEnable(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY)
                .featuresToEnable(DeserializationFeature.ACCEPT_EMPTY_ARRAY_AS_NULL_OBJECT)
                .build();

        // Enforce these settings programmatically to prevent override from properties
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        objectMapper.disable(SerializationFeature.WRITE_DATES_WITH_CONTEXT_TIME_ZONE);
        objectMapper.disable(SerializationFeature.WRITE_DATES_WITH_ZONE_ID);

        // Disable timezone adjustment for date deserialization
        objectMapper.disable(DeserializationFeature.ADJUST_DATES_TO_CONTEXT_TIME_ZONE);
        objectMapper.disable(DeserializationFeature.READ_DATE_TIMESTAMPS_AS_NANOSECONDS);

        // Configure string-only LocalDateTime deserialization
        LocalDateTimeDeserializationUtils.configureObjectMapperForLocalDateTime(objectMapper);

        return objectMapper;
    }

    /**
     * Creates a custom ObjectMapper when Jackson2ObjectMapperBuilder is not available
     * This method replicates the configuration from JsonUtils.getMapper()
     */
    private ObjectMapper createCustomObjectMapper() {
        ObjectMapper objectMapper = new Jackson2ObjectMapperBuilder()
                .modules(new JavaTimeModule())
                .featuresToEnable(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES)
                .featuresToEnable(MapperFeature.ACCEPT_CASE_INSENSITIVE_ENUMS)
                .featuresToEnable(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY)
                .featuresToEnable(DeserializationFeature.ACCEPT_EMPTY_ARRAY_AS_NULL_OBJECT)
                .build();

        // Enforce these settings programmatically to prevent override from properties
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        objectMapper.disable(SerializationFeature.WRITE_DATES_WITH_CONTEXT_TIME_ZONE);
        objectMapper.disable(SerializationFeature.WRITE_DATES_WITH_ZONE_ID);

        // Disable timezone adjustment for date deserialization
        objectMapper.disable(DeserializationFeature.ADJUST_DATES_TO_CONTEXT_TIME_ZONE);
        objectMapper.disable(DeserializationFeature.READ_DATE_TIMESTAMPS_AS_NANOSECONDS);

        // Configure string-only LocalDateTime deserialization
        LocalDateTimeDeserializationUtils.configureObjectMapperForLocalDateTime(objectMapper);

        return objectMapper;
    }
}
