package gov.tib.util;

import gov.tib.KIMsabitler;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.xml.datatype.DatatypeConfigurationException;
import javax.xml.datatype.DatatypeFactory;
import javax.xml.datatype.XMLGregorianCalendar;

public class TemelIslemler {
	
	public static boolean bosMu(String s) {
		if (s != null && !s.trim().equals(""))
			return false;
		return true;

	}
	
	public static boolean isNullOrEmpty(String s) {
		if(s == null){
			return true;
		}
		
		return s.trim().equals("");
		
	}

	public static boolean bosMu(Long l) {

		if (l == null) {
			return false;

		} else if (l == 0)
			return true;
		else {
			return false;
		}
	}

	public static boolean bosMu(Date s) {
		if (s == null)
			return true;
		else {
			return false;
		}
	}

	public static String TurkZaman(Date d) {
		DateFormat trDate = new SimpleDateFormat("dd.MM.yyyy HH:mm:ss");
		return trDate.format(d);
	}

	public static String TurkZamanNullCheck(Date d) {
		if (d == null) {
			return "";
		} else {
			DateFormat trDate = new SimpleDateFormat("dd.MM.yyyy HH:mm:ss");

			return trDate.format(d);
		}

	}
	
	public static String TurkZamanNullCheck(XMLGregorianCalendar d) {
		if (d == null) {
			return "";
		} else {
			
			Date dt = TemelIslemler.toJavaUtilDate(d);
			
			DateFormat trDate = new SimpleDateFormat("dd.MM.yyyy HH:mm:ss");
			return trDate.format(dt);
		}
	}

	public static String TurkTarihNullCheck(Date d) {
		if (d == null) {
			return "";
		} else {
			DateFormat trDate = new SimpleDateFormat("dd.MM.yyyy");

			return trDate.format(d);
		}

	}
	
	public static String TurkTarihNullCheck(XMLGregorianCalendar d) {
		if (d == null) {
			return "";
		} else {
			Date dt = TemelIslemler.toJavaUtilDate(d);
			DateFormat trDate = new SimpleDateFormat("dd.MM.yyyy");
			return trDate.format(dt);
		}
	}
	
	
	public static Date toJavaUtilDate(XMLGregorianCalendar d) {
		if (d == null) {
			return null;
		} else {
			Date tarih = d.toGregorianCalendar().getTime();
			return tarih;
		}
	}
	
	//XMLGregorianCalendar
	public static XMLGregorianCalendar toXMLGregorianCalendar(Date d) {
		if (d == null) {
			return null;
		} else {
			DateFormat trDate = new SimpleDateFormat("dd.MM.yyyy HH:mm:ss");
			GregorianCalendar gc = new GregorianCalendar();
			gc.setTime(d);
			XMLGregorianCalendar tarih;
			try {
				tarih = DatatypeFactory.newInstance().newXMLGregorianCalendar(gc);
			} catch (DatatypeConfigurationException e) {
				e.printStackTrace();
				return null;
			}
			return tarih ;
		}
	}
	
	

	public static String mySQLZaman(Date d) {
		DateFormat trDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		return trDate.format(d);
	}

	public static String TableNameTarih(Date d) {
		DateFormat trDate = new SimpleDateFormat("yyyyMMdd");
		return trDate.format(d);
	}

	public static String TurkTarih(Date d) {
		DateFormat trDate = new SimpleDateFormat("dd.MM.yyyy");
		return trDate.format(d);
	}

	public static Date ParseZaman(String tarih) {

		Date donusTarih = null;
		DateFormat trDate1 = new SimpleDateFormat("dd.MM.yyyy HH:mm:ss");
		DateFormat trDate2 = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
		try {
			if(tarih.indexOf(".")>=0)
				donusTarih = trDate1.parse(tarih);
			else if(tarih.indexOf("/")>=0)
				donusTarih = trDate2.parse(tarih);
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return donusTarih;
	}
	
	public static Date ParseTarih(String tarih) {

		Date donusTarih = null;
		DateFormat trDate1 = new SimpleDateFormat("dd.MM.yyyy");
		DateFormat trDate2 = new SimpleDateFormat("dd/MM/yyyy");
		try {
			if(tarih.indexOf(".")>=0)
				donusTarih = trDate1.parse(tarih);
			else if(tarih.indexOf("/")>=0)
				donusTarih = trDate2.parse(tarih);
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return donusTarih;
	}


	public static Date TarihtenGunEksilt(Date d, Long gun) {
		DateFormat trDate = new SimpleDateFormat("dd.MM.yyyy");
		trDate.format(d);

		Long zaman = d.getTime();
		zaman = zaman - gun * 24 * 60 * 60 * 1000;
		Date tarih = new Date();
		tarih.setTime(zaman);
		return tarih;
	}

	public static Date TarihtenAyEksilt(Date d, Integer ay) {
		Calendar cd = Calendar.getInstance();
		cd.setTime(d);
		cd.add(Calendar.MONTH, -ay);
		return cd.getTime();
	}
	
	public static Date TariheAyEkle(Date d, Integer ay) {
		Calendar cd = Calendar.getInstance();
		cd.setTime(d);
		cd.add(Calendar.MONTH, ay);
		return cd.getTime();
	}

	public static Date TariheGunEkle(Date d, Integer gun) {
		Calendar cd = Calendar.getInstance();
		cd.setTime(d);
		cd.add(Calendar.DAY_OF_MONTH, gun);
		return cd.getTime();
	}

	public static Date TarihtenGunCikar(Date d, Integer gun) {
		Calendar cd = Calendar.getInstance();
		cd.setTime(d);
		cd.add(Calendar.DAY_OF_MONTH, -gun);

		return cd.getTime();
	}

	public static Date TarihtenSaatCikar(Date d, Integer saat) {
		Calendar cd = Calendar.getInstance();
		cd.setTime(d);
		cd.add(Calendar.HOUR_OF_DAY, -saat);

		return cd.getTime();
	}

	public static long TarihGunFark(Date date1, Date date2) {
		Calendar cdate1 = Calendar.getInstance();
		cdate1.setTime(date1);

		Calendar cdate2 = Calendar.getInstance();
		cdate2.setTime(date2);

//		System.out.println(cdate1.getTimeInMillis());
//		System.out.println(cdate2.getTimeInMillis());
		long delta = (cdate1.getTimeInMillis() - cdate2.getTimeInMillis());

		return delta;

	}

	/**
	 * @deprecated
	 */
	public static Date TarihtenGunEksilt(Date d, int gun) {
		Long zaman = d.getTime();
		zaman = zaman - gun * 24 * 60 * 60 * 1000;
		Date tarih = new Date();
		tarih.setTime(zaman);
		return tarih;
	}

	public static Date TarihtenSaatEksilt(Date d, int saat) {
		Long zaman = d.getTime();
		zaman = zaman - saat * 60 * 60 * 1000;
		Date tarih = new Date();
		tarih.setTime(zaman);
		return tarih;
	}

	public static Date tariheGunEkle(Date d, int gun) {
		Long zaman = d.getTime();
		zaman = zaman + gun * 60 * 24 * 60 * 1000;
		Date tarih = new Date();
		tarih.setTime(zaman);
		return tarih;
	}

	public static Date ZamandanZamanEksilt(Date d1, Date d2) {
		Long zaman = d1.getTime() - d2.getTime();
		return new Date(zaman);

	}

	public static int[] tib24Saat() {
		int[] saat = null;
		for (int i = 0; i < 24; i++) {
			saat[i] = i;
		}
		return saat;
	}

	public static int[] tib60Dakika() {
		int[] dakika = null;
		for (int i = 0; i < 60; i++) {
			dakika[i] = i;
		}
		return dakika;
	}

	public static int bool2Int(boolean girdi) {
		if (girdi)
			return 1;
		else
			return 0;

	}

	public static boolean int2Bool(int girdi) {
		if (girdi > 0)
			return true;
		else
			return false;
	}

	public static Date TariheZamanUla(Date d, Date t) {
		DateFormat trDate = new SimpleDateFormat("dd.MM.yyyy");
		DateFormat trTime = new SimpleDateFormat("HH:mm");
		DateFormat trDateTime = new SimpleDateFormat("dd.MM.yyyy HH:mm");
		String tarih1 = trDate.format(d) + " " + trTime.format(t);

		try {
			return trDateTime.parse(tarih1);
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			return null;
		}
	}
	
	
	public static Date BugunTarihiGetir() {
		Date d = new Date();
		DateFormat trDate = new SimpleDateFormat("dd.MM.yyyy");
		String tarih = trDate.format(d);

		try {
			return trDate.parse(tarih);
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			return null;
		}
	}

	public static String long2Kronometre(int sure) {

		String saniye = String.valueOf((sure % 60));
		if (saniye.length() < 2)
			saniye = "0" + saniye;

		String dakika = String.valueOf((sure / 60) % 60);
		if (dakika.length() < 2)
			dakika = "0" + dakika;
		String saat = String.valueOf((sure / (60 * 60)) % 24);
		if (saat.length() < 2)
			saat = "0" + saat;

		String kronometre = saat + ":" + dakika + ":" + saniye;
		return kronometre;

	}

	public static String doksanAt(String telNo) {
		if (telNo.startsWith("90"))
			return telNo.substring(2, telNo.length());
		return telNo;
	}

	public static boolean mailMi(String mail) {
		return patternKontrol(
				"^[_a-zA-Z0-9-]+(\\.[_a-zA-Z0-9-]+)*@[a-zA-Z0-9-]+(\\.[a-zA-Z0-9-]+)*\\.(([0-9]{1,3})|([a-zA-Z]{2,3})|(aero|coop|info|museum|name))$",
				mail);
	}

	public static boolean ipMi(String ip) {
		return patternKontrol(
				"\\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\b",
				ip);
	}

	public static boolean patternKontrol(String regEx, String val) {
		Pattern pattern = Pattern.compile(regEx);
		Matcher matcher = pattern.matcher(val);

		return matcher.matches();
	}

	public static String nullTemizle(String girdi) {

		if (girdi == null)
			return "";
		else
			return girdi;
	}

	public static String objeNullTemizle(Object girdi) {

		if (girdi == null)
			return "";
		else
			return girdi.toString();
	}

	public static String stringNullChange(Object girdi) {

		if (girdi == null)
			return "-";
		else
			return girdi.toString();
	}

	public static String tarihFormatla(Date tarih, String format) {
		try {
			SimpleDateFormat dateFormatter = new SimpleDateFormat(format);
			return dateFormatter.format(tarih);
		} catch (Exception e) {
			return "";
		}

	}

	public static String aramaTipi(Integer tip) {
		String aramaTipi = "";
		if (tip == null)
			return aramaTipi;
		switch (tip) {
		case 3:
			aramaTipi = "Acil Arama";
			break;
		case 10:
			aramaTipi = "Aradı";
			break;
		case 11:
			aramaTipi = "Ödemeli Aradı";
			break;
		case 30:
			aramaTipi = "Arandı";

			break;
		case 90:
			aramaTipi = "Mesaj attı";
			break;
		case 80:
			aramaTipi = "Mesaj aldı";
			break;
		case 50:
			aramaTipi = "MMS attı";
			break;
		case 53:
			aramaTipi = "MMS İletişimi";
			break;
		case 55:
			aramaTipi = "MMS aldı";
			break;

		case 40:
			aramaTipi = "Arandı Yönlendirme";
			break;
		case 25:
			aramaTipi = "Roaming aradı";
			break;
		case 35:
			aramaTipi = "Roaming arandı";
			break;
		case 26:
			aramaTipi = "Roaming mesaj attı";
			break;

		case 36:
			aramaTipi = "Roaming mesaj aldı";
			break;
		case 60:
			aramaTipi = "Wap";
			break;
		case 70:
			aramaTipi = "Gprs";
			break;

		default:
			aramaTipi = "";
			break;
		}
		return aramaTipi;
	}

	public static String aramaTipSwitch(Integer tip) {
		String aramaTipi = "";
		if (tip == null)
			return aramaTipi;
		switch (tip) {
		case 3:
			aramaTipi = "Acil Arama";
			break;
		case 10:
			aramaTipi = "Arama";
			break;
		case 11:
			aramaTipi = "Ödemeli Arama";
			break;
		case 30:
			aramaTipi = "Aranma";

			break;
		case 90:
			aramaTipi = "Mesaj atma";
			break;
		case 80:
			aramaTipi = "Mesaj alma";
			break;

		case 50:
			aramaTipi = "MMS Mesaj alma";
			break;
		case 55:
			aramaTipi = "MMS Mesaj alma";
			break;
		case 53:
			aramaTipi = "MMS İletişimi";
			break;

		case 40:
			aramaTipi = "Yönlendirme";
			break;
		case 25:
			aramaTipi = "Roam. arama";
			break;
		case 35:
			aramaTipi = "Roam. aranma";
			break;
		case 26:
			aramaTipi = "Roam. mesaj atma";
			break;

		case 36:
			aramaTipi = "Roam. mesaj alma";
			break;
		case 60:
			aramaTipi = "Wap";
			break;
		case 70:
			aramaTipi = "Gprs";
			break;

		default:
			aramaTipi = "";
			break;
		}
		return aramaTipi;
	}

	public static Integer switchTip(Integer tip) {
		Integer aramaTipi;
		// if (tip == null)
		// return aramaTipi;
		switch (tip) {
		case 3:
			aramaTipi = KIMsabitler.ARANMA;
			break;
		case 10:
			aramaTipi = KIMsabitler.ARANMA;
			break;
		case 11:
			aramaTipi = KIMsabitler.ARANMA;
			break;
		case 30:
			aramaTipi = KIMsabitler.ARAMA;

			break;
		case 90:
			aramaTipi = KIMsabitler.MESAJ_ALMA;
			break;
		case 80:
			aramaTipi = KIMsabitler.MESAJ_ATMA;
			break;
		case 40:
			aramaTipi = KIMsabitler.YONLEDIRME;
			break;

		case 50:
			aramaTipi = KIMsabitler.MMS_MESAJ_ALMA;
			break;
		case 55:
			aramaTipi = KIMsabitler.MMS_MESAJ_ATMA;
			break;
		case 53:
			aramaTipi = KIMsabitler.MMS_ILETISIMI;
			break;

		case 25:
			aramaTipi = KIMsabitler.ROAMING_ARANMA;
			break;
		case 35:
			aramaTipi = KIMsabitler.ROAMING_ARAMA;
			break;
		case 26:
			aramaTipi = KIMsabitler.ROAMING_MESAJ_ALMA;
			break;

		case 36:
			aramaTipi = KIMsabitler.ROAMING_MESAJ_ATMA;
			break;
		case 60:
			aramaTipi = null;
			break;
		case 70:
			aramaTipi = null;
			break;

		default:
			aramaTipi = null;
			break;
		}
		return aramaTipi;
	}

	public static ArrayList<Object> karsiTelSorguTipChange(
			ArrayList<Object> aramaSecenekleri) {
		ArrayList<Object> karsiAramasecenekleri = new ArrayList<Object>();
		for (int i = 0; i < aramaSecenekleri.size(); i++) {
			Integer tip = switchTip((Integer) aramaSecenekleri.get(i));
			if (tip != null) {
				karsiAramasecenekleri.add(tip);
				if ((Integer) aramaSecenekleri.get(i) == KIMsabitler.ARANMA) {
					karsiAramasecenekleri.add(KIMsabitler.ARAMA_ACIL);
					karsiAramasecenekleri.add(KIMsabitler.ODEMELI_ARAMA);
				}

			}
		}
		return karsiAramasecenekleri;
	}

	public static String bazTabloAdSwitch(Integer tip) {
		String aramaTipi = "";
		if (tip == null)
			return aramaTipi;
		switch (tip) {
		case 3:
			aramaTipi = "ACİL ARAMA YAPANLAR";
			break;
		case 10:
			aramaTipi = "ARAMA YAPANLAR";
			break;
		case 11:
			aramaTipi = "ÖDEMELİ ARAMA YAPANLAR";
			break;
		case 30:
			aramaTipi = "ARANANLAR";

			break;
		case 90:
			aramaTipi = "MESAJ ATANLAR";
			break;
		case 80:
			aramaTipi = "MESAJ ALANLAR";
			break;
		case 40:
			aramaTipi = "YÖNLENDİRME";
			break;
		case 25:
			aramaTipi = "ROAMİNG ARAMA YAPANLAR";
			break;
		case 35:
			aramaTipi = "ROAMİNG ARANANLAR";
			break;
		case 26:
			aramaTipi = "ROAMİNG MESAJ ATANLAR";
			break;

		case 36:
			aramaTipi = "ROAMİNG MESAJ ALANLAR";
			break;
		case 60:
			aramaTipi = "WAP İLETİŞİMİ YAPANLAR";
			break;
		case 70:
			aramaTipi = "GPRS İLETİŞİMİ YAPANLAR";
			break;

		default:
			aramaTipi = " - ";
			break;
		}
		return aramaTipi;
	}

	public static String operatorAdSwitch(String kodOperator) {
		if (kodOperator == null)
			return "";

		int operatorKod = Integer.parseInt(kodOperator);
		String operator = "";

		switch (operatorKod) {
		case 1:
			operator = "Turkcell";
			break;
		case 2:
			operator = "Vodafone";
			break;
		case 3:
			operator = "Avea";
			break;
		}
		return operator;
	}

	public static String TableNameTarihYilAy(Date d) {
		DateFormat trDate = new SimpleDateFormat("yyyyMM");
		return trDate.format(d);
	}

	public static String islemZulSayfaSwitch(Integer tip) {
		String aramaTipi = "";
		if (tip == null)
			return aramaTipi;
		switch (tip) {
		case 999:
			aramaTipi = "N_AboneSorgu.zul";
			break;
		case 100:
			aramaTipi = "N_TelefonGorusme.zul";
			break;
		case 110:
			aramaTipi = "N_TelefonGorusmeSadeceGPRS.zul";
			break;

		case 200:
			aramaTipi = "N_ImeiGorusme.zul";
			break;
		case 210:
			aramaTipi = "N_ImeiGorusmeSadeceGPRS.zul";
			break;

		case 300:
			aramaTipi = "N_UluslararasiGorusme.zul";
			break;
		case 400:
			aramaTipi = "N_BazGorusme.zul";
			break;

		case 510:
			aramaTipi = "N_TelImeiKullanma.zul";
			break;

		case 520:
			aramaTipi = "N_NumaraKullananImei.zul";
			break;

		case 530:
			aramaTipi = "N_ImeiKullananNumara.zul";
			break;
		// VODAFONE_4040_SAYFASI = 610;
		case 610:
			aramaTipi = "N_Vodafone4040Gorusme.zul";
			break;

		// TKART_TELEFON_SAYFASI = 620;
		case 620:
			aramaTipi = "N_TkartGorusme.zul";
			break;
		// TKART_KART_SAYFASI = 630;
		case 630:
			aramaTipi = "";
			break;

		default:
			aramaTipi = "";
			break;
		}
		return aramaTipi;
	}

	public static String islemAdSwitch(Integer tip) {
		String aramaTipi = "";
		if (tip == null)
			return aramaTipi;
		switch (tip) {
		case 999:
			aramaTipi = "Abone Sorgusu";
			break;
		case 100:
			aramaTipi = "Telefon Görüşme";
			break;
		case 110:
			aramaTipi = "Sadece GPRS (Telefon)";
			break;

		case 200:
			aramaTipi = "Imei Görüşme";
			break;
		case 210:
			aramaTipi = "Sadece GPRS (Imei)";
			break;

		case 300:
			aramaTipi = "Uluslararası Görüşme";
			break;
		case 400:
			aramaTipi = "Baz Görüşme";
			break;

		case 510:
			aramaTipi = "Tel No/Imei Kullanma";
			break;

		case 520:
			aramaTipi = "Numarayı Kullanan Imei";
			break;

		case 530:
			aramaTipi = "Imei Kullanan Numara";
			break;
		// VODAFONE_4040_SAYFASI = 610;
		case 610:
			aramaTipi = "Vodafone 4040 Görüşme Sorgusu";
			break;

		// TKART_TELEFON_SAYFASI = 620;
		case 620:
			aramaTipi = "Tkart Görüşme Sorgusu";
			break;
		// TKART_KART_SAYFASI = 630;
		case 630:
			aramaTipi = "";
			break;

		default:
			aramaTipi = "";
			break;
		}
		return aramaTipi;
	}

	public static String aramaImgSwitch(Integer tip) {
		String aramaTipi = "";
		if (tip == null)
			return aramaTipi;
		switch (tip) {
		case 3:
			aramaTipi = "/images/img/icon/AcilArama.png";
			break;
		case 10:
			aramaTipi = "/images/img/icon/Arama.png";
			break;
		case 11:
			aramaTipi = "/images/img/icon/OdemeliArama.png";
			break;
		case 30:
			aramaTipi = "/images/img/icon/Aranma.png";

			break;
		case 90:
			aramaTipi = "/images/img/icon/smsatma2.png";
			break;
		case 80:
			aramaTipi = "/images/img/icon/smsalma2.png";
			break;
		case 40:
			aramaTipi = "/images/img/icon/yonlendirme.png";
			break;
		case 25:
			aramaTipi = "/images/img/icon/roamingarama1.png";
			break;
		case 35:
			aramaTipi = "/images/img/icon/roamingarama2.png";
			break;
		case 26:
			aramaTipi = "/images/img/icon/roamingsmsatma.png";
			break;

		case 36:
			aramaTipi = "/images/img/icon/roamingsmsalma.png";
			break;
		case 60:
			aramaTipi = "/images/img/icon/wap.png";
			break;
		case 70:
			aramaTipi = "/images/img/icon/gprs.png";
			break;

		default:
			aramaTipi = "";
			break;
		}
		return aramaTipi;
	}

	public static String sonucImgSwitch(String sonucTip) {
		String imgSrc = "";
		if (sonucTip.equalsIgnoreCase(KIMsabitler.SURE)) {
			imgSrc = "/images/img/icon/time.png";
			return imgSrc;
		}
		if (sonucTip.equalsIgnoreCase(KIMsabitler.IMEI)) {
			imgSrc = "/images/img/icon/imeiSonuc.png";
			return imgSrc;
		}
		if (sonucTip.equalsIgnoreCase(KIMsabitler.IMSI)) {
			imgSrc = "/images/img/icon/imsiSonuc.png";
			return imgSrc;
		}
		if (sonucTip.equalsIgnoreCase(KIMsabitler.BAZ_ISTASYONU)) {
			imgSrc = "/images/img/icon/bazSonuc.png";
			return imgSrc;
		}
		if (sonucTip.equalsIgnoreCase(KIMsabitler.TRUNK)) {
			imgSrc = "/images/img/icon/trunkSonuc.png";
			return imgSrc;
		}
		if (sonucTip.equalsIgnoreCase(KIMsabitler.ISIM_SOYISIM)) {
			imgSrc = "/images/img/icon/isimsoyisim.png";
			return imgSrc;
		}
		if (sonucTip.equalsIgnoreCase(KIMsabitler.ACIK_KIMLIK)) {
			imgSrc = "/images/img/icon/acikkimlik.png";
			return imgSrc;
		}
		if (sonucTip.equalsIgnoreCase(KIMsabitler.DIGER_ISIM_SOYISIM)) {
			imgSrc = "/images/img/icon/isimsoyisim.png";
			return imgSrc;
		}
		if (sonucTip.equalsIgnoreCase(KIMsabitler.DIGER_ACIK_KIMLIK)) {
			imgSrc = "/images/img/icon/acikkimlik.png";
			return imgSrc;
		}
		if (sonucTip.equalsIgnoreCase(KIMsabitler.DIGER_ADRES)) {
			imgSrc = "/images/img/icon/digeradres.png";
			return imgSrc;
		}

		if (sonucTip.equalsIgnoreCase(KIMsabitler.HEDEF_ISIM_SOYISIM)) {
			imgSrc = "/images/img/icon/hedefadsoyad.png";
			return imgSrc;
		}
		if (sonucTip.equalsIgnoreCase(KIMsabitler.HEDEF_ACIK_KIMLIK)) {
			imgSrc = "/images/img/icon/hedefacikkimlik.png";
			return imgSrc;
		}
		if (sonucTip.equalsIgnoreCase(KIMsabitler.HEDEF_ADRES)) {
			imgSrc = "/images/img/icon/hedefadres.png";
			return imgSrc;
		}
		if (sonucTip.equalsIgnoreCase(KIMsabitler.INTRUNK)) {
			imgSrc = "/images/img/icon/intrunk.png";
			return imgSrc;
		}
		if (sonucTip.equalsIgnoreCase(KIMsabitler.OUTTRUNK)) {
			imgSrc = "/images/img/icon/outtrunk.png";
			return imgSrc;
		}
		if (sonucTip.equalsIgnoreCase(KIMsabitler.MIDTRUNK)) {
			imgSrc = "/images/img/icon/midtrunk.png";
			return imgSrc;
		}
		if (sonucTip.equalsIgnoreCase(KIMsabitler.HARCANAN_KONTOR)) {
			imgSrc = "/images/img/icon/harcanankontor.png";
			return imgSrc;
		}
		if (sonucTip.equalsIgnoreCase(KIMsabitler.KALAN_KONTOR)) {
			imgSrc = "/images/img/icon/kalankontor.png";
			return imgSrc;
		}
		if (sonucTip.equalsIgnoreCase(KIMsabitler.ANKESOR_ADRES)) {
			imgSrc = "/images/img/icon/ankesoradres.png";
			return imgSrc;
		}
		if (sonucTip.equalsIgnoreCase(KIMsabitler.KART_NO)) {
			imgSrc = "/images/img/icon/kartno.png";
			return imgSrc;
		}
		if (sonucTip.equalsIgnoreCase(KIMsabitler.ABONE_BASLANGIC_TARIHI)) {
			imgSrc = "/images/img/icon/abonebaslamatarih.png";
			return imgSrc;
		}
		return imgSrc;

	}

	public static String sonucTipSwitch(String sonucTip) {
		String sonuctip = "";
		if (sonucTip.equalsIgnoreCase(KIMsabitler.SURE)) {
			sonuctip = "Süre";
			return sonuctip;
		}
		if (sonucTip.equalsIgnoreCase(KIMsabitler.IMEI)) {
			sonuctip = "Imei";
			return sonuctip;
		}
		if (sonucTip.equalsIgnoreCase(KIMsabitler.IMSI)) {
			sonuctip = "Imsi";
			return sonuctip;
		}
		if (sonucTip.equalsIgnoreCase(KIMsabitler.BAZ_ISTASYONU)) {
			sonuctip = "Baz İstasyonu";
			return sonuctip;
		}
		if (sonucTip.equalsIgnoreCase(KIMsabitler.TRUNK)) {
			sonuctip = "Trunk";
			return sonuctip;
		}
		if (sonucTip.equalsIgnoreCase(KIMsabitler.ISIM_SOYISIM)) {
			sonuctip = "İsim Soyisim";
			return sonuctip;
		}
		if (sonucTip.equalsIgnoreCase(KIMsabitler.ACIK_KIMLIK)) {
			sonuctip = "Açık Kimlik";
			return sonuctip;
		}
		if (sonucTip.equalsIgnoreCase(KIMsabitler.DIGER_ISIM_SOYISIM)) {
			sonuctip = "Diğer İsim Soyisim";
			return sonuctip;
		}
		if (sonucTip.equalsIgnoreCase(KIMsabitler.DIGER_ACIK_KIMLIK)) {
			sonuctip = "Diğer Açık Kimlik";
			return sonuctip;
		}
		if (sonucTip.equalsIgnoreCase(KIMsabitler.DIGER_ADRES)) {
			sonuctip = "Diğer Adres";
			return sonuctip;
		}
		if (sonucTip.equalsIgnoreCase(KIMsabitler.HEDEF_ISIM_SOYISIM)) {
			sonuctip = "Hedef İsim Soyisim";
			return sonuctip;
		}
		if (sonucTip.equalsIgnoreCase(KIMsabitler.HEDEF_ACIK_KIMLIK)) {
			sonuctip = "Hedef Açık Kimlik";
			return sonuctip;
		}
		if (sonucTip.equalsIgnoreCase(KIMsabitler.HEDEF_ADRES)) {
			sonuctip = "Hedef Adres";
			return sonuctip;
		}
		if (sonucTip.equalsIgnoreCase(KIMsabitler.INTRUNK)) {
			sonuctip = "Intrunk";
			return sonuctip;
		}
		if (sonucTip.equalsIgnoreCase(KIMsabitler.OUTTRUNK)) {
			sonuctip = "Outtrunk";
			return sonuctip;
		}
		if (sonucTip.equalsIgnoreCase(KIMsabitler.MIDTRUNK)) {
			sonuctip = "Midtrunk";
			return sonuctip;
		}
		if (sonucTip.equalsIgnoreCase(KIMsabitler.HARCANAN_KONTOR)) {
			sonuctip = "Harcanan Kontör";
			return sonuctip;
		}
		if (sonucTip.equalsIgnoreCase(KIMsabitler.KALAN_KONTOR)) {
			sonuctip = "Kalan Kontör";
			return sonuctip;
		}
		if (sonucTip.equalsIgnoreCase(KIMsabitler.ANKESOR_ADRES)) {
			sonuctip = "Ankesör Adres";
			return sonuctip;
		}
		if (sonucTip.equalsIgnoreCase(KIMsabitler.KART_NO)) {
			sonuctip = "Kart No";
			return sonuctip;
		}
		if (sonucTip.equalsIgnoreCase(KIMsabitler.ABONE_BASLANGIC_TARIHI)) {
			sonuctip = "Abone Başlama Tarihi";
			return sonuctip;
		}
		return sonuctip;
	}

	public static String islemImgSwitch(Integer tip) {
		String aramaTipi = "";
		if (tip == null)
			return aramaTipi;
		switch (tip) {
		case 999:
			aramaTipi = "/images/img/icon/abonesorguicon.png";
			break;
		case 100:
			aramaTipi = "/images/img/icon/telefon.png";
			break;
		case 110:
			aramaTipi = "S_TelefonGorusmeSadeceGPRS.zul";
			break;
		case 200:
			aramaTipi = "/images/img/icon/imeiicon.png";
			break;
		case 210:
			aramaTipi = "S_ImeiGorusmeSadeceGPRS.zul";
			break;
		case 300:
			aramaTipi = "/images/img/icon/uluslararasiicon.png";
			break;
		case 400:
			aramaTipi = "/images/img/icon/bazicon.png";
			break;
		case 510:
			aramaTipi = "/images/img/icon/telnoimeiicon.png";
			break;
		case 520:
			aramaTipi = "/images/img/icon/telimeiicon.png";
			break;
		case 530:
			aramaTipi = "/images/img/icon/imeitelicon.png";
			break;
		// VODAFONE_4040_SAYFASI = 610;
		case 610:
			aramaTipi = "/images/img/icon/vodafoneicon3.png";
			break;

		// TKART_TELEFON_SAYFASI = 620;
		case 620:
			aramaTipi = "/images/img/icon/ttkarticon.png";
			break;
		// TKART_KART_SAYFASI = 630;
		case 630:
			aramaTipi = "";
			break;

		default:
			aramaTipi = "";
			break;
		}
		return aramaTipi;
	}

	public static boolean kimTarihCheck(Date tarih) {
		int deger = tarih.compareTo(new Date());
		if (deger == 0 || deger < 0) {
			return false;
		}
		return true;

	}
	
	public static boolean tarihSinirCheck(Date basTar, Date bitTar, Date minDate,
			Date maxDate) {

		int baslama1 = minDate.compareTo(basTar);
		int bitis1 = maxDate.compareTo(basTar);

		int baslama2 = minDate.compareTo(bitTar);
		int bitis2 = maxDate.compareTo(bitTar);

		if (baslama1 <= 0 && bitis1 >= 0 && baslama2 <= 0 && bitis2 >= 0) {
			return true;
		}

		return false;
	}

	public static String numaraFormatla(String input) {
		String sonuc = "";

		if (input == null) {
			return "";
		}

		if (input.length() > 15) {
			sonuc = input.substring(0, 15);
		}

		if (input != null) {
			switch (input.length()) {

			case 10: // "5337376383"
				sonuc = input.substring(0, 3) + " " + input.substring(3, 6)
						+ " " + input.substring(6, 8) + " "
						+ input.substring(8, 10);
				break;
			case 12: // "905337376383"
				sonuc = input.substring(0, 2) + " " + input.substring(2, 5)
						+ " " + input.substring(5, 8) + " "
						+ input.substring(8, 10) + " "
						+ input.substring(10, 12);
				break;
			// case 14: //
			// sonuc = input.substring(0, 6) + " " + input.substring(6, 8)
			// + " " + input.substring(8, 14);
			// break;
			// case 15://
			// sonuc = input.substring(0, 6) + " " + input.substring(6, 8)
			// + " " + input.substring(8, 14) + " "
			// + input.substring(14, 15);
			// break;

			case 14: //
				sonuc = input.substring(0, 3) + " " + input.substring(3, 6)
						+ " " + input.substring(6, 9) + " "
						+ input.substring(9, 12) + " "
						+ input.substring(12, 14);
				break;
			case 15:// 
				sonuc = input.substring(0, 3) + " " + input.substring(3, 6)
						+ " " + input.substring(6, 9) + " "
						+ input.substring(9, 12) + " "
						+ input.substring(12, 15);
				break;

			default:
				sonuc = input;
			}

		}

		return sonuc;
	}

}
