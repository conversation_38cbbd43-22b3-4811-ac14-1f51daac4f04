CREATE TABLE ulkeler
(
    id         BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    is_deleted BO<PERSON><PERSON><PERSON>,
    created_at TIMESTAMP WITHOUT TIME ZONE,
    updated_at TIMESTAMP WITHOUT TIME ZONE,
    deleted_at TIMESTAMP WITHOUT TIME ZONE,
    created_by <PERSON><PERSON><PERSON><PERSON>(255),
    updated_by VARCHAR(255),
    deleted_by VARCHAR(255),
    name       <PERSON><PERSON><PERSON><PERSON>(255),
    code       VA<PERSON><PERSON><PERSON>(255),
    CONSTRAINT pk_ulkeler PRIMARY KEY (id)
);