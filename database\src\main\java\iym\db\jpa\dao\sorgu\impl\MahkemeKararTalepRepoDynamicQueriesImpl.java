package iym.db.jpa.dao.sorgu.impl;

import iym.common.enums.MakosUserAuditType;
import iym.common.model.entity.iym.mk.sorgu.MahkemeKararTalepSorguInfo;
import iym.common.model.entity.iym.mk.sorgu.MahkemeKararTalepSorguParam;
import iym.common.util.DateTimeUtils;
import iym.db.jpa.dao.sorgu.MahkemeKararTalepRepoDynamicQueries;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MahkemeKararTalepRepoDynamicQueriesImpl implements MahkemeKararTalepRepoDynamicQueries {

    @PersistenceContext
    private EntityManager entityManager;


    private static final String MAHKEME_KARAR_TALEP_SORGU_BASESQL_STR = """
                SELECT
                    mkt.ID,
                    mkt.SORUSTURMA_NO,
                    mkt.MAHKEME_KARAR_NO,
                    mkt.MAHKEME_KODU,
                    ma.MAHKEME_ADI,
                    mkt.DURUM,
                    mkt.ACIKLAMA,
                    mkt.KAYIT_TARIHI,
                    mkt.KULLANICI_ID,
                    kl.KULLANICI_ADI,
                    kl.ADI,
                    kl.SOYADI,
                    egk.KURUM_KOD,
                    egk.KURUM_ADI,
                    e.ID as EVRAK_ID,
                    e.EVRAK_SIRA_NO,
                    e.EVRAK_NO,
                    e.EVRAK_KONUSU
                FROM
                    iym.MAHKEME_KARAR_TALEP mkt
                INNER JOIN iym.MAHKEME_ADI ma ON mkt.MAHKEME_KODU = ma.MAHKEME_KODU
                INNER JOIN iym.KULLANICILAR kl ON mkt.KULLANICI_ID = kl.ID
                INNER JOIN iym.EVRAK_KAYIT e ON mkt.EVRAK_ID = e.ID
                INNER JOIN iym.ILLER i ON mkt.MAHKEME_ILI = i.IL_KOD
                INNER JOIN iym.KULLANICI_KURUM kk ON mkt.KULLANICI_ID = kk.KULLANICI_ID
                INNER JOIN iym.EVRAK_GELEN_KURUMLAR egk ON kk.KURUM_KOD = egk.KURUM_KOD
                WHERE 1=1
            """;

    @Override
    public List<MahkemeKararTalepSorguInfo> islenecekMahkemeKararTalepListesi(String kurumKodu) {
        // TODO
        List<MahkemeKararTalepSorguInfo> result = null;

        return result;
    }

    @Override
    public List<MahkemeKararTalepSorguInfo> mahkemeKararTalepSorgu(String kurumKodu, MahkemeKararTalepSorguParam param) {

        StringBuilder sql = new StringBuilder(MAHKEME_KARAR_TALEP_SORGU_BASESQL_STR);

        Map<String, Object> parameters = new HashMap<>();

        // Add kurumKodu filter for security - users should only see their institution's data
        if (kurumKodu != null && !kurumKodu.isEmpty()) {
            sql.append(" AND egk.KURUM_KOD = :kurumKodu");
            parameters.put("kurumKodu", kurumKodu);
        }

        if (param.getSorusturmaNo() != null && !param.getSorusturmaNo().isEmpty()) {
            sql.append(" AND mkt.SORUSTURMA_NO = :sorusturmaNo");
            parameters.put("sorusturmaNo", param.getSorusturmaNo());
        }

        if (param.getMahkemeKararNo() != null && !param.getMahkemeKararNo().isEmpty()) {
            sql.append(" AND mkt.MAHKEME_KARAR_NO = :mahkemeKararNo");
            parameters.put("mahkemeKararNo", param.getMahkemeKararNo());
        }

        if (param.getMahkemeKodu() != null && !param.getMahkemeKodu().isEmpty()) {
            sql.append(" AND mkt.MAHKEME_KODU = :mahkemeKodu");
            parameters.put("mahkemeKodu", param.getMahkemeKodu());
        }

        if (param.getDurum() != null && !param.getDurum().isEmpty()) {
            sql.append(" AND mkt.DURUM = :durum");
            parameters.put("durum", param.getDurum());
        }

        if (param.getAciklama() != null && !param.getAciklama().isEmpty()) {
            // Use UPPER() instead of LOWER() for better Turkish character handling
            // Oracle's UPPER() function handles Turkish characters (İ, Ğ, Ü, Ö, Ş, Ç) more reliably than LOWER()
            // This ensures proper case-insensitive search for Turkish text without character conversion issues
            // Note: CONCAT(CONCAT('%', :aciklama), '%') is used because Oracle CONCAT only accepts 2 parameters
            sql.append(" AND UPPER(mkt.ACIKLAMA) LIKE UPPER(CONCAT(CONCAT('%', :aciklama), '%'))");
            parameters.put("aciklama", param.getAciklama());
        }

        if (param.getKayitTarihi() != null) {
            LocalDateTime startOfDay = DateTimeUtils.truncateTime(param.getKayitTarihi());
            LocalDateTime nextDayStart = DateTimeUtils.addDays(startOfDay, 1);

            sql.append(" AND mkt.KAYIT_TARIHI >= :kayitTarihiStart");
            sql.append(" AND mkt.KAYIT_TARIHI < :kayitTarihiEnd");
            Timestamp kayitTarihiStart = Timestamp.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
            Timestamp kayitTarihiEnd = Timestamp.from(nextDayStart.atZone(ZoneId.systemDefault()).toInstant());

            parameters.put("kayitTarihiStart", kayitTarihiStart);
            parameters.put("kayitTarihiEnd", kayitTarihiEnd);
        }

        if (param.getKaydedenKullaniciId() != null) {
            sql.append(" AND mkt.KULLANICI_ID = :kaydedenKullaniciId");
            parameters.put("kaydedenKullaniciId", param.getKaydedenKullaniciId());
        }

        if (param.getEvrakSiraNo() != null && !param.getEvrakSiraNo().isEmpty()) {
            sql.append(" AND e.EVRAK_SIRA_NO = :evrakSiraNo");
            parameters.put("evrakSiraNo", param.getEvrakSiraNo());
        }

        // Add ORDER BY for consistent results
        sql.append(" ORDER BY mkt.KAYIT_TARIHI DESC");

        try {
            Query query = entityManager.createNativeQuery(sql.toString(), "MahkemeKararTalepSorguInfoMapping");
            parameters.forEach(query::setParameter);
            return query.getResultList();
        } catch (Exception e) {
            // Log the error and rethrow as a runtime exception
            throw new RuntimeException("Error executing mahkemeKararTalepSorgu query: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public Long testInsertUserAuditLog(MakosUserAuditType userAuditType) {
        // Create a simple log table insert for testing transaction behavior
        // This simulates a custom insert operation that should be part of the same transaction

        // Use Oracle-specific syntax for sequence generation
        // Using positional parameters instead of named parameters for Oracle compatibility

        // First get the next sequence value for ID
        Long nextId = ((Number) entityManager.createNativeQuery("SELECT iym.MAKOS_USER_AUDIT_LOG_SEQ.NEXTVAL FROM DUAL")
                .getSingleResult()).longValue();

        String sql = """
            INSERT INTO IYM.MAKOS_USER_AUDIT_LOG
                (ID, USER_AUDIT_TYPE, USERNAME, ACTING_USERNAME, USER_IP, ADMIN_OPERATED_USERNAME, REQUEST_TIME, RESPONSE_TIME, RESPONSE_CODE)
            VALUES
                (?, ?, '', '', '', '', CURRENT_TIMESTAMP , '', 0)
            """;

        Query query = entityManager.createNativeQuery(sql);
        query.setParameter(1, nextId);
        query.setParameter(2, userAuditType.toString());
        int result = query.executeUpdate();

        if (result > 0) {
            return nextId;
        }

        throw new RuntimeException("Failed to insert testInsertUserAuditLog entry");
    }
}
