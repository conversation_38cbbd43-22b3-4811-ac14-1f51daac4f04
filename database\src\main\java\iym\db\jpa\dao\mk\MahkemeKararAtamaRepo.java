package iym.db.jpa.dao.mk;

import iym.common.model.entity.iym.MahkemeKararAtama;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Repository interface for MahkemeKararAtama entity
 */
@Repository
public interface MahkemeKararAtamaRepo extends JpaRepository<MahkemeKararAtama, Long> {

    List<MahkemeKararAtama> findByEvrakId(Long evrakId);

    List<MahkemeKararAtama> findByEvrakIdAndDurum(Long evrakId, String durum);

}
