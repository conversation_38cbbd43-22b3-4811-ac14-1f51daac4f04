// Enum değerlerini insan tarafından okunabilir etiketlere dönüştüren yardımcı metot
export function  formatEnumLabel(enumKey: string): string {
    // Önce alt çizgileri boşluklara dönüştür
    return enumKey
        // Alt çizgileri boşluklara dönüştür
        .replace(/_/g, ' ')
        // İlk karakteri ve boşluktan sonraki her karakteri büyük harf yap
        .replace(/\b\w/g, (str) => str.toUpperCase())
        // Fazla boşlukları temizle
        .trim();
}
