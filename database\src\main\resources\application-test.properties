
spring.jpa.database-platform=org.hibernate.dialect.OracleDialect
spring.datasource.driver-class-name=oracle.jdbc.OracleDriver
spring.datasource.url=************************************
spring.jpa.properties.hibernate.default_schema=iym
spring.datasource.username=iym
spring.datasource.password=iym

hibernate.dialect=org.hibernate.dialect.OracleDialect
hibernate.ejb.naming_strategy=org.hibernate.cfg.ImprovedNamingStrategy
hibernate.show_sql=true
hibernate.format_sql=true

## default connection pool
spring.datasource.hikari.connectionTimeout=20000
spring.datasource.hikari.maximumPoolSize=5

logging.level.*=DEBUG
logging.level.com.zaxxer.hikari.HikariConfig=DEBUG
logging.level.com.zaxxer.hikari=TRACE
logging.level.org.hibernate=DEBUG