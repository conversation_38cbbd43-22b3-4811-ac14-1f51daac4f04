package iym.common.service.db.mkislem;

import iym.common.model.entity.iym.mkislem.DetayMahkemeKararIslem;
import iym.common.model.entity.iym.talep.DetayMahkemeKararTalep;
import iym.common.service.db.GenericDbService;

import java.util.List;


public interface DbMahkemeKararIslemDetayService extends GenericDbService<DetayMahkemeKararIslem, Long> {

    List<DetayMahkemeKararIslem> findByEvrakId(Long evrakId);

    List<DetayMahkemeKararIslem> findByMahkemeKararIslemId(Long mahkemeKararIslemId);

}
