# Backend Arama Alanları Geliştirme Dokümantasyonu

## Genel Bakış
Frontend'de eklenen 4 yeni arama alanı için backend tarafında gerekli geliştirmeler:

### Yeni Arama Alanları
1. **mahkemeAdi** - Mahkeme adına göre arama
2. **kurumKodu** - Kurum koduna göre arama  
3. **kurumAdi** - Kurum adına göre arama
4. **evrakKonusu** - Evrak konusuna göre arama

## Gerekli Değişiklikler

### 1. MahkemeKararTalepSorguParam Sınıfı Güncelleme

**Dosya:** `makos/src/main/java/iym/makos/model/dto/mktalep/query/MahkemeKararTalepSorguParam.java`

```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Mahkeme Karar Talep Sorgu Parametreleri")
public class MahkemeKararTalepSorguParam {
    
    // Mevcut alanlar...
    @Schema(description = "Soruşturma No")
    private String sorusturmaNo;
    
    @Schema(description = "Mahkeme Karar No")
    private String mahkemeKararNo;
    
    @Schema(description = "Mahkeme Kodu")
    private String mahkemeKodu;
    
    @Schema(description = "Durum")
    private String durum;
    
    @Schema(description = "Açıklama")
    private String aciklama;
    
    @Schema(description = "Kayıt Tarihi")
    private LocalDateTime kayitTarihi;
    
    @Schema(description = "Kaydeden Kullanıcı ID")
    private Long kaydedenKullaniciId;
    
    @Schema(description = "Evrak Sıra No")
    private String evrakSiraNo;
    
    // YENİ ALANLAR
    @Schema(description = "Mahkeme Adı")
    private String mahkemeAdi;
    
    @Schema(description = "Kurum Kodu")
    private String kurumKodu;
    
    @Schema(description = "Kurum Adı")
    private String kurumAdi;
    
    @Schema(description = "Evrak Konusu")
    private String evrakKonusu;
}
```

### 2. Repository/DAO Katmanı Güncelleme

**Dosya:** `makos/src/main/java/iym/makos/repository/MahkemeKararTalepRepository.java` (veya ilgili repository)

```java
@Repository
public interface MahkemeKararTalepRepository extends JpaRepository<MahkemeKararTalep, Long> {
    
    // Mevcut sorgu metodu güncellenmeli
    @Query("SELECT m FROM MahkemeKararTalep m " +
           "LEFT JOIN m.mahkeme mah " +
           "LEFT JOIN m.kurum kur " +
           "LEFT JOIN m.evrak ev " +
           "WHERE (:sorusturmaNo IS NULL OR m.sorusturmaNo LIKE %:sorusturmaNo%) " +
           "AND (:mahkemeKararNo IS NULL OR m.mahkemeKararNo LIKE %:mahkemeKararNo%) " +
           "AND (:mahkemeKodu IS NULL OR m.mahkemeKodu LIKE %:mahkemeKodu%) " +
           "AND (:durum IS NULL OR m.durum = :durum) " +
           "AND (:aciklama IS NULL OR m.aciklama LIKE %:aciklama%) " +
           "AND (:kayitTarihi IS NULL OR m.kayitTarihi = :kayitTarihi) " +
           "AND (:kaydedenKullaniciId IS NULL OR m.kaydedenKullaniciId = :kaydedenKullaniciId) " +
           "AND (:evrakSiraNo IS NULL OR m.evrakSiraNo LIKE %:evrakSiraNo%) " +
           // YENİ ALANLAR
           "AND (:mahkemeAdi IS NULL OR mah.mahkemeAdi LIKE %:mahkemeAdi%) " +
           "AND (:kurumKodu IS NULL OR kur.kurumKodu LIKE %:kurumKodu%) " +
           "AND (:kurumAdi IS NULL OR kur.kurumAdi LIKE %:kurumAdi%) " +
           "AND (:evrakKonusu IS NULL OR ev.evrakKonusu LIKE %:evrakKonusu%) " +
           "ORDER BY m.kayitTarihi DESC")
    List<MahkemeKararTalep> findBySearchCriteria(
            @Param("sorusturmaNo") String sorusturmaNo,
            @Param("mahkemeKararNo") String mahkemeKararNo,
            @Param("mahkemeKodu") String mahkemeKodu,
            @Param("durum") String durum,
            @Param("aciklama") String aciklama,
            @Param("kayitTarihi") LocalDateTime kayitTarihi,
            @Param("kaydedenKullaniciId") Long kaydedenKullaniciId,
            @Param("evrakSiraNo") String evrakSiraNo,
            // YENİ PARAMETRELER
            @Param("mahkemeAdi") String mahkemeAdi,
            @Param("kurumKodu") String kurumKodu,
            @Param("kurumAdi") String kurumAdi,
            @Param("evrakKonusu") String evrakKonusu
    );
}
```

### 3. Service Katmanı Güncelleme

**Dosya:** `makos/src/main/java/iym/makos/service/mktalep/MahkemeKararTalepService.java`

```java
@Service
public class MahkemeKararTalepService {
    
    @Autowired
    private MahkemeKararTalepRepository repository;
    
    @Autowired
    private MahkemeKararTalepSorguMapper mapper;
    
    public List<MahkemeKararTalepSorguView> mahkemeKararTalepSorgu(MahkemeKararTalepSorguParam param) {
        
        // Yeni alanları da dahil ederek repository çağrısı
        List<MahkemeKararTalep> entities = repository.findBySearchCriteria(
                param.getSorusturmaNo(),
                param.getMahkemeKararNo(),
                param.getMahkemeKodu(),
                param.getDurum(),
                param.getAciklama(),
                param.getKayitTarihi(),
                param.getKaydedenKullaniciId(),
                param.getEvrakSiraNo(),
                // YENİ ALANLAR
                param.getMahkemeAdi(),
                param.getKurumKodu(),
                param.getKurumAdi(),
                param.getEvrakKonusu()
        );
        
        return entities.stream()
                .map(mapper::toMahkemeKararTalepSorguView)
                .collect(Collectors.toList());
    }
}
```

### 4. OpenAPI Schema Güncelleme

**Dosya:** `backend/src/main/resources/makos-api.yaml`

```yaml
MahkemeKararTalepSorguParam:
  type: object
  properties:
    # Mevcut alanlar...
    sorusturmaNo:
      type: string
      description: Soruşturma No
    mahkemeKararNo:
      type: string
      description: Mahkeme Karar No
    mahkemeKodu:
      type: string
      description: Mahkeme Kodu
    durum:
      type: string
      description: Durum
    aciklama:
      type: string
      description: Açıklama
    kayitTarihi:
      type: string
      format: date-time
      description: Kayıt Tarihi
    kaydedenKullaniciId:
      type: integer
      format: int64
      description: Kaydeden Kullanıcı ID
    evrakSiraNo:
      type: string
      description: Evrak Sıra No
    # YENİ ALANLAR
    mahkemeAdi:
      type: string
      description: Mahkeme Adı
    kurumKodu:
      type: string
      description: Kurum Kodu
    kurumAdi:
      type: string
      description: Kurum Adı
    evrakKonusu:
      type: string
      description: Evrak Konusu
```

## Alternatif Yaklaşım: Dynamic Query Builder

Eğer mevcut sistem dynamic query kullanıyorsa:

```java
@Service
public class MahkemeKararTalepService {
    
    public List<MahkemeKararTalepSorguView> mahkemeKararTalepSorgu(MahkemeKararTalepSorguParam param) {
        
        // Dynamic query builder kullanımı
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<MahkemeKararTalep> query = cb.createQuery(MahkemeKararTalep.class);
        Root<MahkemeKararTalep> root = query.from(MahkemeKararTalep.class);
        
        // Join'ler
        Join<MahkemeKararTalep, Mahkeme> mahkemeJoin = root.join("mahkeme", JoinType.LEFT);
        Join<MahkemeKararTalep, Kurum> kurumJoin = root.join("kurum", JoinType.LEFT);
        Join<MahkemeKararTalep, Evrak> evrakJoin = root.join("evrak", JoinType.LEFT);
        
        List<Predicate> predicates = new ArrayList<>();
        
        // Mevcut alanlar...
        if (param.getSorusturmaNo() != null) {
            predicates.add(cb.like(root.get("sorusturmaNo"), "%" + param.getSorusturmaNo() + "%"));
        }
        
        // YENİ ALANLAR
        if (param.getMahkemeAdi() != null) {
            predicates.add(cb.like(mahkemeJoin.get("mahkemeAdi"), "%" + param.getMahkemeAdi() + "%"));
        }
        
        if (param.getKurumKodu() != null) {
            predicates.add(cb.like(kurumJoin.get("kurumKodu"), "%" + param.getKurumKodu() + "%"));
        }
        
        if (param.getKurumAdi() != null) {
            predicates.add(cb.like(kurumJoin.get("kurumAdi"), "%" + param.getKurumAdi() + "%"));
        }
        
        if (param.getEvrakKonusu() != null) {
            predicates.add(cb.like(evrakJoin.get("evrakKonusu"), "%" + param.getEvrakKonusu() + "%"));
        }
        
        query.where(predicates.toArray(new Predicate[0]));
        query.orderBy(cb.desc(root.get("kayitTarihi")));
        
        List<MahkemeKararTalep> entities = entityManager.createQuery(query).getResultList();
        
        return entities.stream()
                .map(mapper::toMahkemeKararTalepSorguView)
                .collect(Collectors.toList());
    }
}
```

## Test Senaryoları

### 1. Mahkeme Adı Arama Testi
```java
@Test
public void testMahkemeAdiArama() {
    MahkemeKararTalepSorguParam param = new MahkemeKararTalepSorguParam();
    param.setMahkemeAdi("Ankara");
    
    List<MahkemeKararTalepSorguView> sonuclar = service.mahkemeKararTalepSorgu(param);
    
    assertThat(sonuclar).isNotEmpty();
    assertThat(sonuclar).allMatch(sonuc -> 
        sonuc.getMahkemeAdi() != null && 
        sonuc.getMahkemeAdi().toLowerCase().contains("ankara"));
}
```

### 2. Kurum Kodu Arama Testi
```java
@Test
public void testKurumKoduArama() {
    MahkemeKararTalepSorguParam param = new MahkemeKararTalepSorguParam();
    param.setKurumKodu("001");
    
    List<MahkemeKararTalepSorguView> sonuclar = service.mahkemeKararTalepSorgu(param);
    
    assertThat(sonuclar).isNotEmpty();
    assertThat(sonuclar).allMatch(sonuc -> 
        sonuc.getKurumKodu() != null && 
        sonuc.getKurumKodu().contains("001"));
}
```

## Performans Optimizasyonları

### 1. Index Önerileri
```sql
-- Mahkeme adı için index
CREATE INDEX idx_mahkeme_adi ON mahkeme(mahkeme_adi);

-- Kurum kodu için index  
CREATE INDEX idx_kurum_kodu ON kurum(kurum_kodu);

-- Kurum adı için index
CREATE INDEX idx_kurum_adi ON kurum(kurum_adi);

-- Evrak konusu için index
CREATE INDEX idx_evrak_konusu ON evrak(evrak_konusu);
```

### 2. Pagination Desteği
```java
public Page<MahkemeKararTalepSorguView> mahkemeKararTalepSorgu(
        MahkemeKararTalepSorguParam param, 
        Pageable pageable) {
    
    // Repository metoduna pageable parametresi eklenmeli
    Page<MahkemeKararTalep> entities = repository.findBySearchCriteria(
            param, pageable);
    
    return entities.map(mapper::toMahkemeKararTalepSorguView);
}
```

## Özet

Bu dokümantasyon, frontend'de eklenen 4 yeni arama alanının backend tarafında implementasyonu için gerekli tüm değişiklikleri kapsamaktadır:

1. ✅ **DTO Güncelleme** - Yeni alanların parametre sınıfına eklenmesi
2. ✅ **Repository Güncelleme** - Sorgu metodlarının yeni alanları desteklemesi
3. ✅ **Service Güncelleme** - Business logic'in yeni parametreleri işlemesi
4. ✅ **OpenAPI Güncelleme** - API dokümantasyonunun güncellenmesi
5. ✅ **Test Senaryoları** - Yeni fonksiyonalitenin test edilmesi
6. ✅ **Performans Optimizasyonları** - Index ve pagination önerileri

Bu değişiklikler tamamlandıktan sonra, frontend'deki yeni arama alanları backend tarafında da tam olarak desteklenecektir.
