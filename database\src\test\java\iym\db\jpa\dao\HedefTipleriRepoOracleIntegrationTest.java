package iym.db.jpa.dao;

import iym.common.model.entity.iym.HedefTipleri;
import iym.common.testcontainer.AbstractOracleTestContainerForDataJpa;
import iym.spring.db.loader.DbLoader;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.boot.test.autoconfigure.orm.jpa.TestEntityManager;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.transaction.annotation.Transactional;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Oracle Testcontainer integration tests for HedefTipleri entity.
 * <p>
 * This test class specifically tests Oracle-specific column definitions:
 * - CHAR(1) column definition for SONLANDIRMAMI field
 * - Oracle constraint behavior
 * - Oracle-specific SQL queries
 * <p>
 * The HedefTipleri entity contains Oracle-specific column definitions that
 * cannot be properly tested with H2 database.
 *
 * <AUTHOR> Team
 */
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_CLASS)
@DataJpaTest
@ContextConfiguration(classes = {DbLoader.class})
@ActiveProfiles("oracle-test")
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@Import({AbstractOracleTestContainerForDataJpa.OracleTestContainerConfiguration.class})
@Transactional
@Execution(ExecutionMode.SAME_THREAD) // Temporarily disabled parallel execution for debugging
@DisplayName("HedefTipleri Oracle Integration Tests")
class HedefTipleriRepoOracleIntegrationTest extends AbstractOracleTestContainerForDataJpa {

    @Autowired
    private TestEntityManager entityManager;

    private HedefTipleri testHedefTipi;

    @BeforeEach
    void setUp() {
        // Ensure clean container state for parallel execution
        ensureCleanContainerState();

        // Create test HedefTipleri with Oracle-specific column values
        testHedefTipi = new HedefTipleri();
        testHedefTipi.setId(100L);
        testHedefTipi.setHedefKodu(1001L);
        testHedefTipi.setHedefTipi("TEST_HEDEF_TIPI");
        testHedefTipi.setSonlandirmami("E"); // CHAR(1) - Oracle specific
        testHedefTipi.setKarsiligi(2001L);
        testHedefTipi.setSno(3001L);
        testHedefTipi.setHedefTanim("TEST_TANIM");
        testHedefTipi.setDurum("AKTIF");
    }

    @AfterEach
    void tearDown() {
        // Clean up after each test to prevent interference in parallel execution
        try {
            if (entityManager != null) {
                entityManager.clear();
            }
        } catch (Exception e) {
            // Log but don't fail the test
            System.out.println("⚠️ Warning: Error during test cleanup: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("Should save and retrieve HedefTipleri with Oracle CHAR(1) column")
    void shouldSaveAndRetrieveHedefTipleri_withOracleCharColumn() {
        // When - Save entity with Oracle-specific CHAR(1) column
        HedefTipleri savedHedefTipi = entityManager.persistAndFlush(testHedefTipi);
        entityManager.clear();

        // Then - Retrieve and verify Oracle CHAR(1) column behavior
        HedefTipleri retrievedHedefTipi = entityManager.find(HedefTipleri.class, savedHedefTipi.getHedefKodu());

        assertThat(retrievedHedefTipi).isNotNull();
        assertThat(retrievedHedefTipi.getHedefKodu()).isEqualTo(1001L);
        assertThat(retrievedHedefTipi.getHedefTipi()).isEqualTo("TEST_HEDEF_TIPI");
        assertThat(retrievedHedefTipi.getSonlandirmami()).isEqualTo("E"); // CHAR(1) column
        assertThat(retrievedHedefTipi.getSonlandirmami()).hasSize(1);
        assertThat(retrievedHedefTipi.getKarsiligi()).isEqualTo(2001L);
        assertThat(retrievedHedefTipi.getHedefTanim()).isEqualTo("TEST_TANIM");
        assertThat(retrievedHedefTipi.getDurum()).isEqualTo("AKTIF");
    }

    @Test
    @DisplayName("Should handle Oracle CHAR(1) column with different values")
    void shouldHandleOracleCharColumn_withDifferentValues() {
        // Test with 'E' (Evet)
        testHedefTipi.setSonlandirmami("E");
        HedefTipleri savedE = entityManager.persistAndFlush(testHedefTipi);
        entityManager.clear();

        HedefTipleri retrievedE = entityManager.find(HedefTipleri.class, savedE.getHedefKodu());
        assertThat(retrievedE.getSonlandirmami()).isEqualTo("E");

        // Test with 'H' (Hayır)
        HedefTipleri testHedefTipi2 = new HedefTipleri();
        testHedefTipi2.setId(101L);
        testHedefTipi2.setHedefKodu(1002L);
        testHedefTipi2.setHedefTipi("TEST_HEDEF_TIPI_2");
        testHedefTipi2.setSonlandirmami("H"); // CHAR(1) - Oracle specific
        testHedefTipi2.setKarsiligi(2002L);
        testHedefTipi2.setSno(3002L);
        testHedefTipi2.setHedefTanim("TEST_TANIM_2");
        testHedefTipi2.setDurum("AKTIF");

        HedefTipleri savedH = entityManager.persistAndFlush(testHedefTipi2);
        entityManager.clear();

        HedefTipleri retrievedH = entityManager.find(HedefTipleri.class, savedH.getHedefKodu());
        assertThat(retrievedH.getSonlandirmami()).isEqualTo("H");
        assertThat(retrievedH.getSonlandirmami()).hasSize(1);
    }

    @Test
    @DisplayName("Should handle Oracle CHAR(1) column with null value")
    void shouldHandleOracleCharColumn_withNullValue() {
        // Given - Set CHAR(1) column to null
        testHedefTipi.setSonlandirmami(null);
        testHedefTipi.setHedefKodu(1003L);

        // When - Save and retrieve
        HedefTipleri savedHedefTipi = entityManager.persistAndFlush(testHedefTipi);
        entityManager.clear();

        HedefTipleri retrievedHedefTipi = entityManager.find(HedefTipleri.class, savedHedefTipi.getHedefKodu());

        // Then - Verify null handling in Oracle CHAR(1) column
        assertThat(retrievedHedefTipi).isNotNull();
        assertThat(retrievedHedefTipi.getSonlandirmami()).isNull();
    }

    @Test
    @DisplayName("Should enforce Oracle primary key constraint on HEDEF_KODU")
    void shouldEnforceOraclePrimaryKeyConstraint_onHedefKodu() {
        // Given - Save first entity
        entityManager.persistAndFlush(testHedefTipi);

        // When - Try to save another entity with same HEDEF_KODU
        HedefTipleri duplicateHedefTipi = new HedefTipleri();
        duplicateHedefTipi.setId(102L);
        duplicateHedefTipi.setHedefKodu(1001L); // Same HEDEF_KODU
        duplicateHedefTipi.setHedefTipi("DUPLICATE_HEDEF_TIPI");
        duplicateHedefTipi.setSonlandirmami("H");
        duplicateHedefTipi.setKarsiligi(2003L);
        duplicateHedefTipi.setSno(3003L);
        duplicateHedefTipi.setHedefTanim("DUPLICATE_TANIM");
        duplicateHedefTipi.setDurum("AKTIF");

        // Then - Should throw constraint violation exception
        org.junit.jupiter.api.Assertions.assertThrows(Exception.class, () -> entityManager.persistAndFlush(duplicateHedefTipi));
    }

    @Test
    @DisplayName("Should handle Oracle VARCHAR2 columns correctly")
    void shouldHandleOracleVarchar2Columns_correctly() {
        // Given - Set VARCHAR2 columns with various lengths
        testHedefTipi.setHedefTipi("LONG_HEDEF_TIPI_NAME_TEST"); // VARCHAR2(25)
        testHedefTipi.setHedefTanim("LONG_TANIM_TEST"); // VARCHAR2(16)
        testHedefTipi.setDurum("PASIF"); // VARCHAR2(8)

        // When - Save and retrieve
        HedefTipleri savedHedefTipi = entityManager.persistAndFlush(testHedefTipi);
        entityManager.clear();

        HedefTipleri retrievedHedefTipi = entityManager.find(HedefTipleri.class, savedHedefTipi.getHedefKodu());

        // Then - Verify Oracle VARCHAR2 behavior
        assertThat(retrievedHedefTipi).isNotNull();
        assertThat(retrievedHedefTipi.getHedefTipi()).isEqualTo("LONG_HEDEF_TIPI_NAME_TEST");
        assertThat(retrievedHedefTipi.getHedefTanim()).isEqualTo("LONG_TANIM_TEST");
        assertThat(retrievedHedefTipi.getDurum()).isEqualTo("PASIF");
    }

    @Test
    @DisplayName("Should handle Oracle NUMBER columns correctly")
    void shouldHandleOracleNumberColumns_correctly() {
        // Given - Set Oracle NUMBER columns with large values
        testHedefTipi.setKarsiligi(999999999L);
        testHedefTipi.setSno(888888888L);

        // When - Save and retrieve
        HedefTipleri savedHedefTipi = entityManager.persistAndFlush(testHedefTipi);
        entityManager.clear();

        HedefTipleri retrievedHedefTipi = entityManager.find(HedefTipleri.class, savedHedefTipi.getHedefKodu());

        // Then - Verify Oracle NUMBER behavior
        assertThat(retrievedHedefTipi).isNotNull();
        assertThat(retrievedHedefTipi.getKarsiligi()).isEqualTo(999999999L);
        assertThat(retrievedHedefTipi.getSno()).isEqualTo(888888888L);
    }

    @Test
    @DisplayName("Should retrieve existing HedefTipleri from init script")
    void shouldRetrieveExistingHedefTipleri_fromInitScript() {
        // When - Try to find HedefTipleri created by init script using HEDEF_KODU (primary key)
        HedefTipleri initHedefTipi = entityManager.find(HedefTipleri.class, 1L);

        // Then - Verify init script data
        if (initHedefTipi != null) {
            assertThat(initHedefTipi.getHedefKodu()).isEqualTo(1L);
            assertThat(initHedefTipi.getHedefTipi()).isEqualTo("GSM");
            assertThat(initHedefTipi.getSonlandirmami()).isEqualTo("E");
            assertThat(initHedefTipi.getDurum()).isEqualTo("AKTIF");
        }
    }
}
