# Build stage
FROM maven:3.9-eclipse-temurin-17 AS build
WORKDIR /app

# Copy the parent POM and submodule POMs first for better layer caching
COPY pom.xml .
COPY backend/pom.xml backend/
COPY common/pom.xml common/
COPY database/pom.xml database/
COPY makos/pom.xml makos/

# Download dependencies (this layer will be cached if pom files don't change)
RUN mvn dependency:go-offline -B -pl backend,common,database -am

# Copy source code
COPY common/ common/
COPY database/ database/
COPY backend/ backend/

# Build the application
RUN mvn clean package -DskipTests -pl backend -am

# Runtime stage
FROM eclipse-temurin:17-jre-alpine
WORKDIR /app

# Set timezone
RUN apk add --no-cache tzdata && \
    cp /usr/share/zoneinfo/Europe/Istanbul /etc/localtime && \
    echo "Europe/Istanbul" > /etc/timezone

# Copy the built JAR file
COPY --from=build /app/backend/target/*.jar app.jar

# Expose the port that <PERSON><PERSON> runs on
EXPOSE 8080

# Run the application
ENTRYPOINT ["java", "-jar", "-Dserver.port=8080", "app.jar"]
