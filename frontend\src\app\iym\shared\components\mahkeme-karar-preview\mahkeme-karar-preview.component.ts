import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DialogModule } from 'primeng/dialog';
import { ButtonModule } from 'primeng/button';
import { KararTuruEnum } from '../../enums/KararTuruEnum';
import { EvrakDetay, MahkemeKararBilgisi } from '../../../../generated-api';
const EvrakTuruEnum = EvrakDetay.EvrakTuruEnum;
const MahkemeKararTipiEnum = MahkemeKararBilgisi.MahkemeKararTipiEnum;

@Component({
  selector: 'app-mahkeme-karar-preview',
  standalone: true,
  imports: [CommonModule, DialogModule, ButtonModule],
  templateUrl: './mahkeme-karar-preview.component.html',
  styleUrls: ['./mahkeme-karar-preview.component.scss']
})
export class MahkemeKararPreviewComponent {
  @Input() visible: boolean = false;
  @Input() formData: any = {};
  @Input() kararTuru: KararTuruEnum | null = null;
  @Input() seciliDosya: File | null = null;
  @Output() visibleChange = new EventEmitter<boolean>();
  @Output() onConfirm = new EventEmitter<void>();

  // Enums for template
  KararTuruEnum = KararTuruEnum;
  EvrakTuruEnum = EvrakTuruEnum;
  MahkemeKararTipiEnum = MahkemeKararTipiEnum;

  constructor() {}

  onDialogHide(): void {
    this.visible = false;
    this.visibleChange.emit(this.visible);
  }

  onConfirmClick(): void {
    this.onConfirm.emit();
    this.onDialogHide();
  }

  getKararTuruLabel(kararTuru: KararTuruEnum): string {
    const labels: { [key: string]: string } = {
      [KararTuruEnum.IletisiminDenetlenmesiYeniKarar]: 'İletişimin Denetlenmesi Yeni Karar',
      [KararTuruEnum.IletisiminDenetlenmesiUzatmaKarari]: 'İletişimin Denetlenmesi Uzatma Kararı',
      [KararTuruEnum.IletisiminDenetlenmesiSonlandirmaKarari]: 'İletişimin Denetlenmesi Sonlandırma Kararı',
      [KararTuruEnum.IletisiminDenetlenmesiHedefGuncelleme]: 'İletişimin Denetlenmesi Hedef Güncelleme',
      [KararTuruEnum.IletisiminDenetlenmesiMahkemekararGuncelleme]: 'İletişimin Denetlenmesi Mahkeme Karar Güncelleme',
      [KararTuruEnum.IletisiminDenetlenmesiAidiyatGuncelleme]: 'İletişimin Denetlenmesi Aidiyat Güncelleme',
      [KararTuruEnum.IletisiminTespiti]: 'İletişimin Tespiti'
    };
    return labels[kararTuru] || kararTuru;
  }

  getEvrakTuruLabel(evrakTuru: typeof EvrakTuruEnum[keyof typeof EvrakTuruEnum]): string {
    const labels: { [key: string]: string } = {
      [EvrakTuruEnum.IletisiminDenetlenmesi]: 'İletişimin Denetlenmesi',
      [EvrakTuruEnum.IletisiminTespiti]: 'İletişimin Tespiti'
    };
    return labels[evrakTuru] || evrakTuru;
  }

  getMahkemeKararTipiLabel(mahkemeKararTipi: typeof MahkemeKararTipiEnum[keyof typeof MahkemeKararTipiEnum]): string {
    const labels: { [key: string]: string } = {
      [MahkemeKararTipiEnum.OnleyiciHakimKarari]: 'Önleyici Hakim Kararı',
      [MahkemeKararTipiEnum.AdliHakimKarari]: 'Adli Hakim Kararı',
      [MahkemeKararTipiEnum.AdliSavcilikHtsKarari]: 'Adli Savcılık HTS Kararı'
    };
    return labels[mahkemeKararTipi] || mahkemeKararTipi;
  }

  getHedefTipLabel(hedefTip: string): string {
    const labels: { [key: string]: string } = {
      'GSM': 'GSM',
      'SABIT': 'Sabit',
      'IMEI': 'IMEI',
      'IP_TAKIP': 'IP Takip'
    };
    return labels[hedefTip] || hedefTip;
  }

  getSureTipiLabel(sureTip: string): string {
    const labels: { [key: string]: string } = {
      'GUN': 'Gün',
      'AY': 'Ay',
      'YIL': 'Yıl'
    };
    return labels[sureTip] || sureTip;
  }

  getSorguTipiLabel(sorguTipi: string): string {
    const labels: { [key: string]: string } = {
      'TELEFON_GORUSME': 'Telefon Görüşme',
      'IMEI_GORUSME': 'IMEI Görüşme',
      'IMEI_KULLANAN_NUMARA': 'IMEI Kullanan Numara'
    };
    return labels[sorguTipi] || sorguTipi;
  }

  formatTarih(tarih: string | Date): string {
    if (!tarih) return '';
    const date = new Date(tarih);
    return date.toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  hasHedefDetayListesi(): boolean {
    return this.formData.hedefDetayListesi && this.formData.hedefDetayListesi.length > 0;
  }

  hasAidiyatKodlari(): boolean {
    return this.formData.mahkemeAidiyatKodlari && this.formData.mahkemeAidiyatKodlari.length > 0;
  }

  hasSucTipiKodlari(): boolean {
    return this.formData.mahkemeSucTipiKodlari && this.formData.mahkemeSucTipiKodlari.length > 0;
  }

  hasGuncellemeDetaylari(): boolean {
    return (this.formData.hedefGuncellemeKararDetayListesi && this.formData.hedefGuncellemeKararDetayListesi.length > 0) ||
           (this.formData.mahkemeKararGuncellemeDetayListesi && this.formData.mahkemeKararGuncellemeDetayListesi.length > 0) ||
           (this.formData.aidiyatGuncellemeKararDetayListesi && this.formData.aidiyatGuncellemeKararDetayListesi.length > 0);
  }

  hasITHedefDetaylari(): boolean {
    return this.formData.itHedefDetayListesi && this.formData.itHedefDetayListesi.length > 0;
  }

  getPreviewData(): any {
    // Combine common form data with specific form data based on karar türü
    const commonData = this.formData.commonForm || {};
    const specificData = this.getSpecificFormData();

    return {
      ...commonData,
      ...specificData,
      kararTuru: this.kararTuru,
      seciliDosya: this.seciliDosya
    };
  }

  private getSpecificFormData(): any {
    switch (this.kararTuru) {
      case KararTuruEnum.IletisiminDenetlenmesiYeniKarar:
      case KararTuruEnum.IletisiminDenetlenmesiUzatmaKarari:
      case KararTuruEnum.IletisiminDenetlenmesiSonlandirmaKarari:
        return {
          hedefDetayListesi: this.formData.hedefDetayListesi || [],
          mahkemeAidiyatKodlari: this.formData.mahkemeAidiyatKodlari || [],
          mahkemeSucTipiKodlari: this.formData.mahkemeSucTipiKodlari || []
        };
      case KararTuruEnum.IletisiminDenetlenmesiHedefGuncelleme:
        return {
          hedefGuncellemeKararDetayListesi: this.formData.hedefGuncellemeKararDetayListesi || []
        };
      case KararTuruEnum.IletisiminDenetlenmesiMahkemekararGuncelleme:
        return {
          mahkemeKararGuncellemeDetayListesi: this.formData.mahkemeKararGuncellemeDetayListesi || []
        };
      case KararTuruEnum.IletisiminDenetlenmesiAidiyatGuncelleme:
        return {
          aidiyatGuncellemeKararDetayListesi: this.formData.aidiyatGuncellemeKararDetayListesi || []
        };
      case KararTuruEnum.IletisiminTespiti:
        return {
          itHedefDetayListesi: this.formData.itHedefDetayListesi || []
        };
      default:
        return {};
    }
  }
}
