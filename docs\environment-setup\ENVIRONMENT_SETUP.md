# Ortam Bazlı Konfigürasyon Rehberi

Bu proje artık ortam bazlı konfigürasyon kullanmaktadır. Farklı ortamlarda farklı veritabanları kullanılabilir.

## Ortamlar

### 1. Development (dev) - Varsayılan
- **Veritabanı**: Oracle (Docker container)
- **Profil**: `dev`
- **Kullanım**: <PERSON><PERSON> ortamı

### 2. Production (prod)
- **Veritabanı**: Oracle (Production server)
- **Profil**: `prod`
- **Kullanım**: Canlı ortam

### 3. Test (test)
- **Veritabanı**: H2 In-Memory
- **Profil**: `test`
- **Kullanım**: Birim testleri ve entegrasyon testleri

## Nasıl Çalıştırılır

### Development Ortamında (Varsayılan) - Windows 11 PowerShell
```powershell
# Oracle Docker container'ını başlat
cd docker\oracle
docker-compose up -d

# Backend modülünü çalıştır (yeni PowerShell penceresi) - Port 4000
cd ..\..\backend
mvn spring-boot:run

# Makos modülünü çalıştır (başka bir PowerShell penceresi) - Port 5000
cd ..\makos
mvn spring-boot:run
```

### Production Ortamında - Windows 11 PowerShell
```powershell
# Environment variables ile production ayarları
$env:DB_HOST="production-oracle-server"
$env:DB_PORT="1521"
$env:DB_SERVICE="PROD"
$env:DB_USERNAME="iym_prod"
$env:DB_PASSWORD="prod_password"
$env:DB_SCHEMA="iym_prod"

# Backend modülünü production profili ile çalıştır - Port 4000
cd backend
mvn spring-boot:run "-Dspring.profiles.active=prod"

# Makos modülünü production profili ile çalıştır - Port 5000
cd ..\makos
mvn spring-boot:run "-Dspring.profiles.active=prod"
```

### Test Ortamında - Windows 11 PowerShell
```powershell
# Testler otomatik olarak test profilini kullanır
mvn test
```

### Belirli Profil ile Çalıştırma - Windows 11 PowerShell
```powershell
# Development profili ile
mvn spring-boot:run "-Dspring.profiles.active=dev"

# Production profili ile
mvn spring-boot:run "-Dspring.profiles.active=prod"

# Test profili ile
mvn spring-boot:run "-Dspring.profiles.active=test"
```

## Konfigürasyon Dosyaları

### Backend Modülü
- `application.properties` - Genel ayarlar ve varsayılan profil (dev)
- `application-dev.properties` - Development ortamı (Oracle Docker)
- `application-prod.properties` - Production ortamı (Oracle Server)
- `application-test.properties` - Test ortamı (H2 In-Memory)

### Makos Modülü
- `application.properties` - Genel ayarlar ve varsayılan profil (dev)
- `application-dev.properties` - Development ortamı (Oracle Docker)
- `application-prod.properties` - Production ortamı (Oracle Server)
- `application-test.properties` - Test ortamı (H2 In-Memory)

## Veritabanı Ayarları

### Oracle (Development & Production)
- **Driver**: `oracle.jdbc.OracleDriver`
- **Dialect**: `org.hibernate.dialect.OracleDialect`
- **Schema**: `iym`

### H2 (Test)
- **Driver**: `org.h2.Driver`
- **Dialect**: `org.hibernate.dialect.H2Dialect`
- **URL**: In-memory database

## Environment Variables (Production)

Production ortamında aşağıdaki environment variables kullanılabilir:

- `DB_HOST`: Oracle server hostname (default: localhost)
- `DB_PORT`: Oracle server port (default: 1521)
- `DB_SERVICE`: Oracle service name (default: XE)
- `DB_USERNAME`: Database username (default: iym)
- `DB_PASSWORD`: Database password (default: iym)
- `DB_SCHEMA`: Database schema (default: iym)

## Önemli Notlar

1. **Varsayılan Profil**: Hiçbir profil belirtilmezse `dev` profili kullanılır
2. **Test Profili**: Unit testler otomatik olarak `test` profilini kullanır
3. **Docker**: Development ortamında Oracle için Docker kullanılır
4. **Production**: Production ortamında environment variables ile konfigürasyon yapılır
5. **Logging**: Her ortam için farklı logging seviyeleri ayarlanmıştır

## Konfigürasyon Testi - Windows 11 PowerShell

Konfigürasyonların doğru çalıştığını test etmek için:

```powershell
# Test profili ile birim test çalıştır (H2 kullanır)
mvn test "-Dtest=CanakNumaralarServiceTest"

# Development profili ile compile et (Oracle beklenir)
mvn compile "-Dspring.profiles.active=dev"
```

## Sorun Giderme - Windows 11 PowerShell

### Oracle Bağlantı Hatası
Eğer Oracle bağlantı hatası alırsanız:
```powershell
# Docker Oracle'ı başlatın
cd docker\oracle
docker-compose up -d

# Oracle'ın hazır olmasını bekleyin (yaklaşık 1-2 dakika)
docker logs iym-oracle

# Oracle container durumunu kontrol edin
docker ps | Select-String "iym-oracle"
```

### Test Hatası
Eğer testlerde veritabanı hatası alırsanız, test profili kullanıldığından emin olun:
- Test dosyalarında `@ActiveProfiles("test")` annotation'ı olmalı
- Veya `src/test/resources/application.properties` dosyasında `spring.profiles.active=test` olmalı
