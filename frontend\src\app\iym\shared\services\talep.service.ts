import {Injectable} from '@angular/core';
import {HttpClient, HttpResponse} from '@angular/common/http';
import {Observable} from 'rxjs';
import {environment} from '../../../../enviroments/environment';

// Generated API
import {
    MakosControllerService,
    ResponseEvrakGelenKurumlarResponse,
    ResponseIDYeniKararResponse,
    ResponseIllerResponse,
    ResponseMahkemeKararTipleriResponse,
    ResponseMahkemeKodlariResponse,
    ResponseSorguTipiListResponse,
    ResponseSucTipleriResponse,
    ResponseTespitTuruListResponse
} from '../../../generated-api';

// Generated API Models
import {IDYeniKararRequest} from '../../../generated-api';
import {ITKararRequest} from '../../../generated-api';

@Injectable({
    providedIn: 'root'
})
export class TalepService {

    private readonly apiUrl = environment.apiUrl;

    constructor(
        private http: HttpClient,
        private makosControllerService: MakosControllerService
    ) {
    }

    // /**
    //  * General request submission
    //  */
    // genelTalepGonder(request: MahkemeKararRequest, dosya?: File | null): Observable<any> {
    //   const formData = new FormData();
    //
    //   // Add request object as JSON string
    //   formData.append('mahkemeKararDetay', JSON.stringify(request));
    //
    //   // Add file if provided
    //   if (dosya) {
    //     formData.append('mahkemeKararDosyasi', dosya, dosya.name);
    //   }
    //
    //   return this.http.post(`${this.apiUrl}/makos/genel-talep`, formData);
    // }

    /**
     * ID New Decision submission
     * Sends file and JSON data in multipart/form-data format
     *
     * @param request IDYeniKararRequest object
     * @param dosya Decision file
     * @returns Observable<ResponseIDYeniKararResponse>
     */
    yeniKararIdGonder(request: IDYeniKararRequest, dosya: File): Observable<ResponseIDYeniKararResponse> {
        return this.makosControllerService.yeniKararID(dosya, request);
    }

    // /**
    //  * ID Extension Decision submission
    //  */
    // idUzatmaKarariGonder(request: IDUzatmaKarariRequest, dosya: File): Observable<any> {
    //   const formData = new FormData();
    //   formData.append('mahkemeKararDetay', JSON.stringify(request));
    //   formData.append('mahkemeKararDosyasi', dosya, dosya.name);
    //
    //   return this.http.post(`${this.apiUrl}/makos/uzatmaKarariGonderID`, formData);
    // }
    //
    // /**
    //  * ID Termination Decision submission
    //  */
    // idSonlandirmaKarariGonder(request: IDSonlandirmaKarariRequest, dosya: File): Observable<any> {
    //   const formData = new FormData();
    //   formData.append('mahkemeKararDetay', JSON.stringify(request));
    //   formData.append('mahkemeKararDosyasi', dosya, dosya.name);
    //
    //   return this.http.post(`${this.apiUrl}/makos/sonlandirmaKarariGonderID`, formData);
    // }

    /**
     * IT Decision submission
     */
    itKararGonder(request: ITKararRequest, dosya: File): Observable<any> {
        const formData = new FormData();
        formData.append('mahkemeKararDetay', JSON.stringify(request));
        formData.append('mahkemeKararDosyasi', dosya, dosya.name);

        return this.http.post(`${this.apiUrl}/makos/yeni-karar-it`, formData);
    }

    // /**
    //  * Affiliation Information Update
    //  */
    // aidiyatBilgisiGuncelle(request: IDAidiyatBilgisiGuncellemeRequest, dosya: File): Observable<any> {
    //   const formData = new FormData();
    //   formData.append('mahkemeKararDetay', JSON.stringify(request));
    //   formData.append('mahkemeKararDosyasi', dosya, dosya.name);
    //
    //   return this.http.post(`${this.apiUrl}/makos/aidiyatBilgisiGuncelle`, formData);
    // }
    //
    // /**
    //  * Target Name Surname Update
    //  */
    // hedefAdSoyadGuncelle(request: IDHedefAdSoyadGuncellemeRequest, dosya: File): Observable<any> {
    //   const formData = new FormData();
    //   formData.append('mahkemeKararDetay', JSON.stringify(request));
    //   formData.append('mahkemeKararDosyasi', dosya, dosya.name);
    //
    //   return this.http.post(`${this.apiUrl}/makos/hedefAdSoyadGuncelle`, formData);
    // }
    //
    // /**
    //  * Court Information Update
    //  */
    // mahkemeBilgisiGuncelle(request: IDMahkemeKoduGuncellemeRequest, dosya: File): Observable<any> {
    //   const formData = new FormData();
    //   formData.append('request', new Blob([JSON.stringify(request)], { type: 'application/json' }));
    //   formData.append('file', dosya);
    //
    //   return this.http.post(`${this.apiUrl}/makos/mahkemeBilgisiGuncelle`, formData);
    // }
    //
    // /**
    //  * Dish Update
    //  */
    // canakGuncelle(request: IDCanakGuncellemeRequest, dosya: File): Observable<any> {
    //   const formData = new FormData();
    //   formData.append('mahkemeKararDetay', JSON.stringify(request));
    //   formData.append('mahkemeKararDosyasi', dosya, dosya.name);
    //
    //   return this.http.post(`${this.apiUrl}/makos/canakNoGuncelle`, formData);
    // }

    /**
     * Request status query
     */
    talepDurumuSorgula(talepId: string): Observable<any> {
        return this.http.get(`${this.apiUrl}/makos/durum/${talepId}`);
    }

    /**
     * Request history retrieval
     */
    talepGecmisiGetir(sayfa: number = 0, boyut: number = 10): Observable<any> {
        return this.http.get(`${this.apiUrl}/makos/gecmis?sayfa=${sayfa}&boyut=${boyut}`);
    }

    /**
     * Request cancellation
     */
    talepIptalEt(talepId: string): Observable<any> {
        return this.http.delete(`${this.apiUrl}/makos/${talepId}`);
    }

    /**
     * File download
     */
    dosyaIndir(dosyaId: string): Observable<Blob> {
        return this.http.get(`${this.apiUrl}/makos/dosya/${dosyaId}`, {
            responseType: 'blob'
        });
    }

    /**
     * Court codes list retrieval
     */
    mahkemeKodlariGetir(): Observable<any[]> {
        return this.http.get<any[]>(`${this.apiUrl}/makos/mahkeme-kodlari`);
    }

    /**
     * Province district codes list retrieval
     */
    ilIlceKodlariGetir(): Observable<any[]> {
        return this.http.get<any[]>(`${this.apiUrl}/makos/il-ilce-kodlari`);
    }

    /**
     * Institution codes list retrieval
     */
    kurumKodlariGetir(): Observable<any[]> {
        return this.http.get<any[]>(`${this.apiUrl}/makos/kurum-kodlari`);
    }

    /**
     * Suç tipi kodları listesi getirme
     */
    sucTipiKodlariGetir(): Observable<any[]> {
        return this.http.get<any[]>(`${this.apiUrl}/makos/suc-tipi-kodlari`);
    }

    /**
     * Hedef tipleri listesi getirme
     */
    hedefTipleriGetir(): Observable<any[]> {
        return this.http.get<any[]>(`${this.apiUrl}/makos/hedef-tipleri`);
    }

    /**
     * Süre tipleri listesi getirme
     */
    sureTipleriGetir(): Observable<any[]> {
        return this.http.get<any[]>(`${this.apiUrl}/makos/sure-tipleri`);
    }

    /**
     * İller listesi getirme
     */
    illerGetir(): Observable<HttpResponse<ResponseIllerResponse>> {
        return this.makosControllerService.iller('response', false, {
            httpHeaderAccept: 'application/json'
        });
    }

    /**
     * Kurumlar listesi getirme
     */
    kurumlarGetir(): Observable<ResponseEvrakGelenKurumlarResponse> {
        return this.makosControllerService.kurumlar();
    }

    /**
     * Mahkeme kodları listesi getirme (domain sorgu)
     */
    mahkemeKodlariSorgula(): Observable<ResponseMahkemeKodlariResponse> {
        return this.makosControllerService.mahkemeKodlari();
    }

    /**
     * Suç tipleri listesi getirme (domain sorgu)
     */
    sucTipleriGetir(): Observable<ResponseSucTipleriResponse> {
        return this.makosControllerService.sucTipleri();
    }

    /**
     * Sorgu tipleri listesi getirme (domain sorgu)
     */
    sorguTipleriGetir(): Observable<ResponseSorguTipiListResponse> {
        return this.makosControllerService.sorguTipleri();
    }

    /**
     * Mahkeme karar tipleri listesi getirme (domain sorgu)
     */
    mahkemeKararTipleriGetir(): Observable<ResponseMahkemeKararTipleriResponse> {
        return this.makosControllerService.mahkemeKararTipleri();
    }

    /**
     * Tespit türleri listesi getirme (domain sorgu)
     */
    tespitTurleriGetir(): Observable<ResponseTespitTuruListResponse> {
        return this.makosControllerService.tespitTurleri();
    }
    
    // /**
    //  * Talep validasyonu
    //  */
    // talepValidasyonu(request: MahkemeKararRequest): Observable<any> {
    //   return this.http.post(`${this.apiUrl}/makos/validasyon`, request);
    // }

    /**
     * Dosya validasyonu
     */
    dosyaValidasyonu(dosya: File): Observable<any> {
        const formData = new FormData();
        formData.append('dosya', dosya, dosya.name);

        return this.http.post(`${this.apiUrl}/makos/dosya-validasyon`, formData);
    }

    // /**
    //  * Toplu talep gönderme
    //  */
    // topluTalepGonder(talepler: MahkemeKararRequest[], dosyalar: File[]): Observable<any> {
    //   const formData = new FormData();
    //
    //   // Talepleri JSON array olarak ekle
    //   formData.append('talepler', JSON.stringify(talepler));
    //
    //   // Dosyaları ekle
    //   dosyalar.forEach((dosya, index) => {
    //     formData.append(`dosya_${index}`, dosya, dosya.name);
    //   });
    //
    //   return this.http.post(`${this.apiUrl}/makos/toplu-talep`, formData);
    // }

    /**
     * İstatistik bilgileri getirme
     */
    istatistikBilgileriGetir(): Observable<any> {
        return this.http.get(`${this.apiUrl}/makos/istatistikler`);
    }

    /**
     * Rapor oluşturma
     */
    raporOlustur(raporParametreleri: any): Observable<any> {
        return this.http.post(`${this.apiUrl}/makos/rapor`, raporParametreleri);
    }

    /**
     * Hata logları getirme
     */
    hataLoglariniGetir(sayfa: number = 0, boyut: number = 10): Observable<any> {
        return this.http.get(`${this.apiUrl}/makos/hata-loglari?sayfa=${sayfa}&boyut=${boyut}`);
    }

    /**
     * Sistem durumu kontrolü
     */
    sistemDurumuKontrol(): Observable<any> {
        return this.http.get(`${this.apiUrl}/makos/sistem-durumu`);
    }
}
