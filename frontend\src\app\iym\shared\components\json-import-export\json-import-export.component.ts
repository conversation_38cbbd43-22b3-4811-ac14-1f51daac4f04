import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { DialogModule } from 'primeng/dialog';
import { ButtonModule } from 'primeng/button';
import { TabViewModule } from 'primeng/tabview';
import { FileUploadModule } from 'primeng/fileupload';
import { MessageService } from 'primeng/api';
import { KararTuruEnum } from '../../enums/KararTuruEnum';

@Component({
  selector: 'app-json-import-export',
  standalone: true,
  imports: [CommonModule, FormsModule, DialogModule, ButtonModule, TabViewModule, FileUploadModule],
  providers: [MessageService],
  templateUrl: './json-import-export.component.html',
  styleUrls: ['./json-import-export.component.scss']
})
export class JsonImportExportComponent {
  @Input() visible: boolean = false;
  @Input() formData: any = {};
  @Input() kararTuru: KararTuruEnum | null = null;
  @Output() visibleChange = new EventEmitter<boolean>();
  @Output() onImport = new EventEmitter<any>();

  activeTab: number = 0; // 0: Export, 1: Import
  exportedJson: string = '';
  importJson: string = '';
  importError: string = '';
  isValidJson: boolean = true;

  constructor(private messageService: MessageService) {}

  ngOnInit(): void {
    this.generateExportJson();
  }

  ngOnChanges(): void {
    if (this.visible) {
      this.generateExportJson();
      this.clearImport();
    }
  }

  onDialogHide(): void {
    this.visible = false;
    this.visibleChange.emit(this.visible);
  }

  onTabChange(event: any): void {
    this.activeTab = event.index;
    if (this.activeTab === 0) {
      this.generateExportJson();
    } else {
      this.clearImport();
    }
  }

  generateExportJson(): void {
    try {
      const exportData = {
        metadata: {
          exportDate: new Date().toISOString(),
          kararTuru: this.kararTuru,
          version: '1.0'
        },
        formData: this.formData
      };
      this.exportedJson = JSON.stringify(exportData, null, 2);
    } catch (error) {
      this.messageService.add({
        severity: 'error',
        summary: 'Hata',
        detail: 'JSON oluşturulurken hata oluştu.'
      });
    }
  }

  copyToClipboard(): void {
    navigator.clipboard.writeText(this.exportedJson).then(() => {
      this.messageService.add({
        severity: 'success',
        summary: 'Başarılı',
        detail: 'JSON panoya kopyalandı.'
      });
    }).catch(() => {
      this.messageService.add({
        severity: 'error',
        summary: 'Hata',
        detail: 'Panoya kopyalama başarısız.'
      });
    });
  }

  downloadJson(): void {
    try {
      const blob = new Blob([this.exportedJson], { type: 'application/json' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `mahkeme-karar-${this.kararTuru}-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      this.messageService.add({
        severity: 'success',
        summary: 'Başarılı',
        detail: 'JSON dosyası indirildi.'
      });
    } catch (error) {
      this.messageService.add({
        severity: 'error',
        summary: 'Hata',
        detail: 'Dosya indirme başarısız.'
      });
    }
  }

  clearImport(): void {
    this.importJson = '';
    this.importError = '';
    this.isValidJson = true;
  }

  validateImportJson(): void {
    this.importError = '';
    this.isValidJson = true;

    if (!this.importJson.trim()) {
      return;
    }

    try {
      const parsedData = JSON.parse(this.importJson);

      // Basic validation
      if (!parsedData.formData) {
        this.importError = 'JSON formatı geçersiz: formData alanı bulunamadı.';
        this.isValidJson = false;
        return;
      }

      // Karar türü kontrolü
      if (parsedData.metadata?.kararTuru && parsedData.metadata.kararTuru !== this.kararTuru) {
        this.importError = `Uyarı: JSON'daki karar türü (${parsedData.metadata.kararTuru}) mevcut karar türünden (${this.kararTuru}) farklı.`;
        // Bu durumda hata değil uyarı olarak devam edebiliriz
      }

      this.isValidJson = true;
    } catch (error) {
      this.importError = 'Geçersiz JSON formatı.';
      this.isValidJson = false;
    }
  }

  onImportJsonChange(): void {
    // Debounce validation
    setTimeout(() => {
      this.validateImportJson();
    }, 300);
  }

  importFromJson(): void {
    if (!this.isValidJson || !this.importJson.trim()) {
      this.messageService.add({
        severity: 'error',
        summary: 'Hata',
        detail: 'Geçerli bir JSON giriniz.'
      });
      return;
    }

    try {
      const parsedData = JSON.parse(this.importJson);
      this.onImport.emit(parsedData.formData);
      this.onDialogHide();

      this.messageService.add({
        severity: 'success',
        summary: 'Başarılı',
        detail: 'JSON verisi başarıyla içe aktarıldı.'
      });
    } catch (error) {
      this.messageService.add({
        severity: 'error',
        summary: 'Hata',
        detail: 'JSON içe aktarma başarısız.'
      });
    }
  }

  onFileSelect(event: any): void {
    const file = event.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e: any) => {
        this.importJson = e.target.result;
        this.validateImportJson();
      };
      reader.readAsText(file);
    }
  }

  formatJson(): void {
    if (this.importJson.trim()) {
      try {
        const parsed = JSON.parse(this.importJson);
        this.importJson = JSON.stringify(parsed, null, 2);
        this.validateImportJson();
      } catch (error) {
        this.messageService.add({
          severity: 'warn',
          summary: 'Uyarı',
          detail: 'JSON formatlanamadı - geçersiz format.'
        });
      }
    }
  }

  getKararTuruLabel(kararTuru: KararTuruEnum): string {
    const labels: { [key: string]: string } = {
      [KararTuruEnum.IletisiminDenetlenmesiYeniKarar]: 'İletişimin Denetlenmesi Yeni Karar',
      [KararTuruEnum.IletisiminDenetlenmesiUzatmaKarari]: 'İletişimin Denetlenmesi Uzatma Kararı',
      [KararTuruEnum.IletisiminDenetlenmesiSonlandirmaKarari]: 'İletişimin Denetlenmesi Sonlandırma Kararı',
      [KararTuruEnum.IletisiminDenetlenmesiHedefGuncelleme]: 'İletişimin Denetlenmesi Hedef Güncelleme',
      [KararTuruEnum.IletisiminDenetlenmesiMahkemekararGuncelleme]: 'İletişimin Denetlenmesi Mahkeme Karar Güncelleme',
      [KararTuruEnum.IletisiminDenetlenmesiAidiyatGuncelleme]: 'İletişimin Denetlenmesi Aidiyat Güncelleme',
      [KararTuruEnum.IletisiminTespiti]: 'İletişimin Tespiti'
    };
    return labels[kararTuru] || kararTuru;
  }

  getJsonSize(): string {
    const bytes = new Blob([this.exportedJson]).size;
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
