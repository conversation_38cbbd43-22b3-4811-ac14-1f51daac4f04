package iym.db.jpa.service.impl.mkislem;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.mkislem.MahkemeSucTipiDetayIslem;
import iym.common.service.db.mkislem.DbMahkemeSucTipiDetayIslemService;
import iym.db.jpa.dao.mkislem.MahkemeSucTipiDetayIslemRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class DbMahkemeSucTipiDetayIslemServiceImp extends GenericDbServiceImpl<MahkemeSucTipiDetayIslem, Long> implements DbMahkemeSucTipiDetayIslemService {

    private final MahkemeSucTipiDetayIslemRepo mahkemeSucTipiDetayIslemRepo;

    @Autowired
    public DbMahkemeSucTipiDetayIslemServiceImp(MahkemeSucTipiDetayIslemRepo repository) {
        super(repository);
        this.mahkemeSucTipiDetayIslemRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public List<MahkemeSucTipiDetayIslem> findByMahkemeKararDetayIslemId(Long mahkemeKararDetayIslemId) {
        return mahkemeSucTipiDetayIslemRepo.findByMahkemeKararDetayIslemId(mahkemeKararDetayIslemId);
    }

}
