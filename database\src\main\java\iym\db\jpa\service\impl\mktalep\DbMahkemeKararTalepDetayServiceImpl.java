package iym.db.jpa.service.impl.mktalep;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.talep.DetayMahkemeKararTalep;
import iym.common.service.db.mktalep.DbMahkemeKararTalepDetayService;
import iym.db.jpa.dao.mktalep.DetayMahkemeKararTalepRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class DbMahkemeKararTalepDetayServiceImpl extends GenericDbServiceImpl<DetayMahkemeKararTalep, Long> implements DbMahkemeKararTalepDetayService {

    private final DetayMahkemeKararTalepRepo detayMahkemeKararTalepRepo;

    @Autowired
    public DbMahkemeKararTalepDetayServiceImpl(DetayMahkemeKararTalepRepo repository) {
        super(repository);
        this.detayMahkemeKararTalepRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public List<DetayMahkemeKararTalep> findByEvrakId(Long evrakId){
        return detayMahkemeKararTalepRepo.findByEvrakId(evrakId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<DetayMahkemeKararTalep> findByMahkemeKararTalepId(Long mahkemeKararTalepId){
        return detayMahkemeKararTalepRepo.findByMahkemeKararTalepId(mahkemeKararTalepId);
    }


}
