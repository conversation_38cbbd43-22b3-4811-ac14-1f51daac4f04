package iym.db.jpa.service.impl.mk;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.mk.MahkemeKararGuncelleme;
import iym.common.service.db.mk.DbMahkemeKararGuncelleService;
import iym.db.jpa.dao.mk.MahkemeKararGuncelleRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;


@Service
public class DbMahkemeKararGuncelleServiceImpl extends GenericDbServiceImpl<MahkemeKararGuncelleme, Long> implements DbMahkemeKararGuncelleService {

    private final MahkemeKararGuncelleRepo mahkemeKararGuncelleRepo;

    @Autowired
    public DbMahkemeKararGuncelleServiceImpl(MahkemeKararGuncelleRepo repository) {
        super(repository);
        this.mahkemeKararGuncelleRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<MahkemeKararGuncelleme> findByDetayMahkemeKararId(Long detayMahkemeKararId) {
        return mahkemeKararGuncelleRepo.findByDetayMahkemeKararId(detayMahkemeKararId);
    }


}
