package iym.common.service.db;

import iym.common.model.entity.iym.EvrakMahkemeKararIslem;

import java.util.List;

/**
 * Service interface for EvrakMahkemeKararIslem entity
 */
public interface DbEvrakMahkemeKararIslemService extends GenericDbService<EvrakMahkemeKararIslem, Long> {

    List<EvrakMahkemeKararIslem> findByKurum(String kurum);

    
    List<EvrakMahkemeKararIslem> findByKurumAndSeviye(String kurum, String seviye);
}
