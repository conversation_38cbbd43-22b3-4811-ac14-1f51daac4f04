package iym.db.jpa.dao.mkislem;

import iym.common.model.entity.iym.mkislem.MahkemeKararGuncellemeIslem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;


@Repository
public interface MahkemeKararGuncelleIslemRepo extends JpaRepository<MahkemeKararGuncellemeIslem, Long> {
    //detayMahkemeKararIslemId
    Optional<MahkemeKararGuncellemeIslem> findByDetayMahkemeKararIslemId(Long detayMahkemeKararIslemId);

}
