-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create KULLANICI_KURUMLAR table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'KULLAN<PERSON>I_KURUMLAR';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.KULLANICI_KURUMLAR (
      KURUM_KOD NUMBER,
      KURUM_AD VARCHAR2(100 BYTE),
      KURUM VARCHAR2(10 BYTE)
    )';
  END IF;
END;
/

-- Insert sample data if table is empty
DECLARE
  row_count NUMBER;
BEGIN
  SELECT COUNT(*) INTO row_count FROM iym.KULLANICI_KURUMLAR;
  IF row_count = 0 THEN
    -- Sample data 1
    INSERT INTO iym.KULLANICI_KURUMLAR (KURUM_KOD, KURUM_AD, KURUM)
    VALUES (1, '<PERSON><PERSON> Bakanlığı', '01');
    
    -- Sample data 2
    INSERT INTO iym.KULLANICI_KURUMLAR (KURUM_KOD, KURUM_AD, KURUM)
    VALUES (2, 'EMNIYET', '02');
    
    -- Sample data 3
    INSERT INTO iym.KULLANICI_KURUMLAR (KURUM_KOD, KURUM_AD, KURUM)
    VALUES (3, 'JANDARMA', '03');
    
    -- Sample data 4
    INSERT INTO iym.KULLANICI_KURUMLAR (KURUM_KOD, KURUM_AD, KURUM)
    VALUES (4, 'IDB', '02');
    
    -- Sample data 5
    INSERT INTO iym.KULLANICI_KURUMLAR (KURUM_KOD, KURUM_AD, KURUM)
    VALUES (5, 'Sağlık Bakanlığı', '05');
  END IF;
END;
/

COMMIT;
