import { Component, Input, Output, EventEmitter, forwardRef } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MessageService } from 'primeng/api';
import { HedefTipEnum, SureTipEnum } from '../../../generated-api/model/models';

@Component({
  selector: 'app-hedef-detaylari',
  templateUrl: './hedef-detaylari.component.html',
  styleUrls: ['./hedef-detaylari.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => HedefDetaylariComponent),
      multi: true
    }
  ]
})
export class HedefDetaylariComponent implements ControlValueAccessor {
  @Input() label: string = 'Hedef Detayları';
  @Input() disabled: boolean = false;
  @Input() hedefAidiyatKodlariOptions: any[] = [];
  @Output() hedefAidiyatKodlariOptionsChange = new EventEmitter<any[]>();

  value: any[] = [];
  hedefDialogVisible: boolean = false;
  hedefForm: FormGroup;
  editingHedefIndex: number = -1;
  newHedefAidiyatKodu: string = '';

  // Enums for template
  HedefTipEnum = HedefTipEnum;
  SureTipEnum = SureTipEnum;

  private onChange = (value: any[]) => {};
  private onTouched = () => {};

  constructor(
    private fb: FormBuilder,
    private messageService: MessageService
  ) {
    this.hedefForm = this.fb.group({
      hedefNo: ['', Validators.required],
      hedefTip: [HedefTipEnum.Gsm, Validators.required],
      hedefAd: ['', Validators.required],
      hedefSoyad: ['', Validators.required],
      baslamaTarihi: [new Date(), Validators.required],
      sure: ['', [Validators.required, Validators.min(1)]],
      sureTip: [SureTipEnum.Gun, Validators.required],
      hedefAidiyatKodlari: [[]],
      canakNo: ['']
    });
  }

  writeValue(value: any[]): void {
    this.value = value || [];
  }

  registerOnChange(fn: (value: any[]) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  onValueChange(): void {
    this.onChange(this.value);
    this.onTouched();
  }

  hedefEkleDialog(): void {
    this.editingHedefIndex = -1;
    this.hedefForm.reset();
    this.hedefForm.patchValue({
      baslamaTarihi: new Date(),
      hedefTip: HedefTipEnum.Gsm,
      sureTip: SureTipEnum.Gun,
      sure: '30',
      hedefAidiyatKodlari: []
    });
    this.hedefDialogVisible = true;
  }

  hedefDuzenleDialog(index: number): void {
    this.editingHedefIndex = index;
    const hedef = this.value[index];
    this.hedefForm.patchValue({
      ...hedef,
      baslamaTarihi: hedef.baslamaTarihi ? new Date(hedef.baslamaTarihi) : new Date()
    });
    this.hedefDialogVisible = true;
  }

  hedefKaydet(): void {
    if (this.hedefForm.valid) {
      const hedefData = {
        ...this.hedefForm.value,
        baslamaTarihi: this.hedefForm.value.baslamaTarihi?.toISOString()
      };

      if (this.editingHedefIndex >= 0) {
        this.value[this.editingHedefIndex] = hedefData;
        this.messageService.add({
          severity: 'success',
          summary: 'Başarılı',
          detail: 'Hedef güncellendi.'
        });
      } else {
        this.value.push(hedefData);
        this.messageService.add({
          severity: 'success',
          summary: 'Başarılı',
          detail: 'Hedef eklendi.'
        });
      }

      this.onValueChange();
      this.hedefDialogVisible = false;
    }
  }

  hedefSil(index: number): void {
    if (confirm('Bu hedefi silmek istediğinizden emin misiniz?')) {
      this.value.splice(index, 1);
      this.onValueChange();
      this.messageService.add({
        severity: 'success',
        summary: 'Başarılı',
        detail: 'Hedef silindi.'
      });
    }
  }

  getHedefTipLabel(hedefTip: string): string {
    const labels: { [key: string]: string } = {
      'GSM': 'GSM',
      'SABIT': 'Sabit',
      'IMEI': 'IMEI',
      'IP_TAKIP': 'IP Takip'
    };
    return labels[hedefTip] || hedefTip;
  }

  getSureTipiLabel(sureTip: string): string {
    const labels: { [key: string]: string } = {
      'GUN': 'Gün',
      'AY': 'Ay',
      'YIL': 'Yıl'
    };
    return labels[sureTip] || sureTip;
  }

  formatTarih(tarih: string | Date): string {
    if (!tarih) return '';
    const date = new Date(tarih);
    return date.toLocaleDateString('tr-TR');
  }

  addNewHedefAidiyatKodu(): void {
    if (this.newHedefAidiyatKodu && this.newHedefAidiyatKodu.trim()) {
      const trimmedKod = this.newHedefAidiyatKodu.trim();

      if (!this.hedefAidiyatKodlariOptions.some(option => option.value === trimmedKod)) {
        const newOptions = [...this.hedefAidiyatKodlariOptions, {
          label: trimmedKod,
          value: trimmedKod
        }];
        this.hedefAidiyatKodlariOptions = newOptions;
        this.hedefAidiyatKodlariOptionsChange.emit(this.hedefAidiyatKodlariOptions);
      }

      const currentValues = [...(this.hedefForm.get('hedefAidiyatKodlari')?.value || [])];
      if (!currentValues.includes(trimmedKod)) {
        currentValues.push(trimmedKod);
        this.hedefForm.get('hedefAidiyatKodlari')?.setValue(currentValues);
      }

      this.newHedefAidiyatKodu = '';
    }
  }
}
