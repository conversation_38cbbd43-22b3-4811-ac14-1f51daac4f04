/**
 * IYM Backend OpenAPI definition
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { IDMahkemeKararTalepSorgulamaResponse } from './iDMahkemeKararTalepSorgulamaResponse';
import { ResponseIDYeniKararResponseException } from './responseIDYeniKararResponseException';


export interface ResponseIDMahkemeKararTalepSorgulamaResponse { 
    resultCode?: ResponseIDMahkemeKararTalepSorgulamaResponse.ResultCodeEnum;
    resultDetails?: string;
    exception?: ResponseIDYeniKararResponseException;
    result?: IDMahkemeKararTalepSorgulamaResponse;
    success?: boolean;
}
export namespace ResponseIDMahkemeKararTalepSorgulamaResponse {
    export const ResultCodeEnum = {
        Success: 'SUCCESS',
        Failed: 'FAILED',
        Rejected: 'REJECTED'
    } as const;
    export type ResultCodeEnum = typeof ResultCodeEnum[keyof typeof ResultCodeEnum];
}


