package iym.db.jpa.service.impl.mkislem;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.mkislem.HedeflerDetayIslem;
import iym.common.service.db.mkislem.DbHedeflerDetayIslemService;
import iym.db.jpa.dao.mkislem.HedeflerDetayIslemRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
public class DbHedeflerDetayIslemServiceImpl extends GenericDbServiceImpl<HedeflerDetayIslem, Long> implements DbHedeflerDetayIslemService {

    private final HedeflerDetayIslemRepo hedeflerDetayIslemRepo;

    @Autowired
    public DbHedeflerDetayIslemServiceImpl(HedeflerDetayIslemRepo repository) {
        super(repository);
        this.hedeflerDetayIslemRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public List<HedeflerDetayIslem> findByMahkemeKararIslemId(Long mahkemeKararIslemId){
        return hedeflerDetayIslemRepo.findByMahkemeKararIslemId(mahkemeKararIslemId);
    }

    @Override
    @Transactional(readOnly = true)
    public List<HedeflerDetayIslem> findByDetayMahkemeKararIslemId(Long detayMahkemeKararIslemId){
        return hedeflerDetayIslemRepo.findBydetayMahkemeKararIslemId(detayMahkemeKararIslemId);
    }


    @Override
    @Transactional(readOnly = true)
    public Optional<HedeflerDetayIslem> findHedeflerDetayIslem(Long mahkemeKararIslemId, String hedefNo, Integer hedefTipi){
        return hedeflerDetayIslemRepo.findByMahkemeKararIslemIdAndHedefNoAndHedefTipi(mahkemeKararIslemId, hedefNo, hedefTipi);
    }


}
