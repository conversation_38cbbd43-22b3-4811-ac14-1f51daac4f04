/**
 * IYM Backend OpenAPI definition
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { MahkemeKararBilgisi } from './mahkemeKararBilgisi';
import { IDHedefDetay } from './iDHedefDetay';
import { EvrakDetay } from './evrakDetay';


export interface IDYeniKararRequest { 
    id: string;
    kararTuru: IDYeniKararRequest.KararTuruEnum;
    evrakDetay: EvrakDetay;
    mahkemeKararBilgisi: MahkemeKararBilgisi;
    hedefDetayListesi: Array<IDHedefDetay>;
    mahkemeAidiyatKodlari?: Array<string>;
    mahkemeSucTipiKodlari?: Array<string>;
}
export namespace IDYeniKararRequest {
    export const KararTuruEnum = {
        IletisiminDenetlenmesiYeniKarar: 'ILETISIMIN_DENETLENMESI_YENI_KARAR',
        IletisiminDenetlenmesiUzatmaKarari: 'ILETISIMIN_DENETLENMESI_UZATMA_KARARI',
        IletisiminDenetlenmesiSonlandirmaKarari: 'ILETISIMIN_DENETLENMESI_SONLANDIRMA_KARARI',
        IletisiminDenetlenmesiAidiyatGuncelleme: 'ILETISIMIN_DENETLENMESI_AIDIYAT_GUNCELLEME',
        IletisiminDenetlenmesiHedefGuncelleme: 'ILETISIMIN_DENETLENMESI_HEDEF_GUNCELLEME',
        IletisiminDenetlenmesiMahkemekararGuncelleme: 'ILETISIMIN_DENETLENMESI_MAHKEMEKARAR_GUNCELLEME',
        IletisiminDenetlenmesiCanakGuncelleme: 'ILETISIMIN_DENETLENMESI_CANAK_GUNCELLEME',
        IletisiminTespiti: 'ILETISIMIN_TESPITI',
        GenelEvrak: 'GENEL_EVRAK',
        IletisiminDenetlenmesiSuctipiGuncelleme: 'ILETISIMIN_DENETLENMESI_SUCTIPI_GUNCELLEME'
    } as const;
    export type KararTuruEnum = typeof KararTuruEnum[keyof typeof KararTuruEnum];
}


