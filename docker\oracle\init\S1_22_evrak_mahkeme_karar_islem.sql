-- Connect as IYM user
CONNECT iym/iym@//localhost:1521/XE;

-- Create EVRAK_MAHKEME_KARAR_ISLEM table if it doesn't exist
DECLARE
  table_exists NUMBER;
BEGIN
  SELECT COUNT(*) INTO table_exists FROM user_tables WHERE table_name = 'EVRA<PERSON>_MAHKEME_KARAR_ISLEM';
  IF table_exists = 0 THEN
    EXECUTE IMMEDIATE 'CREATE TABLE iym.EVRAK_MAHKEME_KARAR_ISLEM (
      EVRAK_ID NUMBER NOT NULL,
      KURUM VARCHAR2(10 BYTE),
      SEVIYE VARCHAR2(1 BYTE) DEFAULT ''0'',
      CONSTRAINT PK_EVRAK_ID PRIMARY KEY (EVRAK_ID) ENABLE
    )';
  END IF;
END;
/


COMMIT;
