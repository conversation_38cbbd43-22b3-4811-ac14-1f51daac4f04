package iym.db.jpa.service.impl.mktalep;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.iym.talep.MahkemeAidiyatDetayTalep;
import iym.common.service.db.mktalep.DbMahkemeAidiyatDetayTalepService;
import iym.db.jpa.dao.mktalep.MahkemeAidiyatDetayTalepRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Service implementation for MahkemeAidiyatDetayTalep entity
 */
@Service
public class DbMahkemeAidiyatDetayTalepServiceImpl extends GenericDbServiceImpl<MahkemeAidiyatDetayTalep, Long> implements DbMahkemeAidiyatDetayTalepService {

    private final MahkemeAidiyatDetayTalepRepo mahkemeAidiyatDetayTalepRepo;

    @Autowired
    public DbMahkemeAidiyatDetayTalepServiceImpl(MahkemeAidiyatDetayTalepRepo repository) {
        super(repository);
        this.mahkemeAidiyatDetayTalepRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public List<MahkemeAidiyatDetayTalep> findByMahkemeKararId(Long mahkemeKararId) {
        return mahkemeAidiyatDetayTalepRepo.findByMahkemeKararTalepId(mahkemeKararId);
    }



    @Override
    @Transactional(readOnly = true)
    public List<MahkemeAidiyatDetayTalep> findByMahkemeKararDetayTalepId(Long mahkemeKararDetayTalepId) {
        return mahkemeAidiyatDetayTalepRepo.findByMahkemeKararDetayTalepId(mahkemeKararDetayTalepId);
    }



}
