package iym.db.jpa.service.impl;

import iym.common.db.impl.GenericDbServiceImpl;
import iym.common.model.entity.makos.log.MakosKararRequestLog;
import iym.common.service.db.DbMakosKararRequestLogService;
import iym.db.jpa.dao.MakosKararRequestLogRepo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;


/**
 * Service implementation for MakosKararRequestLog entity
 * Provides business logic methods for MAKOS court decision request logging
 */
@Service
public class DbMakosKararRequestLogServiceImpl extends GenericDbServiceImpl<MakosKararRequestLog, UUID> implements DbMakosKararRequestLogService {

    private final MakosKararRequestLogRepo makosKararRequestLogRepo;

    @Autowired
    public DbMakosKararRequestLogServiceImpl(MakosKararRequestLogRepo repository) {
        super(repository);
        this.makosKararRequestLogRepo = repository;
    }

    @Override
    @Transactional(readOnly = true)
    public List<MakosKararRequestLog> findByUsername(String username) {
        return makosKararRequestLogRepo.findByUsername(username);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MakosKararRequestLog> findByActingUsername(String actingUsername) {
        return makosKararRequestLogRepo.findByActingUsername(actingUsername);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MakosKararRequestLog> findByUserIp(String userIp) {
        return makosKararRequestLogRepo.findByUserIp(userIp);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MakosKararRequestLog> findByMahkemeKararNo(String mahkemeKararNo) {
        return makosKararRequestLogRepo.findByMahkemeKararNo(mahkemeKararNo);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MakosKararRequestLog> findBySorusturmaNo(String sorusturmaNo) {
        return makosKararRequestLogRepo.findBySorusturmaNo(sorusturmaNo);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MakosKararRequestLog> findByRequestTimeBetween(LocalDateTime startTime, LocalDateTime endTime) {
        return makosKararRequestLogRepo.findByRequestTimeBetween(startTime, endTime);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MakosKararRequestLog> findByUsernameAndRequestTimeBetween(String username, LocalDateTime startTime, LocalDateTime endTime) {
        return makosKararRequestLogRepo.findByUsernameAndRequestTimeBetween(username, startTime, endTime);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MakosKararRequestLog> findByMahkemeKararNoAndRequestTimeBetween(String mahkemeKararNo, LocalDateTime startTime, LocalDateTime endTime) {
        return makosKararRequestLogRepo.findByMahkemeKararNoAndRequestTimeBetween(mahkemeKararNo, startTime, endTime);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MakosKararRequestLog> findByResponseCode(Integer responseCode) {
        return makosKararRequestLogRepo.findByResponseCode(responseCode);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MakosKararRequestLog> findAllByOrderByRequestTimeDesc() {
        return makosKararRequestLogRepo.findAllByOrderByRequestTimeDesc();
    }

    @Override
    @Transactional(readOnly = true)
    public List<MakosKararRequestLog> findByUsernameOrderByRequestTimeDesc(String username) {
        return makosKararRequestLogRepo.findByUsernameOrderByRequestTimeDesc(username);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MakosKararRequestLog> findByMahkemeKararNoOrderByRequestTimeDesc(String mahkemeKararNo) {
        return makosKararRequestLogRepo.findByMahkemeKararNoOrderByRequestTimeDesc(mahkemeKararNo);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MakosKararRequestLog> findByRequestURLContaining(String urlPattern) {
        return makosKararRequestLogRepo.findByRequestURLContaining(urlPattern);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MakosKararRequestLog> findByRequestId(UUID requestId) {
        return makosKararRequestLogRepo.findByRequestId(requestId);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<MakosKararRequestLog> findAll(Specification<MakosKararRequestLog> spec, Pageable pageable) {
        return makosKararRequestLogRepo.findAll(spec, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MakosKararRequestLog> findByEvrakNo(String evrakNo) {
        return makosKararRequestLogRepo.findByEvrakNo(evrakNo);
    }

    @Override
    @Transactional(readOnly = true)
    public List<MakosKararRequestLog> findByEvrakNoOrderByRequestTimeDesc(String evrakNo) {
        return makosKararRequestLogRepo.findByEvrakNoOrderByRequestTimeDesc(evrakNo);
    }
}
