### Health Check Test File for MAKOS Module
### Base URL: http://localhost:5000/makosapi

### 1. Test Anonymous Health Check Endpoint
### This should return 200 OK without authentication
GET http://localhost:5000/makosapi/check/healthCheck
Accept: application/json

### Expected Response:
### {
###   "response": {
###     "responseCode": "SUCCESS",
###     "responseMessage": "Makos API is alive"
###   }
### }

### 2. Test Health Check with Basic Auth (Valid Credentials)
### Using makos_admin:123456 as specified
GET http://localhost:5000/makosapi/check/healthCheckAuthorized
Accept: application/json
Authorization: Basic bWFrb3NfYWRtaW46MTIzNDU2

### Expected Response:
### {
###   "response": {
###     "responseCode": "SUCCESS",
###     "responseMessage": "Makos API is alive"
###   }
### }

### 3. Test Health Check with Basic Auth (Invalid Credentials)
### This should return 401 Unauthorized
GET http://localhost:5000/makosapi/check/healthCheckAuthorized
Accept: application/json
Authorization: Basic invalid_user:invalid_pass

### Expected Response: 401 Unauthorized

### 4. Test Health Check Admin (Requires ADMIN role)
### This should work with makos_admin:123456 since makos_admin has ADMIN role
GET http://localhost:5000/makosapi/check/healthCheckAdmin
Accept: application/json
Authorization: Basic bWFrb3NfYWRtaW46MTIzNDU2

### Expected Response:
### {
###   "response": {
###     "responseCode": "SUCCESS",
###     "responseMessage": "Makos API is alive"
###   }
### }

### Environment Variables for IntelliJ HTTP Client
### You can also use environment variables:
### @baseUrl = http://localhost:5000/makosapi
### @username = makos_admin
### @password = 123456
###
### Then use: GET {{baseUrl}}/check/healthCheckAuthorized
### With: Authorization: Basic {{username}}:{{password}}

### Testing via Backend Module (Port 4000)
### The backend module also exposes these endpoints via proxy:

### 5. Test via Backend - Anonymous Health Check
GET http://localhost:4000/api/makos/health
Accept: application/json

### 6. Test via Backend - Authorized Health Check
GET http://localhost:4000/api/makos/healthCheckAuthorized
Accept: application/json
Authorization: Basic bWFrb3NfYWRtaW46MTIzNDU2